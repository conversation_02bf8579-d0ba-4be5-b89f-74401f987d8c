# Faz 1: Database & Models (1-2 gün)

## 📋 Faz Özeti
Database şeması oluşturma, entity modelleri ve migration'lar. Mevcut Wallet tablosuna LOCKED_BALANCE kolonu ekleme ve yeni RZW Savings tabloları oluşturma.

## 🎯 Hedefler
- ✅ Wallet tablosuna LOCKED_BALANCE kolonu ekleme
- ✅ RZW Savings entity'leri oluşturma
- ✅ Migration'lar hazırlama
- ✅ Entity konfigürasyonları
- ✅ Enum'lar ve constants

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### 1.1 Wallet Entity Güncelleme
**Dosya**: `src/Areas/Admin/DbModel/Wallet.cs`
```csharp
// LOCKED_BALANCE kolonu ekleme
[Required]
[Column("LOCKED_BALANCE", TypeName = "decimal(20,8)")]
public decimal LockedBalance { get; set; } = 0;

// Hesaplanmış alanlar
[NotMapped]
public decimal TotalBalance => Balance + LockedBalance;

[NotMapped]
public decimal AvailableBalance => Balance;
```

#### 1.2 RZW Savings Entities Oluşturma

**Dosya**: `src/Areas/Admin/DbModel/RzwSavingsAccount.cs`
```csharp
[Table("RZW_SAVINGS_ACCOUNT")]
public class RzwSavingsAccount
{
    [Key]
    [Column("ID")]
    public int Id { get; set; }

    [Required]
    [Column("USER_ID")]
    public int UserId { get; set; }

    [Required]
    [Column("RZW_AMOUNT", TypeName = "decimal(20,8)")]
    public decimal RzwAmount { get; set; }

    [Required]
    [Column("INTEREST_RATE", TypeName = "decimal(5,4)")]
    public decimal InterestRate { get; set; }

    [Required]
    [Column("TERM_TYPE")]
    [StringLength(20)]
    public string TermType { get; set; } = null!;

    [Required]
    [Column("TERM_DURATION")]
    public int TermDuration { get; set; }

    [Required]
    [Column("START_DATE", TypeName = "datetime")]
    public DateTime StartDate { get; set; }

    [Required]
    [Column("MATURITY_DATE", TypeName = "datetime")]
    public DateTime MaturityDate { get; set; }

    [Required]
    [Column("STATUS")]
    [StringLength(20)]
    public string Status { get; set; } = RzwSavingsStatus.Active;

    [Column("TOTAL_EARNED_RZW", TypeName = "decimal(20,8)")]
    public decimal TotalEarnedRzw { get; set; } = 0;

    [Column("LAST_INTEREST_DATE", TypeName = "datetime")]
    public DateTime? LastInterestDate { get; set; }

    [Column("AUTO_RENEW")]
    public bool AutoRenew { get; set; } = false;

    [Column("EARLY_WITHDRAWAL_PENALTY", TypeName = "decimal(5,4)")]
    public decimal EarlyWithdrawalPenalty { get; set; } = 0.10m;

    [Required]
    [Column("CREATED_DATE", TypeName = "datetime")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Column("MODIFIED_DATE", TypeName = "datetime")]
    public DateTime? ModifiedDate { get; set; }

    // Navigation properties
    [ForeignKey("UserId")]
    public virtual User User { get; set; } = null!;

    [InverseProperty("RzwSavingsAccount")]
    public virtual ICollection<RzwSavingsInterestPayment> InterestPayments { get; set; } = new List<RzwSavingsInterestPayment>();
}
```

**Dosya**: `src/Areas/Admin/DbModel/RzwSavingsInterestPayment.cs`
```csharp
[Table("RZW_SAVINGS_INTEREST_PAYMENT")]
public class RzwSavingsInterestPayment
{
    [Key]
    [Column("ID")]
    public int Id { get; set; }

    [Required]
    [Column("RZW_SAVINGS_ACCOUNT_ID")]
    public int RzwSavingsAccountId { get; set; }

    [Required]
    [Column("RZW_AMOUNT", TypeName = "decimal(20,8)")]
    public decimal RzwAmount { get; set; }

    [Required]
    [Column("PAYMENT_DATE", TypeName = "datetime")]
    public DateTime PaymentDate { get; set; }

    [Column("WALLET_TRANSACTION_ID")]
    public int? WalletTransactionId { get; set; }

    [Column("DESCRIPTION")]
    [StringLength(500)]
    public string? Description { get; set; }

    [Required]
    [Column("CREATED_DATE", TypeName = "datetime")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    // Navigation properties
    [ForeignKey("RzwSavingsAccountId")]
    public virtual RzwSavingsAccount RzwSavingsAccount { get; set; } = null!;
}
```

**Dosya**: `src/Areas/Admin/DbModel/RzwSavingsPlan.cs`
```csharp
[Table("RZW_SAVINGS_PLAN")]
public class RzwSavingsPlan
{
    [Key]
    [Column("ID")]
    public int Id { get; set; }

    [Required]
    [Column("NAME")]
    [StringLength(100)]
    public string Name { get; set; } = null!;

    [Required]
    [Column("TERM_TYPE")]
    [StringLength(20)]
    public string TermType { get; set; } = null!;

    [Required]
    [Column("TERM_DURATION")]
    public int TermDuration { get; set; }

    [Required]
    [Column("INTEREST_RATE", TypeName = "decimal(5,4)")]
    public decimal InterestRate { get; set; }

    [Required]
    [Column("MIN_RZW_AMOUNT", TypeName = "decimal(20,8)")]
    public decimal MinRzwAmount { get; set; }

    [Column("MAX_RZW_AMOUNT", TypeName = "decimal(20,8)")]
    public decimal? MaxRzwAmount { get; set; }

    [Required]
    [Column("IS_ACTIVE")]
    public bool IsActive { get; set; } = true;

    [Column("DISPLAY_ORDER")]
    public int DisplayOrder { get; set; } = 0;

    [Column("DESCRIPTION")]
    [StringLength(500)]
    public string? Description { get; set; }

    [Required]
    [Column("CREATED_DATE", TypeName = "datetime")]
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

    [Column("MODIFIED_DATE", TypeName = "datetime")]
    public DateTime? ModifiedDate { get; set; }
}
```

#### 1.3 Enums ve Constants

**Dosya**: `src/Areas/Admin/DbModel/RzwSavingsEnums.cs`
```csharp
public static class RzwSavingsStatus
{
    public const string Active = "Active";
    public const string Matured = "Matured";
    public const string Withdrawn = "Withdrawn";
    public const string Cancelled = "Cancelled";
}

public static class RzwSavingsTermType
{
    public const string Daily = "Daily";
    public const string Monthly = "Monthly";
    public const string Yearly = "Yearly";
}

public static class RzwSavingsConstants
{
    public const decimal DEFAULT_EARLY_WITHDRAWAL_PENALTY = 0.10m; // %10
    public const int DAILY_TERM_DURATION = 1;
    public const int MONTHLY_TERM_DURATION = 30;
    public const int YEARLY_TERM_DURATION = 365;
}
```

#### 1.4 Transaction Types Güncelleme

**Dosya**: `src/Areas/Admin/DbModel/BalanceTransaction.cs`
```csharp
public static class TransactionType
{
    // Mevcut types...
    public const string Deposit = "Deposit";
    public const string Withdrawal = "Withdrawal";
    // ... diğer mevcut types

    // YENİ: RZW Savings types
    public const string RzwSavingsDeposit = "RZW Savings Deposit";
    public const string RzwSavingsWithdrawal = "RZW Savings Withdrawal";
    public const string RzwSavingsInterest = "RZW Savings Interest";
    public const string RzwSavingsEarlyWithdrawal = "RZW Savings Early Withdrawal";
    public const string RzwSavingsMaturity = "RZW Savings Maturity";

    // YENİ: RZW Savings için TradeType enum'a eklenecek
}

#### 1.5 TradeType Enum Güncelleme

**Dosya**: `src/Areas/Admin/DbModel/Trade.cs`
```csharp
public enum TradeType
{
    Buy,                    // alis
    Sell,                   // satis
    PackageBonus,           // paket satın alımında verilen bonus
    ReferralReward,         // yönlendirme ödülü

    // YENİ: RZW Savings types
    RzwSavingsDeposit,      // vadeli hesaba yatırma
    RzwSavingsWithdrawal,   // vadeli hesaptan çekme
    RzwSavingsInterest,     // faiz ödemesi
    RzwSavingsEarlyWithdrawal, // erken çekim
    RzwSavingsMaturity      // vade dolma
}
```

#### 1.8 AppDbContext Güncelleme

**Dosya**: `src/Areas/Admin/DbModel/AppDbContext.cs`
```csharp
public class AppDbContext : DbContext
{
    // Mevcut DbSet'ler...

    // YENİ: RZW Savings DbSet'ler
    public virtual DbSet<RzwSavingsAccount> RzwSavingsAccounts { get; set; }
    public virtual DbSet<RzwSavingsInterestPayment> RzwSavingsInterestPayments { get; set; }
    public virtual DbSet<RzwSavingsPlan> RzwSavingsPlans { get; set; }
}
```

#### 1.6 RZW Savings Account ID Field Ekleme

**Dosya**: `src/Areas/Admin/DbModel/Trade.cs` (güncelleme)
```csharp
[Table("TRADE")]
public class Trade
{
    // Mevcut alanlar...

    [Column("REFERRAL_REWARD_ID")]
    public int? ReferralRewardId { get; set; }

    // YENİ: RZW Savings Account referansı
    [Column("RZW_SAVINGS_ACCOUNT_ID")]
    public int? RzwSavingsAccountId { get; set; }

    // Navigation properties...
    [ForeignKey("ReferralRewardId")]
    public virtual ReferralReward? ReferralReward { get; set; }

    // YENİ: RZW Savings Account navigation
    [ForeignKey("RzwSavingsAccountId")]
    public virtual RzwSavingsAccount? RzwSavingsAccount { get; set; }
}
```

#### 1.7 Migration Oluşturma

**Dosya**: `src/Migrations/AddRzwSavingsSupport.cs`
```csharp
public partial class AddRzwSavingsSupport : Migration
{
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        // 1. WALLET tablosuna LOCKED_BALANCE kolonu ekle
        migrationBuilder.AddColumn<decimal>(
            name: "LOCKED_BALANCE",
            table: "WALLET",
            type: "decimal(20,8)",
            nullable: false,
            defaultValue: 0m);

        // 2. RZW_SAVINGS_PLAN tablosu oluştur
        migrationBuilder.CreateTable(
            name: "RZW_SAVINGS_PLAN",
            columns: table => new
            {
                ID = table.Column<int>(type: "INTEGER", nullable: false)
                    .Annotation("Sqlite:Autoincrement", true),
                NAME = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                TERM_TYPE = table.Column<string>(type: "TEXT", maxLength: 20, nullable: false),
                TERM_DURATION = table.Column<int>(type: "INTEGER", nullable: false),
                INTEREST_RATE = table.Column<decimal>(type: "decimal(5,4)", nullable: false),
                MIN_RZW_AMOUNT = table.Column<decimal>(type: "decimal(20,8)", nullable: false),
                MAX_RZW_AMOUNT = table.Column<decimal>(type: "decimal(20,8)", nullable: true),
                IS_ACTIVE = table.Column<bool>(type: "INTEGER", nullable: false),
                DISPLAY_ORDER = table.Column<int>(type: "INTEGER", nullable: false),
                DESCRIPTION = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                CREATED_DATE = table.Column<DateTime>(type: "datetime", nullable: false),
                MODIFIED_DATE = table.Column<DateTime>(type: "datetime", nullable: true)
            },
            constraints: table =>
            {
                table.PrimaryKey("PK_RZW_SAVINGS_PLAN", x => x.ID);
            });

        // 3. RZW_SAVINGS_ACCOUNT tablosu oluştur
        // ... (devamı migration'da)

        // 4. TRADE tablosuna RZW_SAVINGS_ACCOUNT_ID kolonu ekle
        migrationBuilder.AddColumn<int>(
            name: "RZW_SAVINGS_ACCOUNT_ID",
            table: "TRADE",
            type: "INTEGER",
            nullable: true);

        // 5. Index'ler ekle
        migrationBuilder.CreateIndex(
            name: "IX_WALLET_USER_LOCKED_BALANCE",
            table: "WALLET",
            columns: new[] { "USER_ID", "LOCKED_BALANCE" });

        migrationBuilder.CreateIndex(
            name: "IX_TRADE_RZW_SAVINGS_ACCOUNT_ID",
            table: "TRADE",
            column: "RZW_SAVINGS_ACCOUNT_ID");
    }
}
```

## 📋 Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Wallet.cs entity güncelleme
- [ ] RzwSavingsAccount.cs oluşturma
- [ ] RzwSavingsInterestPayment.cs oluşturma
- [ ] RzwSavingsPlan.cs oluşturma
- [ ] RzwSavingsEnums.cs oluşturma
- [ ] TransactionType güncelleme (BalanceTransaction için)
- [ ] TradeType enum güncelleme (RZW Savings types ekleme)
- [ ] Trade.cs entity güncelleme (RzwSavingsAccountId field ekleme)
- [ ] AppDbContext güncelleme
- [ ] Migration oluşturma (LOCKED_BALANCE + RZW_SAVINGS_ACCOUNT_ID)
- [ ] Index'ler ekleme
- [ ] Initial data seeding

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🧪 Test Kriterleri

### Database Tests
- [ ] Migration başarıyla çalışıyor
- [ ] Yeni tablolar oluşturuluyor
- [ ] LOCKED_BALANCE kolonu ekleniyor
- [ ] Index'ler doğru oluşturuluyor
- [ ] Foreign key'ler çalışıyor

### Entity Tests
- [ ] Entity property'leri doğru mapping
- [ ] Navigation property'ler çalışıyor
- [ ] Validation attribute'ları çalışıyor
- [ ] Hesaplanmış alanlar doğru

## 📝 Notlar

### Önemli Kararlar
- BALANCE kolonu mevcut haliyle kalacak (Available Balance)
- LOCKED_BALANCE yeni kolon olarak eklenecek
- TOTAL_BALANCE hesaplanmış alan olacak
- RZW Token ID dinamik olarak TokenPriceService'den alınacak (sabit değer kullanılmayacak)

### Sonraki Faz
Bu faz tamamlandıktan sonra **Faz 2: Balance Management** başlayacak.

---
**Tahmini Süre**: 1-2 gün
**Öncelik**: Yüksek
**Bağımlılıklar**: Yok

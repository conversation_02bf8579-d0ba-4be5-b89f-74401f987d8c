# Faz 5.3: <PERSON><PERSON> (1 gün)

## 📋 Alt Faz Özeti
MyAccount/RzwSavings/Create sayfası oluşturma. Kullanıcıların yeni vadeli hesap a<PERSON>bilecekleri, plan seçimi yapabilecekleri ve miktar girebilecekleri form sayfası.

## 🎯 Hedefler
- ✅ Create.cshtml form sayfası oluşturma
- ✅ RzwSavingsCreateModel oluşturma
- ✅ Plan seçimi interface'i
- ✅ Miktar girişi ve hesaplama
- ✅ Form validation ve submission

## 📊 Bu Alt Faz 4 Küçük Adıma Bölünmüştür

### **Adım 5.3.1**: PageModel ve Form Structure → `RZW_SAVINGS_PHASE_5_3_1.md`
- CreateModel PageModel oluşturma
- Form ViewModel'leri
- Validation rules

### **Adım 5.3.2**: Plan Selection Interface → `RZW_SAVINGS_PHASE_5_3_2.md`
- Plan seçimi kartları
- <PERSON> ka<PERSON>tırma
- Interactive plan selection

### **Adım 5.3.3**: Amount Input ve Calculation → `RZW_SAVINGS_PHASE_5_3_3.md`
- Miktar girişi formu
- Real-time hesaplamalar
- Min/max validation

### **Adım 5.3.4**: Confirmation ve Submission → `RZW_SAVINGS_PHASE_5_3_4.md`
- Özet gösterimi
- Confirmation modal
- Form submission handling

## 📋 Genel Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] **Adım 5.3.1**: PageModel ve form structure
- [ ] **Adım 5.3.2**: Plan selection interface
- [ ] **Adım 5.3.3**: Amount input ve calculation
- [ ] **Adım 5.3.4**: Confirmation ve submission

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🎨 Sayfa Tasarım Konsepti

### Layout Yapısı
```
┌─────────────────────────────────────────┐
│ Page Header + Breadcrumb                │
├─────────────────────────────────────────┤
│ User Balance Summary                    │
├─────────────────────────────────────────┤
│ Step 1: Plan Selection                  │
│ [Daily] [Monthly] [Yearly] Cards        │
├─────────────────────────────────────────┤
│ Step 2: Amount Input                    │
│ [Amount Input] [Slider] [Calculation]   │
├─────────────────────────────────────────┤
│ Step 3: Summary & Confirmation          │
│ [Summary Card] [Terms] [Submit]         │
└─────────────────────────────────────────┘
```

### Form Flow
1. **Plan Selection**: Kullanıcı vade türü seçer
2. **Amount Input**: RZW miktarı girer
3. **Real-time Calculation**: Faiz hesaplaması gösterilir
4. **Confirmation**: Özet ve onay
5. **Submission**: Vadeli hesap oluşturulur

### Renk Şeması
- **Selected Plan**: RZW gradient (#667eea → #764ba2)
- **Available Plans**: Light gray border
- **Amount Input**: Primary blue focus
- **Calculation Results**: Success green

## 🔧 Teknik Gereksinimler

### ViewModels
```csharp
public class RzwSavingsCreateModel
{
    public List<RzwSavingsPlanOption> AvailablePlans { get; set; }
    public RzwBalanceInfo UserBalance { get; set; }
    public CreateSavingsForm Form { get; set; }
    public SavingsCalculation Calculation { get; set; }
}

public class CreateSavingsForm
{
    public int SelectedPlanId { get; set; }
    public decimal Amount { get; set; }
    public bool AutoRenew { get; set; }
    public bool AcceptTerms { get; set; }
}

public class SavingsCalculation
{
    public decimal DailyInterest { get; set; }
    public decimal MonthlyInterest { get; set; }
    public decimal TotalInterest { get; set; }
    public decimal MaturityAmount { get; set; }
    public DateTime MaturityDate { get; set; }
}
```

### JavaScript Fonksiyonları
- Plan selection handling
- Real-time amount calculation
- Form validation
- Slider input handling
- AJAX form submission

### Validation Rules
- Plan selection required
- Amount min/max validation
- Available balance check
- Terms acceptance required

## 📱 Mobile Optimizasyonu

### Responsive Design
- Plan cards stack vertically
- Amount input with number pad
- Touch-friendly sliders
- Simplified calculation display

### User Experience
- Step-by-step wizard
- Progress indicator
- Clear error messages
- Success feedback

## 🧪 Test Kriterleri

### Form Tests
- [ ] Plan selection çalışıyor
- [ ] Amount validation çalışıyor
- [ ] Real-time calculation doğru
- [ ] Form submission başarılı

### UX Tests
- [ ] Responsive design tüm cihazlarda çalışıyor
- [ ] Error handling uygun
- [ ] Loading states doğru
- [ ] Success feedback çalışıyor

## 📝 Notlar

### Önemli Özellikler
- Multi-step form wizard
- Real-time calculations
- Interactive plan comparison
- Mobile-optimized input
- Comprehensive validation

### Hesaplama Formülleri
- **Daily Interest**: Amount × Rate
- **Monthly Interest**: Daily × 30 (or Monthly Rate)
- **Total Interest**: Based on term duration
- **Maturity Amount**: Principal + Total Interest

### Error Scenarios
- Insufficient balance
- Invalid amount
- Plan not available
- Network errors

### Sonraki Adım
Bu alt faz tamamlandıktan sonra **Faz 5.4: Vadeli Hesap Detay Sayfası** başlayacak.

---
**Tahmini Süre**: 1 gün (4 küçük adım)
**Öncelik**: Yüksek
**Bağımlılıklar**: Faz 5.2 tamamlanmış olmalı

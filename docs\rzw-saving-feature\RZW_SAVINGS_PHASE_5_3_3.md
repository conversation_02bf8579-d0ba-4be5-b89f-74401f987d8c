# Adım 5.3.3: Amount Input ve Calculation (2-3 saat)

## 📋 Adım Özeti
Yeni vadeli hesap sayfasında miktar girişi formu, real-time hesaplamalar ve min/max validation'ın implementasyonu.

## 🎯 Hedefler
- ✅ Amount input formu oluşturma
- ✅ Real-time interest calculation
- ✅ Min/max validation
- ✅ Slider input component
- ✅ Calculation results display

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### 5.3.3.1 Amount Input Section HTML

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Create.cshtml` (Step 2 ekleme)
```html
<!-- Step 2: Amount Input (Create.cshtml'e ekleme) -->
<div class="form-step" id="step-amount-input" style="display: none;">
    <div class="step-header">
        <div class="step-number">
            <span class="step-badge">2</span>
        </div>
        <div class="step-content">
            <h5 class="step-title">@Localizer["Enter Investment Amount"]</h5>
            <p class="step-description">@Localizer["Choose how much RZW you want to invest"]</p>
        </div>
    </div>

    <!-- Selected Plan Summary -->
    <div class="selected-plan-summary" id="selectedPlanSummary" style="display: none;">
        <div class="summary-card">
            <div class="summary-header">
                <h6 class="summary-title">
                    <i class="fas fa-check-circle text-success"></i>
                    @Localizer["Selected Plan"]
                </h6>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="changePlan()">
                    @Localizer["Change"]
                </button>
            </div>
            <div class="summary-content">
                <div class="plan-info">
                    <span class="plan-name" id="selectedPlanName"></span>
                    <span class="plan-rate" id="selectedPlanRate"></span>
                </div>
                <div class="plan-limits">
                    <small class="text-muted">
                        @Localizer["Range"]: <span id="selectedPlanRange"></span>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Amount Input Section -->
    <div class="amount-input-section">
        <div class="row">
            <div class="col-lg-6">
                <!-- Amount Input Form -->
                <div class="amount-input-card">
                    <div class="input-header">
                        <label for="amountInput" class="form-label">
                            @Localizer["Investment Amount"]
                        </label>
                        <div class="input-actions">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="setMaxAmount()">
                                @Localizer["Max"]
                            </button>
                        </div>
                    </div>

                    <div class="amount-input-group">
                        <div class="input-group input-group-lg">
                            <input type="number" 
                                   class="form-control amount-input" 
                                   id="amountInput"
                                   asp-for="Form.Amount"
                                   placeholder="0.00000000"
                                   step="0.00000001"
                                   min="0"
                                   autocomplete="off">
                            <span class="input-group-text">RZW</span>
                        </div>
                        <div class="amount-validation">
                            <span asp-validation-for="Form.Amount" class="text-danger"></span>
                            <div id="amountValidationMessage" class="validation-message"></div>
                        </div>
                    </div>

                    <!-- Quick Amount Buttons -->
                    <div class="quick-amounts">
                        <div class="quick-amounts-header">
                            <small class="text-muted">@Localizer["Quick Select"]:</small>
                        </div>
                        <div class="quick-amounts-buttons">
                            <button type="button" class="btn btn-sm btn-outline-secondary quick-amount-btn" data-percentage="25">
                                25%
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary quick-amount-btn" data-percentage="50">
                                50%
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary quick-amount-btn" data-percentage="75">
                                75%
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary quick-amount-btn" data-percentage="100">
                                100%
                            </button>
                        </div>
                    </div>

                    <!-- Amount Slider -->
                    <div class="amount-slider-section">
                        <label for="amountSlider" class="form-label">
                            @Localizer["Adjust with Slider"]
                        </label>
                        <div class="slider-container">
                            <input type="range" 
                                   class="form-range amount-slider" 
                                   id="amountSlider"
                                   min="0" 
                                   max="1000000" 
                                   step="1"
                                   value="0">
                            <div class="slider-labels">
                                <span class="slider-min" id="sliderMin">0</span>
                                <span class="slider-max" id="sliderMax">@Model.ViewModel.UserBalance.AvailableBalance.ToString("N0")</span>
                            </div>
                        </div>
                    </div>

                    <!-- Balance Information -->
                    <div class="balance-info-section">
                        <div class="balance-row">
                            <span class="balance-label">@Localizer["Available Balance"]:</span>
                            <span class="balance-value" data-balance="available">
                                @Model.ViewModel.UserBalance.AvailableBalance.ToString("N8") RZW
                            </span>
                        </div>
                        <div class="balance-row">
                            <span class="balance-label">@Localizer["After Investment"]:</span>
                            <span class="balance-value" id="remainingBalance">
                                @Model.ViewModel.UserBalance.AvailableBalance.ToString("N8") RZW
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <!-- Calculation Results -->
                <div class="calculation-results-card">
                    <div class="results-header">
                        <h6 class="results-title">
                            <i class="fas fa-calculator text-primary"></i>
                            @Localizer["Investment Projection"]
                        </h6>
                    </div>

                    <div class="calculation-content" id="calculationResults" style="display: none;">
                        <!-- Principal Amount -->
                        <div class="calculation-row principal">
                            <div class="calc-label">
                                <i class="fas fa-coins"></i>
                                @Localizer["Principal Amount"]
                            </div>
                            <div class="calc-value" id="calcPrincipal">0.00000000 RZW</div>
                        </div>

                        <!-- Interest Breakdown -->
                        <div class="interest-breakdown">
                            <div class="breakdown-header">
                                <h6>@Localizer["Interest Breakdown"]</h6>
                            </div>
                            
                            <div class="calculation-row">
                                <div class="calc-label">
                                    <i class="fas fa-calendar-day"></i>
                                    @Localizer["Daily Interest"]
                                </div>
                                <div class="calc-value" id="calcDailyInterest">0.00000000 RZW</div>
                            </div>

                            <div class="calculation-row">
                                <div class="calc-label">
                                    <i class="fas fa-calendar-alt"></i>
                                    @Localizer["Monthly Interest"]
                                </div>
                                <div class="calc-value" id="calcMonthlyInterest">0.00000000 RZW</div>
                            </div>

                            <div class="calculation-row total">
                                <div class="calc-label">
                                    <i class="fas fa-chart-line"></i>
                                    @Localizer["Total Interest"]
                                </div>
                                <div class="calc-value" id="calcTotalInterest">0.00000000 RZW</div>
                            </div>
                        </div>

                        <!-- Maturity Information -->
                        <div class="maturity-info">
                            <div class="maturity-header">
                                <h6>@Localizer["Maturity Information"]</h6>
                            </div>
                            
                            <div class="calculation-row maturity">
                                <div class="calc-label">
                                    <i class="fas fa-calendar-check"></i>
                                    @Localizer["Maturity Date"]
                                </div>
                                <div class="calc-value" id="calcMaturityDate">-</div>
                            </div>

                            <div class="calculation-row maturity-amount">
                                <div class="calc-label">
                                    <i class="fas fa-money-bill-wave"></i>
                                    @Localizer["Maturity Amount"]
                                </div>
                                <div class="calc-value highlight" id="calcMaturityAmount">0.00000000 RZW</div>
                            </div>
                        </div>

                        <!-- ROI Information -->
                        <div class="roi-info">
                            <div class="roi-card">
                                <div class="roi-header">
                                    <span class="roi-label">@Localizer["Return on Investment"]</span>
                                </div>
                                <div class="roi-value" id="calcROI">0.00%</div>
                                <div class="roi-apy">
                                    <small>APY: <span id="calcAPY">0.00%</span></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- No Calculation State -->
                    <div class="no-calculation-state" id="noCalculationState">
                        <div class="no-calc-icon">
                            <i class="fas fa-calculator fa-2x text-muted"></i>
                        </div>
                        <div class="no-calc-text">
                            <p class="text-muted">@Localizer["Enter an amount to see your investment projection"]</p>
                        </div>
                    </div>

                    <!-- Calculation Loading State -->
                    <div class="calculation-loading" id="calculationLoading" style="display: none;">
                        <div class="loading-spinner">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">@Localizer["Calculating..."]</span>
                            </div>
                            <span class="loading-text">@Localizer["Calculating..."]</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Step Navigation -->
    <div class="step-navigation">
        <button type="button" class="btn btn-outline-secondary" onclick="previousStep()">
            <i class="fas fa-arrow-left"></i> @Localizer["Previous"]
        </button>
        <button type="button" class="btn btn-primary" id="nextToConfirmation" onclick="nextStep()" disabled>
            @Localizer["Next"] <i class="fas fa-arrow-right"></i>
        </button>
    </div>
</div>
```

#### 5.3.3.2 Amount Input CSS

**Dosya**: `src/wwwroot/css/rzw-savings-create.css` (Amount input styles ekleme)
```css
/* Amount Input Section */
.amount-input-section {
    margin-bottom: 30px;
}

/* Selected Plan Summary */
.selected-plan-summary {
    margin-bottom: 25px;
}

.summary-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 15px;
}

.summary-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.summary-title {
    margin: 0;
    color: #495057;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.plan-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.plan-name {
    font-weight: 600;
    color: #2c3e50;
}

.plan-rate {
    color: #28a745;
    font-weight: 500;
}

/* Amount Input Card */
.amount-input-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.input-header .form-label {
    margin: 0;
    font-weight: 600;
    color: #2c3e50;
}

.amount-input-group {
    margin-bottom: 20px;
}

.amount-input {
    font-family: 'Courier New', monospace;
    font-size: 1.2rem;
    font-weight: 600;
    text-align: right;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.amount-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.amount-input.is-invalid {
    border-color: #dc3545;
}

.amount-input.is-valid {
    border-color: #28a745;
}

.amount-validation {
    margin-top: 8px;
}

.validation-message {
    font-size: 0.875rem;
    margin-top: 5px;
}

.validation-message.text-danger {
    color: #dc3545;
}

.validation-message.text-success {
    color: #28a745;
}

/* Quick Amounts */
.quick-amounts {
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.quick-amounts-header {
    margin-bottom: 10px;
}

.quick-amounts-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-amount-btn {
    flex: 1;
    min-width: 60px;
    font-weight: 500;
}

.quick-amount-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

/* Amount Slider */
.amount-slider-section {
    margin-bottom: 25px;
}

.slider-container {
    margin-top: 10px;
}

.amount-slider {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    outline: none;
    margin-bottom: 10px;
}

.amount-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: #667eea;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.amount-slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 0 0 8px rgba(102, 126, 234, 0.2);
}

.amount-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #667eea;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: #6c757d;
}

/* Balance Information */
.balance-info-section {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.balance-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.balance-row:last-child {
    margin-bottom: 0;
}

.balance-label {
    color: #6c757d;
    font-size: 0.9rem;
}

.balance-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2c3e50;
}

/* Calculation Results Card */
.calculation-results-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.results-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.results-title {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Calculation Content */
.calculation-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.calculation-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.calculation-row.principal {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
}

.calculation-row.total {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    font-weight: 600;
}

.calculation-row.maturity-amount {
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #d4edda;
}

.calc-label {
    color: #495057;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.calc-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #2c3e50;
}

.calc-value.highlight {
    color: #28a745;
    font-size: 1.1rem;
}

/* Interest Breakdown */
.interest-breakdown,
.maturity-info {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
}

.breakdown-header,
.maturity-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.breakdown-header h6,
.maturity-header h6 {
    margin: 0;
    color: #495057;
    font-size: 0.95rem;
    font-weight: 600;
}

/* ROI Information */
.roi-info {
    margin-top: 20px;
}

.roi-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.roi-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.roi-value {
    font-size: 2rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin: 10px 0;
}

.roi-apy {
    opacity: 0.8;
}

/* No Calculation State */
.no-calculation-state {
    text-align: center;
    padding: 40px 20px;
}

.no-calc-icon {
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-calc-text p {
    margin: 0;
    font-size: 1rem;
}

/* Calculation Loading */
.calculation-loading {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.loading-text {
    color: #6c757d;
}

/* Step Navigation */
.step-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.step-navigation .btn {
    padding: 10px 25px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .amount-input-section .row {
        flex-direction: column-reverse;
    }
    
    .calculation-results-card {
        position: static;
        margin-bottom: 20px;
    }
    
    .amount-input-card {
        padding: 20px;
    }
    
    .quick-amounts-buttons {
        justify-content: space-between;
    }
    
    .quick-amount-btn {
        flex: 0 0 calc(25% - 6px);
    }
    
    .step-navigation {
        flex-direction: column;
        gap: 10px;
    }
    
    .step-navigation .btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .amount-input-card,
    .calculation-results-card {
        padding: 15px;
    }
    
    .amount-input {
        font-size: 1rem;
    }
    
    .roi-value {
        font-size: 1.5rem;
    }
    
    .calculation-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .calc-value {
        align-self: flex-end;
    }
}

/* Animation Classes */
.calculation-animate {
    animation: calculationSlideIn 0.3s ease-out;
}

@keyframes calculationSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.amount-input-focus {
    animation: inputFocus 0.3s ease-out;
}

@keyframes inputFocus {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}
```

## 📋 Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Amount input HTML oluşturma
- [ ] Calculation results display oluşturma
- [ ] Amount input CSS styling
- [ ] Slider component styling
- [ ] Responsive design optimizasyonu
- [ ] Loading states ekleme
- [ ] JavaScript integration (sonraki adımda)

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 🧪 Test Kriterleri

### Input Tests
- [ ] Amount input validation çalışıyor
- [ ] Slider input çalışıyor
- [ ] Quick amount buttons çalışıyor
- [ ] Min/max validation çalışıyor

### Calculation Tests
- [ ] Real-time calculation çalışıyor
- [ ] Calculation display doğru
- [ ] ROI calculation doğru
- [ ] Loading states uygun

## 📝 Notlar

### Input Features
- **Number input**: 8 decimal places support
- **Slider**: Visual amount selection
- **Quick buttons**: 25%, 50%, 75%, 100%
- **Max button**: Set maximum available
- **Real-time validation**: Instant feedback

### Calculation Display
- **Principal amount**: Investment amount
- **Interest breakdown**: Daily, monthly, total
- **Maturity info**: Date and final amount
- **ROI card**: Return percentage and APY
- **Loading states**: Smooth transitions

### Validation Rules
- **Minimum amount**: Plan-specific minimum
- **Maximum amount**: Available balance
- **Step validation**: 0.00000001 RZW
- **Real-time feedback**: Instant validation

### Sonraki Adım
Bu adım tamamlandıktan sonra **Adım 5.3.4: Confirmation ve Submission** başlayacak.

---
**Tahmini Süre**: 2-3 saat
**Öncelik**: Yüksek
**Bağımlılıklar**: Adım 5.3.1 ve 5.3.2 tamamlanmış olmalı

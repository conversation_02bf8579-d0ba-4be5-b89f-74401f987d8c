# Alt Adım *******: Table Structure ve Basic Display (30-45 dakika)

## 📋 Alt Adım Özeti
Interest history table'ın temel yapısının oluşturulması. HTML table structure, data display formatting ve basic styling.

## 🎯 Hedefler
- ✅ Interest history table HTML
- ✅ Basic table structure
- ✅ Data display formatting
- ✅ Table styling

## 📊 Bu Alt Adım 3 Küçük Parçaya Bölünmüştür

### **Parça *******.1**: Table HTML Structure (10-15 dakika)
- Basic table HTML
- Table headers
- Data rows structure

### **Parça *******.2**: Data Formatting (10-15 dakika)
- Date formatting
- Amount formatting
- Status indicators

### **Parça *******.3**: Basic Table Styling (10-15 dakika)
- CSS styling
- Responsive table
- Visual enhancements

## 📋 Parça Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] **Parça *******.1**: Table HTML structure
- [ ] **Parça *******.2**: Data formatting
- [ ] **Parça *******.3**: Basic table styling

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

---

# Parça *******.1: Table HTML Structure (10-15 dakika)

## 📋 Parça Özeti
Interest history table'ın temel HTML yapısının oluşturulması.

## 🎯 Hedefler
- ✅ Table HTML structure
- ✅ Table headers
- ✅ Data rows

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### *******.1.1 Interest History Table HTML

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Details.cshtml` (Information section'dan sonra ekleme)
```html
<!-- Interest History Section (Information section'dan sonra ekleme) -->
<div class="interest-history-section">
    <div class="history-header">
        <div class="header-content">
            <h5 class="section-title">
                <i class="fas fa-history text-primary"></i>
                @Localizer["Interest Payment History"]
            </h5>
            <div class="section-subtitle">
                @Localizer["Complete record of all interest payments received"]
            </div>
        </div>
        <div class="header-actions">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="refreshHistoryTable()">
                    <i class="fas fa-sync-alt"></i>
                    <span class="d-none d-sm-inline">@Localizer["Refresh"]</span>
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showFilterModal()">
                    <i class="fas fa-filter"></i>
                    <span class="d-none d-sm-inline">@Localizer["Filter"]</span>
                </button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="showExportModal()">
                    <i class="fas fa-download"></i>
                    <span class="d-none d-sm-inline">@Localizer["Export"]</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Table Controls -->
    <div class="table-controls">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="table-info">
                    <span class="info-text">
                        @Localizer["Showing"] 
                        <span id="historyTableInfo">@Model.ViewModel.RecentInterestHistory.Count @Localizer["of"] @Model.ViewModel.Statistics.TotalPaymentCount</span>
                        @Localizer["payments"]
                    </span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="table-search">
                    <div class="input-group input-group-sm">
                        <input type="text" 
                               class="form-control" 
                               id="historySearch" 
                               placeholder="@Localizer["Search payments..."]"
                               autocomplete="off">
                        <button class="btn btn-outline-secondary" type="button" onclick="searchHistory()">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interest History Table -->
    <div class="table-container">
        <div class="table-responsive">
            <table class="table interest-history-table" id="interestHistoryTable">
                <thead>
                    <tr>
                        <th scope="col" class="sortable" data-sort="date">
                            <div class="th-content">
                                <span>@Localizer["Date"]</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </div>
                        </th>
                        <th scope="col" class="sortable" data-sort="amount">
                            <div class="th-content">
                                <span>@Localizer["Interest Amount"]</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </div>
                        </th>
                        <th scope="col" class="sortable" data-sort="total">
                            <div class="th-content">
                                <span>@Localizer["Running Total"]</span>
                                <i class="fas fa-sort sort-icon"></i>
                            </div>
                        </th>
                        <th scope="col" class="d-none d-md-table-cell">
                            @Localizer["Description"]
                        </th>
                        <th scope="col" class="d-none d-lg-table-cell">
                            @Localizer["Transaction"]
                        </th>
                        <th scope="col" class="text-center">
                            @Localizer["Actions"]
                        </th>
                    </tr>
                </thead>
                <tbody id="historyTableBody">
                    @if (Model.ViewModel.HasInterestHistory)
                    {
                        @foreach (var payment in Model.ViewModel.RecentInterestHistory)
                        {
                            <tr class="history-row" data-payment-id="@payment.Id">
                                <td class="date-cell">
                                    <div class="date-content">
                                        <span class="date-primary">@payment.FormattedPaymentDate</span>
                                        <small class="date-time d-block text-muted">@payment.FormattedTime</small>
                                        <small class="date-relative d-block text-muted d-md-none">@payment.RelativeTime</small>
                                    </div>
                                </td>
                                <td class="amount-cell">
                                    <div class="amount-content">
                                        <span class="amount-value">@payment.FormattedInterestAmount</span>
                                        <small class="amount-currency d-block text-muted">RZW</small>
                                    </div>
                                </td>
                                <td class="total-cell">
                                    <div class="total-content">
                                        <span class="total-value">@payment.FormattedRunningTotal</span>
                                        <small class="total-currency d-block text-muted">RZW</small>
                                    </div>
                                </td>
                                <td class="description-cell d-none d-md-table-cell">
                                    <div class="description-content">
                                        <span class="description-text">@payment.Description</span>
                                    </div>
                                </td>
                                <td class="transaction-cell d-none d-lg-table-cell">
                                    <div class="transaction-content">
                                        @if (payment.HasTransactionHash)
                                        {
                                            <a href="@payment.BlockchainExplorerUrl" 
                                               target="_blank" 
                                               class="transaction-link"
                                               title="@Localizer["View on blockchain explorer"]">
                                                <i class="fas fa-external-link-alt"></i>
                                                <span class="transaction-hash">@payment.TransactionHash.Substring(0, 8)...</span>
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">@Localizer["Pending"]</span>
                                        }
                                    </div>
                                </td>
                                <td class="actions-cell text-center">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" 
                                                class="btn btn-outline-primary" 
                                                onclick="showPaymentDetails(@payment.Id)"
                                                title="@Localizer["View Details"]">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        @if (payment.HasTransactionHash)
                                        {
                                            <button type="button" 
                                                    class="btn btn-outline-secondary" 
                                                    onclick="copyTransactionHash('@payment.TransactionHash')"
                                                    title="@Localizer["Copy Transaction Hash"]">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                    else
                    {
                        <tr class="no-data-row">
                            <td colspan="6" class="text-center py-4">
                                <div class="no-data-content">
                                    <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                                    <p class="text-muted mb-0">@Localizer["No interest payments found"]</p>
                                    <small class="text-muted">@Localizer["Interest payments will appear here once they are processed"]</small>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>

        <!-- Table Loading State -->
        <div class="table-loading" id="tableLoading" style="display: none;">
            <div class="loading-content">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">@Localizer["Loading..."]</span>
                </div>
                <p class="loading-text">@Localizer["Loading payment history..."]</p>
            </div>
        </div>

        <!-- Table Error State -->
        <div class="table-error" id="tableError" style="display: none;">
            <div class="error-content">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                <p class="error-text">@Localizer["Failed to load payment history"]</p>
                <button type="button" class="btn btn-primary btn-sm" onclick="refreshHistoryTable()">
                    <i class="fas fa-retry"></i> @Localizer["Try Again"]
                </button>
            </div>
        </div>
    </div>

    <!-- Pagination (will be added in next part) -->
    <div class="table-pagination" id="tablePagination">
        <!-- Pagination controls will be added in Alt Adım ******* -->
    </div>
</div>
```

#### *******.1.2 Basic Table JavaScript

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Table functionality ekleme)
```javascript
// Interest History Table Functionality
class InterestHistoryTable {
    constructor(accountId) {
        this.accountId = accountId;
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortColumn = 'date';
        this.sortDirection = 'desc';
        this.searchTerm = '';
        this.filters = {};
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.initTable();
    }

    bindEvents() {
        // Search functionality
        const searchInput = document.getElementById('historySearch');
        if (searchInput) {
            searchInput.addEventListener('input', this.debounce(() => {
                this.searchTerm = searchInput.value;
                this.loadTableData();
            }, 500));
        }

        // Sort functionality
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => {
                const sortColumn = header.getAttribute('data-sort');
                this.handleSort(sortColumn);
            });
        });

        // Row click for mobile details
        document.querySelectorAll('.history-row').forEach(row => {
            row.addEventListener('click', (e) => {
                if (window.innerWidth <= 768 && !e.target.closest('.btn')) {
                    this.toggleRowDetails(row);
                }
            });
        });
    }

    initTable() {
        this.updateTableInfo();
        this.updateSortIndicators();
    }

    handleSort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'desc';
        }
        
        this.updateSortIndicators();
        this.loadTableData();
    }

    updateSortIndicators() {
        // Reset all sort icons
        document.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'fas fa-sort sort-icon';
        });

        // Update active sort icon
        const activeHeader = document.querySelector(`[data-sort="${this.sortColumn}"]`);
        if (activeHeader) {
            const icon = activeHeader.querySelector('.sort-icon');
            icon.className = `fas fa-sort-${this.sortDirection === 'asc' ? 'up' : 'down'} sort-icon active`;
        }
    }

    async loadTableData() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                page: this.currentPage,
                pageSize: this.pageSize,
                sortColumn: this.sortColumn,
                sortDirection: this.sortDirection,
                search: this.searchTerm,
                ...this.filters
            });

            const response = await fetch(`/MyAccount/RzwSavings/Details/${this.accountId}/InterestHistory?${params}`, {
                method: 'GET',
                headers: {
                    'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                }
            });

            const result = await response.json();

            if (result.success) {
                this.updateTableContent(result.data);
                this.updatePagination(result.pagination);
                this.updateTableInfo(result.pagination);
            } else {
                this.showError(result.message);
            }
        } catch (error) {
            console.error('Error loading table data:', error);
            this.showError('Failed to load payment history');
        } finally {
            this.hideLoading();
        }
    }

    updateTableContent(payments) {
        const tbody = document.getElementById('historyTableBody');
        if (!tbody) return;

        if (payments.length === 0) {
            tbody.innerHTML = `
                <tr class="no-data-row">
                    <td colspan="6" class="text-center py-4">
                        <div class="no-data-content">
                            <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                            <p class="text-muted mb-0">No payments found</p>
                            <small class="text-muted">Try adjusting your search or filters</small>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = payments.map(payment => this.createTableRow(payment)).join('');
        this.bindRowEvents();
    }

    createTableRow(payment) {
        return `
            <tr class="history-row" data-payment-id="${payment.id}">
                <td class="date-cell">
                    <div class="date-content">
                        <span class="date-primary">${payment.formattedDate}</span>
                        <small class="date-time d-block text-muted">${payment.formattedTime}</small>
                        <small class="date-relative d-block text-muted d-md-none">${payment.relativeTime}</small>
                    </div>
                </td>
                <td class="amount-cell">
                    <div class="amount-content">
                        <span class="amount-value">${payment.formattedAmount}</span>
                        <small class="amount-currency d-block text-muted">RZW</small>
                    </div>
                </td>
                <td class="total-cell">
                    <div class="total-content">
                        <span class="total-value">${payment.formattedTotal}</span>
                        <small class="total-currency d-block text-muted">RZW</small>
                    </div>
                </td>
                <td class="description-cell d-none d-md-table-cell">
                    <div class="description-content">
                        <span class="description-text">${payment.description}</span>
                    </div>
                </td>
                <td class="transaction-cell d-none d-lg-table-cell">
                    <div class="transaction-content">
                        ${payment.transactionHash ? 
                            `<a href="${payment.explorerUrl}" target="_blank" class="transaction-link">
                                <i class="fas fa-external-link-alt"></i>
                                <span class="transaction-hash">${payment.transactionHash.substring(0, 8)}...</span>
                            </a>` : 
                            '<span class="text-muted">Pending</span>'
                        }
                    </div>
                </td>
                <td class="actions-cell text-center">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="showPaymentDetails(${payment.id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${payment.transactionHash ? 
                            `<button type="button" class="btn btn-outline-secondary" onclick="copyTransactionHash('${payment.transactionHash}')">
                                <i class="fas fa-copy"></i>
                            </button>` : ''
                        }
                    </div>
                </td>
            </tr>
        `;
    }

    bindRowEvents() {
        document.querySelectorAll('.history-row').forEach(row => {
            row.addEventListener('click', (e) => {
                if (window.innerWidth <= 768 && !e.target.closest('.btn')) {
                    this.toggleRowDetails(row);
                }
            });
        });
    }

    toggleRowDetails(row) {
        const existingDetails = row.nextElementSibling;
        if (existingDetails && existingDetails.classList.contains('row-details')) {
            existingDetails.remove();
            row.classList.remove('expanded');
        } else {
            this.showRowDetails(row);
        }
    }

    showRowDetails(row) {
        const paymentId = row.getAttribute('data-payment-id');
        const detailsRow = document.createElement('tr');
        detailsRow.className = 'row-details';
        detailsRow.innerHTML = `
            <td colspan="6" class="details-content">
                <div class="mobile-details">
                    <div class="detail-item">
                        <span class="detail-label">Description:</span>
                        <span class="detail-value">${row.querySelector('.description-cell .description-text')?.textContent || 'Daily Interest Payment'}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Transaction:</span>
                        <span class="detail-value">
                            ${row.querySelector('.transaction-link') ? 
                                row.querySelector('.transaction-link').outerHTML : 
                                'Pending'
                            }
                        </span>
                    </div>
                </div>
            </td>
        `;
        
        row.parentNode.insertBefore(detailsRow, row.nextSibling);
        row.classList.add('expanded');
    }

    updateTableInfo(pagination) {
        const infoElement = document.getElementById('historyTableInfo');
        if (infoElement && pagination) {
            const start = (pagination.currentPage - 1) * pagination.pageSize + 1;
            const end = Math.min(start + pagination.pageSize - 1, pagination.totalItems);
            infoElement.textContent = `${start}-${end} of ${pagination.totalItems}`;
        }
    }

    showLoading() {
        document.getElementById('tableLoading')?.style.setProperty('display', 'block');
        document.querySelector('.table-responsive')?.style.setProperty('display', 'none');
        document.getElementById('tableError')?.style.setProperty('display', 'none');
    }

    hideLoading() {
        document.getElementById('tableLoading')?.style.setProperty('display', 'none');
        document.querySelector('.table-responsive')?.style.setProperty('display', 'block');
    }

    showError(message) {
        document.getElementById('tableError')?.style.setProperty('display', 'block');
        document.querySelector('.table-responsive')?.style.setProperty('display', 'none');
        document.getElementById('tableLoading')?.style.setProperty('display', 'none');
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Global functions
function refreshHistoryTable() {
    if (window.interestHistoryTable) {
        window.interestHistoryTable.loadTableData();
    }
}

function searchHistory() {
    if (window.interestHistoryTable) {
        window.interestHistoryTable.loadTableData();
    }
}

function showPaymentDetails(paymentId) {
    console.log('Show payment details:', paymentId);
    // Will be implemented in later steps
}

function copyTransactionHash(hash) {
    navigator.clipboard.writeText(hash).then(() => {
        // Show success message
        console.log('Transaction hash copied:', hash);
    });
}

function showFilterModal() {
    console.log('Show filter modal');
    // Will be implemented in Alt Adım *******
}

function showExportModal() {
    console.log('Show export modal');
    // Will be implemented in Alt Adım *******
}

// Initialize table when page loads
document.addEventListener('DOMContentLoaded', function() {
    const accountId = window.rzwSavingsDetails?.accountId;
    if (accountId) {
        window.interestHistoryTable = new InterestHistoryTable(accountId);
    }
});
```

## 📋 Parça Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Table HTML structure oluşturma
- [ ] Table headers ve data rows
- [ ] Basic JavaScript functionality
- [ ] Sort functionality
- [ ] Search functionality
- [ ] Loading states

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 📝 Notlar

### Table Features
- **Sortable columns**: Date, amount, total
- **Search functionality**: Real-time search
- **Responsive design**: Mobile-friendly
- **Loading states**: Visual feedback
- **Error handling**: User-friendly errors

### Data Display
- **Date formatting**: Primary date + time
- **Amount formatting**: Decimal precision
- **Transaction links**: Blockchain explorer
- **Action buttons**: View details, copy hash

### Mobile Optimization
- **Expandable rows**: Tap to show details
- **Hidden columns**: Less important data hidden
- **Touch-friendly**: Large touch targets

### Sonraki Parça
Bu parça tamamlandıktan sonra **Parça *******.2: Data Formatting** başlayacak.

---
**Tahmini Süre**: 10-15 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Alt Adım 5.4.2 tamamlanmış olmalı

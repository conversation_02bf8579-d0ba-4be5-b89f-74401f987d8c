# Parça *******.2: Data Formatting (10-15 dakika)

## 📋 <PERSON><PERSON><PERSON>
Table data'nın formatting'i, date/time display, amount formatting ve status indicators'ların implementasyonu.

## 🎯 Hedefler
- ✅ Date formatting
- ✅ Amount formatting
- ✅ Status indicators
- ✅ Relative time display

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### *******.2.1 Enhanced Data Formatting JavaScript

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Data formatting functions ekleme)
```javascript
// Enhanced Data Formatting for Interest History Table
class DataFormatter {
    static formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return {
            primary: date.toLocaleDateString('tr-TR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            }),
            time: date.toLocaleTimeString('tr-TR', {
                hour: '2-digit',
                minute: '2-digit'
            }),
            relative: this.getRelativeTime(diffDays, date, now),
            iso: date.toISOString(),
            timestamp: date.getTime()
        };
    }

    static getRelativeTime(diffDays, date, now) {
        const diffMinutes = Math.floor((now - date) / (1000 * 60));
        const diffHours = Math.floor(diffMinutes / 60);

        if (diffMinutes < 1) return 'Just now';
        if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
        if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)} week${Math.floor(diffDays / 7) > 1 ? 's' : ''} ago`;
        if (diffDays < 365) return `${Math.floor(diffDays / 30)} month${Math.floor(diffDays / 30) > 1 ? 's' : ''} ago`;
        return `${Math.floor(diffDays / 365)} year${Math.floor(diffDays / 365) > 1 ? 's' : ''} ago`;
    }

    static formatAmount(amount, options = {}) {
        const {
            decimals = 8,
            trimZeros = true,
            showCurrency = false,
            currency = 'RZW'
        } = options;

        let formatted = parseFloat(amount).toFixed(decimals);
        
        if (trimZeros) {
            formatted = formatted.replace(/\.?0+$/, '');
        }

        // Add thousand separators
        const parts = formatted.split('.');
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        formatted = parts.join('.');

        return {
            value: formatted,
            display: showCurrency ? `${formatted} ${currency}` : formatted,
            raw: parseFloat(amount),
            currency: currency
        };
    }

    static formatTransactionHash(hash) {
        if (!hash) return null;
        
        return {
            full: hash,
            short: `${hash.substring(0, 6)}...${hash.substring(hash.length - 4)}`,
            display: `${hash.substring(0, 8)}...`,
            explorerUrl: `https://bscscan.com/tx/${hash}`
        };
    }

    static getPaymentStatus(payment) {
        if (!payment.transactionHash) {
            return {
                status: 'pending',
                text: 'Pending',
                class: 'badge-warning',
                icon: 'fas fa-clock'
            };
        }

        const paymentDate = new Date(payment.paymentDate);
        const now = new Date();
        const hoursDiff = (now - paymentDate) / (1000 * 60 * 60);

        if (hoursDiff < 1) {
            return {
                status: 'recent',
                text: 'Recent',
                class: 'badge-info',
                icon: 'fas fa-clock'
            };
        }

        return {
            status: 'confirmed',
            text: 'Confirmed',
            class: 'badge-success',
            icon: 'fas fa-check-circle'
        };
    }

    static formatPaymentDescription(description, paymentDate) {
        if (description && description !== 'Daily Interest Payment') {
            return description;
        }

        const date = new Date(paymentDate);
        const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
        return `Daily Interest - ${dayName}`;
    }

    static createAmountTrend(currentAmount, previousAmount) {
        if (!previousAmount) return null;

        const diff = currentAmount - previousAmount;
        const percentage = (diff / previousAmount) * 100;

        return {
            difference: diff,
            percentage: percentage,
            trend: diff > 0 ? 'up' : diff < 0 ? 'down' : 'same',
            icon: diff > 0 ? 'fas fa-arrow-up' : diff < 0 ? 'fas fa-arrow-down' : 'fas fa-minus',
            class: diff > 0 ? 'text-success' : diff < 0 ? 'text-danger' : 'text-muted',
            display: `${diff > 0 ? '+' : ''}${this.formatAmount(Math.abs(diff)).value}`
        };
    }
}

// Enhanced table row creation with better formatting
InterestHistoryTable.prototype.createTableRow = function(payment) {
    const dateInfo = DataFormatter.formatDate(payment.paymentDate);
    const amountInfo = DataFormatter.formatAmount(payment.interestAmount);
    const totalInfo = DataFormatter.formatAmount(payment.runningTotal);
    const transactionInfo = DataFormatter.formatTransactionHash(payment.transactionHash);
    const statusInfo = DataFormatter.getPaymentStatus(payment);
    const description = DataFormatter.formatPaymentDescription(payment.description, payment.paymentDate);

    return `
        <tr class="history-row" data-payment-id="${payment.id}" data-timestamp="${dateInfo.timestamp}">
            <td class="date-cell">
                <div class="date-content">
                    <span class="date-primary" title="${dateInfo.iso}">${dateInfo.primary}</span>
                    <small class="date-time d-block text-muted">${dateInfo.time}</small>
                    <small class="date-relative d-block text-muted d-md-none">${dateInfo.relative}</small>
                </div>
            </td>
            <td class="amount-cell">
                <div class="amount-content">
                    <span class="amount-value" title="${amountInfo.raw} RZW">${amountInfo.value}</span>
                    <small class="amount-currency d-block text-muted">RZW</small>
                    <div class="amount-status d-md-none">
                        <span class="badge ${statusInfo.class} badge-sm">
                            <i class="${statusInfo.icon}"></i> ${statusInfo.text}
                        </span>
                    </div>
                </div>
            </td>
            <td class="total-cell">
                <div class="total-content">
                    <span class="total-value" title="${totalInfo.raw} RZW">${totalInfo.value}</span>
                    <small class="total-currency d-block text-muted">RZW</small>
                </div>
            </td>
            <td class="description-cell d-none d-md-table-cell">
                <div class="description-content">
                    <span class="description-text">${description}</span>
                    <div class="payment-status mt-1">
                        <span class="badge ${statusInfo.class} badge-sm">
                            <i class="${statusInfo.icon}"></i> ${statusInfo.text}
                        </span>
                    </div>
                </div>
            </td>
            <td class="transaction-cell d-none d-lg-table-cell">
                <div class="transaction-content">
                    ${transactionInfo ? 
                        `<a href="${transactionInfo.explorerUrl}" 
                           target="_blank" 
                           class="transaction-link"
                           title="View transaction on BSC Explorer">
                            <i class="fas fa-external-link-alt"></i>
                            <span class="transaction-hash">${transactionInfo.display}</span>
                        </a>
                        <button type="button" 
                                class="btn btn-link btn-sm p-0 ms-2" 
                                onclick="copyTransactionHash('${transactionInfo.full}')"
                                title="Copy full transaction hash">
                            <i class="fas fa-copy"></i>
                        </button>` : 
                        `<span class="text-muted">
                            <i class="fas fa-clock"></i> Pending
                        </span>`
                    }
                </div>
            </td>
            <td class="actions-cell text-center">
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" 
                            class="btn btn-outline-primary" 
                            onclick="showPaymentDetails(${payment.id})"
                            title="View payment details">
                        <i class="fas fa-eye"></i>
                        <span class="d-none d-xl-inline ms-1">Details</span>
                    </button>
                    ${transactionInfo ? 
                        `<button type="button" 
                                class="btn btn-outline-secondary" 
                                onclick="copyTransactionHash('${transactionInfo.full}')"
                                title="Copy transaction hash">
                            <i class="fas fa-copy"></i>
                        </button>` : ''
                    }
                </div>
            </td>
        </tr>
    `;
};

// Enhanced mobile details with better formatting
InterestHistoryTable.prototype.showRowDetails = function(row) {
    const paymentId = row.getAttribute('data-payment-id');
    const dateCell = row.querySelector('.date-cell .date-primary');
    const amountCell = row.querySelector('.amount-cell .amount-value');
    const totalCell = row.querySelector('.total-cell .total-value');
    const descriptionText = row.querySelector('.description-cell .description-text')?.textContent || 'Daily Interest Payment';
    const transactionLink = row.querySelector('.transaction-link');
    const statusBadge = row.querySelector('.badge');

    const detailsRow = document.createElement('tr');
    detailsRow.className = 'row-details';
    detailsRow.innerHTML = `
        <td colspan="6" class="details-content">
            <div class="mobile-details">
                <div class="details-header">
                    <h6 class="details-title">Payment Details</h6>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="this.closest('.row-details').remove(); this.closest('tbody').querySelector('[data-payment-id=\\"${paymentId}\\"]').classList.remove('expanded');">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="details-body">
                    <div class="detail-grid">
                        <div class="detail-item">
                            <span class="detail-label">
                                <i class="fas fa-calendar-alt"></i> Date & Time
                            </span>
                            <span class="detail-value">
                                ${dateCell?.textContent} ${row.querySelector('.date-time')?.textContent}
                                <small class="d-block text-muted">${row.querySelector('.date-relative')?.textContent}</small>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">
                                <i class="fas fa-coins"></i> Interest Amount
                            </span>
                            <span class="detail-value amount-highlight">
                                ${amountCell?.textContent} RZW
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">
                                <i class="fas fa-chart-line"></i> Running Total
                            </span>
                            <span class="detail-value">
                                ${totalCell?.textContent} RZW
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">
                                <i class="fas fa-info-circle"></i> Description
                            </span>
                            <span class="detail-value">
                                ${descriptionText}
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">
                                <i class="fas fa-check-circle"></i> Status
                            </span>
                            <span class="detail-value">
                                ${statusBadge ? statusBadge.outerHTML : '<span class="badge badge-secondary">Unknown</span>'}
                            </span>
                        </div>
                        ${transactionLink ? `
                        <div class="detail-item">
                            <span class="detail-label">
                                <i class="fas fa-link"></i> Transaction
                            </span>
                            <span class="detail-value">
                                ${transactionLink.outerHTML}
                            </span>
                        </div>` : `
                        <div class="detail-item">
                            <span class="detail-label">
                                <i class="fas fa-clock"></i> Transaction
                            </span>
                            <span class="detail-value text-muted">
                                Pending confirmation
                            </span>
                        </div>`}
                    </div>
                </div>
                <div class="details-footer">
                    <button type="button" class="btn btn-primary btn-sm" onclick="showPaymentDetails(${paymentId})">
                        <i class="fas fa-eye"></i> View Full Details
                    </button>
                    ${transactionLink ? `
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="copyTransactionHash('${transactionLink.href.split('/').pop()}')">
                        <i class="fas fa-copy"></i> Copy Hash
                    </button>` : ''}
                </div>
            </div>
        </td>
    `;
    
    row.parentNode.insertBefore(detailsRow, row.nextSibling);
    row.classList.add('expanded');
    
    // Animate the details row
    setTimeout(() => {
        detailsRow.classList.add('show');
    }, 10);
};

// Enhanced copy function with better feedback
function copyTransactionHash(hash) {
    if (!hash) return;
    
    navigator.clipboard.writeText(hash).then(() => {
        // Show success toast
        showToast('Transaction hash copied to clipboard', 'success');
    }).catch(err => {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = hash;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('Transaction hash copied to clipboard', 'success');
    });
}

// Toast notification function
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => toast.classList.add('show'), 100);
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

// Update relative times periodically
setInterval(() => {
    document.querySelectorAll('.date-relative').forEach(element => {
        const row = element.closest('.history-row');
        const timestamp = parseInt(row?.getAttribute('data-timestamp'));
        if (timestamp) {
            const now = new Date();
            const date = new Date(timestamp);
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            element.textContent = DataFormatter.getRelativeTime(diffDays, date, now);
        }
    });
}, 60000); // Update every minute
```

#### *******.2.2 Enhanced CSS for Data Formatting

**Dosya**: `src/wwwroot/css/rzw-savings-details.css` (Data formatting styles ekleme)
```css
/* Enhanced Data Formatting Styles */

/* Amount highlighting */
.amount-value,
.total-value {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #28a745;
    font-size: 0.95rem;
}

.amount-highlight {
    color: #28a745;
    font-weight: 700;
    font-size: 1.1rem;
}

/* Date formatting */
.date-primary {
    font-weight: 600;
    color: #2c3e50;
}

.date-time {
    font-size: 0.8rem;
    color: #6c757d;
}

.date-relative {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
}

/* Transaction links */
.transaction-link {
    color: #667eea;
    text-decoration: none;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.transaction-link:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

.transaction-hash {
    font-weight: 500;
}

/* Status badges */
.badge-sm {
    font-size: 0.7rem;
    padding: 3px 6px;
}

.payment-status .badge {
    font-size: 0.7rem;
}

/* Mobile details */
.row-details {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.row-details.show {
    opacity: 1;
    transform: translateY(0);
}

.details-content {
    padding: 20px;
}

.mobile-details {
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.details-header {
    background: #f8f9fa;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.details-title {
    margin: 0;
    color: #495057;
    font-weight: 600;
    font-size: 1rem;
}

.details-body {
    padding: 20px;
}

.detail-grid {
    display: grid;
    gap: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 120px;
}

.detail-label i {
    width: 14px;
    text-align: center;
    color: #667eea;
}

.detail-value {
    text-align: right;
    font-weight: 600;
    color: #2c3e50;
    flex: 1;
}

.details-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Toast notifications */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    padding: 15px 20px;
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    border-left: 4px solid #007bff;
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-notification.toast-success {
    border-left-color: #28a745;
}

.toast-notification.toast-error {
    border-left-color: #dc3545;
}

.toast-notification.toast-warning {
    border-left-color: #ffc107;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #2c3e50;
    font-weight: 500;
}

.toast-success .toast-content i {
    color: #28a745;
}

.toast-error .toast-content i {
    color: #dc3545;
}

.toast-warning .toast-content i {
    color: #ffc107;
}

/* Expanded row styling */
.history-row.expanded {
    background-color: #f8f9fa;
}

.history-row.expanded td {
    border-bottom: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .amount-value,
    .total-value {
        font-size: 0.9rem;
    }
    
    .details-content {
        padding: 15px;
    }
    
    .details-header,
    .details-body,
    .details-footer {
        padding: 12px 15px;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .detail-label {
        min-width: auto;
    }
    
    .detail-value {
        text-align: left;
    }
    
    .details-footer {
        flex-direction: column;
    }
    
    .details-footer .btn {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .toast-notification {
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .toast-notification.show {
        transform: translateY(0);
    }
    
    .amount-value,
    .total-value {
        font-size: 0.85rem;
    }
    
    .date-primary {
        font-size: 0.9rem;
    }
    
    .transaction-link {
        font-size: 0.8rem;
    }
}

/* Loading skeleton for amounts */
.amount-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    height: 1.2em;
    width: 80px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Highlight updated values */
.value-updated {
    animation: valueHighlight 1s ease-out;
}

@keyframes valueHighlight {
    0% { background-color: rgba(40, 167, 69, 0.3); }
    100% { background-color: transparent; }
}
```

## 📋 Parça Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Enhanced data formatting functions
- [ ] Date/time formatting
- [ ] Amount formatting with precision
- [ ] Transaction hash formatting
- [ ] Status indicators
- [ ] Mobile details enhancement
- [ ] Toast notifications
- [ ] CSS styling for formatting

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 📝 Notlar

### Data Formatting Features
- **Date formatting**: Multiple formats (primary, time, relative)
- **Amount formatting**: Precision control, thousand separators
- **Transaction hashing**: Short/full display with explorer links
- **Status indicators**: Visual status badges
- **Relative time**: Auto-updating relative timestamps

### Mobile Enhancements
- **Expandable details**: Rich detail view for mobile
- **Touch interactions**: Better mobile experience
- **Responsive formatting**: Optimized for small screens
- **Toast notifications**: User feedback system

### Visual Improvements
- **Color coding**: Status-based colors
- **Typography**: Monospace for amounts
- **Animations**: Smooth transitions
- **Loading states**: Skeleton loading

### Sonraki Parça
Bu parça tamamlandıktan sonra **Parça *******.3: Basic Table Styling** başlayacak.

---
**Tahmini Süre**: 10-15 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Parça *******.1 tamamlanmış olmalı

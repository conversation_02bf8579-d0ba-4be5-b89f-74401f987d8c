# Alt Adım *******: Pagination ve Filtering (45-60 dakika)

## 📋 Alt Adım Özeti
Interest history table için pagination controls, date range filtering ve advanced search functionality'nin implementasyonu.

## 🎯 Hedefler
- ✅ Pagination controls oluşturma
- ✅ Date range filtering
- ✅ Advanced search functionality
- ✅ Filter state management

## 📊 Bu Alt Adım 3 Küçük Parçaya Bölünmüştür

### **Parça *******.1**: Pagination Controls (15-20 dakika)
- Pagination HTML structure
- Page navigation logic
- Page size selection

### **Parça *******.2**: Date Range Filtering (15-20 dakika)
- Date picker integration
- Filter modal
- Date range validation

### **Parça *******.3**: Advanced Search (15-20 dakika)
- Search functionality
- Filter combinations
- State management

## 📋 Parça Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] **Parça *******.1**: Pagination controls
- [ ] **Parça *******.2**: Date range filtering
- [ ] **Parça *******.3**: Advanced search

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

---

# Parça *******.1: Pagination Controls (15-20 dakika)

## 📋 Parça Özeti
Table pagination controls'ların oluşturulması. Page navigation, page size selection ve pagination info.

## 🎯 Hedefler
- ✅ Pagination HTML
- ✅ Page navigation logic
- ✅ Page size selection

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### *******.1.1 Pagination HTML

**Dosya**: `src/Areas/MyAccount/Pages/RzwSavings/Details.cshtml` (Table pagination section güncelleme)
```html
<!-- Table Pagination (tablePagination div'ini güncelle) -->
<div class="table-pagination" id="tablePagination">
    <div class="pagination-container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="pagination-info">
                    <div class="info-group">
                        <span class="info-label">@Localizer["Show"]:</span>
                        <select class="form-select form-select-sm page-size-select" id="pageSizeSelect">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span class="info-label">@Localizer["per page"]</span>
                    </div>
                    <div class="pagination-summary">
                        <span id="paginationSummary">@Localizer["Showing 1-10 of 0 payments"]</span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <nav aria-label="Interest history pagination" class="pagination-nav">
                    <ul class="pagination pagination-sm justify-content-end" id="paginationControls">
                        <!-- Pagination buttons will be generated by JavaScript -->
                        <li class="page-item disabled">
                            <button class="page-link" disabled>
                                <i class="fas fa-angle-double-left"></i>
                                <span class="d-none d-sm-inline ms-1">@Localizer["First"]</span>
                            </button>
                        </li>
                        <li class="page-item disabled">
                            <button class="page-link" disabled>
                                <i class="fas fa-angle-left"></i>
                                <span class="d-none d-sm-inline ms-1">@Localizer["Previous"]</span>
                            </button>
                        </li>
                        <li class="page-item active">
                            <button class="page-link">1</button>
                        </li>
                        <li class="page-item disabled">
                            <button class="page-link" disabled>
                                <span class="d-none d-sm-inline me-1">@Localizer["Next"]</span>
                                <i class="fas fa-angle-right"></i>
                            </button>
                        </li>
                        <li class="page-item disabled">
                            <button class="page-link" disabled>
                                <span class="d-none d-sm-inline me-1">@Localizer["Last"]</span>
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Quick Jump -->
    <div class="quick-jump-container" id="quickJumpContainer" style="display: none;">
        <div class="quick-jump">
            <span class="jump-label">@Localizer["Go to page"]:</span>
            <div class="input-group input-group-sm">
                <input type="number" 
                       class="form-control" 
                       id="jumpToPage" 
                       min="1" 
                       max="1"
                       placeholder="1">
                <button class="btn btn-outline-primary" type="button" onclick="jumpToPage()">
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
            <button type="button" class="btn btn-sm btn-link" onclick="hideQuickJump()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>
```

#### *******.1.2 Pagination JavaScript

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Pagination functionality ekleme)
```javascript
// Pagination Management
class PaginationManager {
    constructor(tableInstance) {
        this.table = tableInstance;
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.totalItems = 0;
        this.maxVisiblePages = 5;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.updatePageSizeSelect();
    }

    bindEvents() {
        // Page size change
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.changePageSize(parseInt(e.target.value));
            });
        }

        // Pagination controls will be bound dynamically
        this.bindPaginationEvents();
    }

    bindPaginationEvents() {
        const paginationControls = document.getElementById('paginationControls');
        if (paginationControls) {
            paginationControls.addEventListener('click', (e) => {
                e.preventDefault();
                const button = e.target.closest('.page-link');
                if (!button || button.disabled) return;

                const action = button.getAttribute('data-action');
                const page = button.getAttribute('data-page');

                if (action) {
                    this.handlePaginationAction(action);
                } else if (page) {
                    this.goToPage(parseInt(page));
                }
            });
        }
    }

    handlePaginationAction(action) {
        switch (action) {
            case 'first':
                this.goToPage(1);
                break;
            case 'prev':
                this.goToPage(Math.max(1, this.currentPage - 1));
                break;
            case 'next':
                this.goToPage(Math.min(this.totalPages, this.currentPage + 1));
                break;
            case 'last':
                this.goToPage(this.totalPages);
                break;
            case 'jump':
                this.showQuickJump();
                break;
        }
    }

    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) {
            return;
        }

        this.currentPage = page;
        this.table.currentPage = page;
        this.updatePaginationControls();
        this.table.loadTableData();
    }

    changePageSize(newSize) {
        if (newSize === this.pageSize) return;

        this.pageSize = newSize;
        this.table.pageSize = newSize;
        
        // Recalculate current page to maintain position
        const currentItem = (this.currentPage - 1) * this.table.pageSize + 1;
        this.currentPage = Math.ceil(currentItem / newSize);
        this.table.currentPage = this.currentPage;
        
        this.table.loadTableData();
    }

    updatePagination(paginationData) {
        this.currentPage = paginationData.currentPage;
        this.totalPages = paginationData.totalPages;
        this.totalItems = paginationData.totalItems;
        this.pageSize = paginationData.pageSize;

        this.updatePaginationControls();
        this.updatePaginationSummary();
        this.updatePageSizeSelect();
        this.updateQuickJump();
    }

    updatePaginationControls() {
        const controls = document.getElementById('paginationControls');
        if (!controls) return;

        const buttons = this.generatePaginationButtons();
        controls.innerHTML = buttons;
    }

    generatePaginationButtons() {
        let buttons = '';

        // First button
        buttons += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <button class="page-link" data-action="first" ${this.currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-angle-double-left"></i>
                    <span class="d-none d-sm-inline ms-1">First</span>
                </button>
            </li>
        `;

        // Previous button
        buttons += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <button class="page-link" data-action="prev" ${this.currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-angle-left"></i>
                    <span class="d-none d-sm-inline ms-1">Previous</span>
                </button>
            </li>
        `;

        // Page numbers
        const pageNumbers = this.getVisiblePageNumbers();
        pageNumbers.forEach(pageNum => {
            if (pageNum === '...') {
                buttons += `
                    <li class="page-item disabled">
                        <button class="page-link" disabled>...</button>
                    </li>
                `;
            } else {
                buttons += `
                    <li class="page-item ${pageNum === this.currentPage ? 'active' : ''}">
                        <button class="page-link" data-page="${pageNum}">${pageNum}</button>
                    </li>
                `;
            }
        });

        // Quick jump button (if many pages)
        if (this.totalPages > 10) {
            buttons += `
                <li class="page-item">
                    <button class="page-link" data-action="jump" title="Jump to page">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </li>
            `;
        }

        // Next button
        buttons += `
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <button class="page-link" data-action="next" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                    <span class="d-none d-sm-inline me-1">Next</span>
                    <i class="fas fa-angle-right"></i>
                </button>
            </li>
        `;

        // Last button
        buttons += `
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <button class="page-link" data-action="last" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                    <span class="d-none d-sm-inline me-1">Last</span>
                    <i class="fas fa-angle-double-right"></i>
                </button>
            </li>
        `;

        return buttons;
    }

    getVisiblePageNumbers() {
        const pages = [];
        const totalPages = this.totalPages;
        const current = this.currentPage;
        const maxVisible = this.maxVisiblePages;

        if (totalPages <= maxVisible) {
            // Show all pages
            for (let i = 1; i <= totalPages; i++) {
                pages.push(i);
            }
        } else {
            // Calculate range around current page
            let start = Math.max(1, current - Math.floor(maxVisible / 2));
            let end = Math.min(totalPages, start + maxVisible - 1);

            // Adjust start if we're near the end
            if (end - start + 1 < maxVisible) {
                start = Math.max(1, end - maxVisible + 1);
            }

            // Add first page and ellipsis if needed
            if (start > 1) {
                pages.push(1);
                if (start > 2) {
                    pages.push('...');
                }
            }

            // Add visible pages
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }

            // Add ellipsis and last page if needed
            if (end < totalPages) {
                if (end < totalPages - 1) {
                    pages.push('...');
                }
                pages.push(totalPages);
            }
        }

        return pages;
    }

    updatePaginationSummary() {
        const summary = document.getElementById('paginationSummary');
        if (!summary) return;

        const start = (this.currentPage - 1) * this.pageSize + 1;
        const end = Math.min(start + this.pageSize - 1, this.totalItems);

        if (this.totalItems === 0) {
            summary.textContent = 'No payments found';
        } else {
            summary.textContent = `Showing ${start}-${end} of ${this.totalItems} payments`;
        }
    }

    updatePageSizeSelect() {
        const select = document.getElementById('pageSizeSelect');
        if (select) {
            select.value = this.pageSize;
        }
    }

    updateQuickJump() {
        const jumpInput = document.getElementById('jumpToPage');
        if (jumpInput) {
            jumpInput.max = this.totalPages;
            jumpInput.placeholder = this.currentPage.toString();
        }
    }

    showQuickJump() {
        const container = document.getElementById('quickJumpContainer');
        if (container) {
            container.style.display = 'block';
            const input = document.getElementById('jumpToPage');
            if (input) {
                input.focus();
                input.select();
            }
        }
    }

    hideQuickJump() {
        const container = document.getElementById('quickJumpContainer');
        if (container) {
            container.style.display = 'none';
        }
    }

    jumpToSpecificPage() {
        const input = document.getElementById('jumpToPage');
        if (input) {
            const page = parseInt(input.value);
            if (page >= 1 && page <= this.totalPages) {
                this.goToPage(page);
                this.hideQuickJump();
            } else {
                input.classList.add('is-invalid');
                setTimeout(() => {
                    input.classList.remove('is-invalid');
                }, 2000);
            }
        }
    }
}

// Update InterestHistoryTable to use PaginationManager
InterestHistoryTable.prototype.initPagination = function() {
    this.pagination = new PaginationManager(this);
};

InterestHistoryTable.prototype.updatePagination = function(paginationData) {
    if (this.pagination) {
        this.pagination.updatePagination(paginationData);
    }
};

// Global functions for pagination
function jumpToPage() {
    if (window.interestHistoryTable && window.interestHistoryTable.pagination) {
        window.interestHistoryTable.pagination.jumpToSpecificPage();
    }
}

function hideQuickJump() {
    if (window.interestHistoryTable && window.interestHistoryTable.pagination) {
        window.interestHistoryTable.pagination.hideQuickJump();
    }
}

// Update table initialization
document.addEventListener('DOMContentLoaded', function() {
    const accountId = window.rzwSavingsDetails?.accountId;
    if (accountId) {
        window.interestHistoryTable = new InterestHistoryTable(accountId);
        window.interestHistoryTable.initPagination();
    }
});

// Add keyboard support for quick jump
document.addEventListener('keydown', function(e) {
    const jumpInput = document.getElementById('jumpToPage');
    if (jumpInput && document.activeElement === jumpInput && e.key === 'Enter') {
        jumpToPage();
    }
    
    if (e.key === 'Escape') {
        hideQuickJump();
    }
});
```

#### *******.1.3 Pagination CSS

**Dosya**: `src/wwwroot/css/rzw-savings-details.css` (Pagination styles ekleme)
```css
/* Table Pagination */
.table-pagination {
    background: white;
    border: 1px solid #e9ecef;
    border-top: none;
    border-radius: 0 0 12px 12px;
    padding: 15px 20px;
}

.pagination-container {
    margin-bottom: 0;
}

/* Pagination Info */
.pagination-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-label {
    color: #6c757d;
    font-size: 0.9rem;
    white-space: nowrap;
}

.page-size-select {
    width: auto;
    min-width: 70px;
}

.pagination-summary {
    color: #495057;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Pagination Navigation */
.pagination-nav {
    display: flex;
    justify-content: flex-end;
}

.pagination {
    margin: 0;
}

.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 6px 12px;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}

.pagination .page-link:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.pagination .page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* Quick Jump */
.quick-jump-container {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.quick-jump {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
}

.jump-label {
    color: #6c757d;
    font-size: 0.9rem;
    white-space: nowrap;
}

.quick-jump .input-group {
    width: 120px;
}

.quick-jump .form-control {
    text-align: center;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .table-pagination {
        padding: 12px 15px;
    }
    
    .pagination-container .row {
        flex-direction: column;
        gap: 15px;
    }
    
    .pagination-info {
        align-items: center;
        text-align: center;
    }
    
    .pagination-nav {
        justify-content: center;
    }
    
    .pagination .page-link {
        padding: 4px 8px;
        font-size: 0.8rem;
        margin: 0 1px;
    }
    
    .pagination .page-link span {
        display: none;
    }
    
    .quick-jump {
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .table-pagination {
        padding: 10px 12px;
    }
    
    .info-group {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .pagination .page-link {
        padding: 3px 6px;
        font-size: 0.75rem;
        min-width: 32px;
        text-align: center;
    }
    
    .quick-jump .input-group {
        width: 100px;
    }
}

/* Animation for pagination updates */
.pagination-updating {
    opacity: 0.6;
    pointer-events: none;
}

.pagination-updated {
    animation: paginationUpdate 0.3s ease-out;
}

@keyframes paginationUpdate {
    0% { transform: translateY(5px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

/* Loading state for pagination */
.pagination-loading .page-link {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    color: transparent;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .pagination .page-link {
        border-width: 2px;
    }
    
    .pagination .page-item.active .page-link {
        border-color: black;
    }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
    .table-pagination {
        background: #2d3748;
        border-color: #4a5568;
        color: #ffffff;
    }
    
    .pagination .page-link {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .pagination .page-link:hover {
        background-color: #4a5568;
        border-color: #718096;
    }
    
    .pagination .page-item.active .page-link {
        background-color: #667eea;
        border-color: #667eea;
    }
    
    .pagination .page-item.disabled .page-link {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #718096;
    }
    
    .page-size-select {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .quick-jump .form-control {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}
```

## 📋 Parça Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Pagination HTML structure
- [ ] Page navigation logic
- [ ] Page size selection
- [ ] Quick jump functionality
- [ ] Pagination state management
- [ ] CSS styling
- [ ] Responsive design
- [ ] Keyboard navigation

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 📝 Notlar

### Pagination Features
- **Smart pagination**: Shows relevant page numbers
- **Page size selection**: 10, 25, 50, 100 options
- **Quick jump**: Direct page navigation
- **Keyboard support**: Enter and Escape keys
- **Responsive design**: Mobile-optimized controls

### Navigation Options
- **First/Last**: Jump to boundaries
- **Previous/Next**: Step navigation
- **Direct pages**: Click specific pages
- **Quick jump**: Type page number
- **Page size**: Change items per page

### User Experience
- **Visual feedback**: Active/disabled states
- **Loading states**: During data fetch
- **Accessibility**: ARIA labels and keyboard support
- **Mobile optimization**: Touch-friendly controls

### Sonraki Parça
Bu parça tamamlandıktan sonra **Parça *******.2: Date Range Filtering** başlayacak.

---
**Tahmini Süre**: 15-20 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Alt Adım 5.4.3.1 tamamlanmış olmalı

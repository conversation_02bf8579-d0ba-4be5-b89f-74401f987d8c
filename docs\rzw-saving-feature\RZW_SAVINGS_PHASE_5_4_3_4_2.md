# Parça *******.2: Card Layout for Mobile (15-20 dakika)

## 📋 <PERSON><PERSON><PERSON>ti
Mobile devices için card-based layout'un implementasyonu. Table view'ın yanında card view seçeneği sunarak mobile kullanıcı deneyimini iyileştirme.

## 🎯 Hedefler
- ✅ Card-based mobile layout
- ✅ Expandable card details
- ✅ Mobile-friendly information display
- ✅ View toggle functionality

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### *******.2.1 Mobile Card CSS

**Dosya**: `src/wwwroot/css/rzw-savings-details.css` (Mobile card styles ekleme)
```css
/* Mobile Card Layout */

/* View Toggle Controls */
.view-toggle-controls {
    display: none;
    background: white;
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
    justify-content: center;
    gap: 5px;
}

@media (max-width: 768px) {
    .view-toggle-controls {
        display: flex;
    }
}

.view-toggle-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 6px 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.view-toggle-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.view-toggle-btn:hover {
    background: #e9ecef;
}

.view-toggle-btn.active:hover {
    background: #5a6fd8;
}

.view-toggle-btn i {
    font-size: 0.75rem;
}

/* Mobile Card Container */
.mobile-card-view {
    display: none;
    padding: 15px;
    background: #f8f9fa;
}

@media (max-width: 768px) {
    .mobile-card-view.active {
        display: block;
    }
    
    .table-view.mobile-hidden {
        display: none;
    }
}

/* Interest Payment Cards */
.interest-payment-cards {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.payment-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    border: 1px solid #e9ecef;
    transition: all 0.2s;
}

.payment-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.payment-card.expanded {
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
    border-color: #667eea;
}

/* Card Header */
.payment-card-header {
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    user-select: none;
}

.payment-card-header:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.card-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-primary-info {
    flex: 1;
}

.card-date {
    font-size: 0.85rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
}

.card-time {
    font-size: 0.7rem;
    color: #6c757d;
}

.card-amount {
    text-align: right;
}

.card-amount-value {
    font-size: 1rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 2px;
}

.card-amount-currency {
    font-size: 0.7rem;
    color: #6c757d;
}

.card-expand-icon {
    margin-left: 10px;
    color: #667eea;
    transition: transform 0.2s;
}

.payment-card.expanded .card-expand-icon {
    transform: rotate(180deg);
}

/* Card Body (Expandable Details) */
.payment-card-body {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.payment-card.expanded .payment-card-body {
    max-height: 300px;
}

.card-body-content {
    padding: 15px;
    background: white;
}

/* Card Details Grid */
.card-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.card-detail-item {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.card-detail-label {
    font-size: 0.7rem;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.card-detail-value {
    font-size: 0.85rem;
    color: #2c3e50;
    font-weight: 500;
}

.card-detail-value.amount {
    color: #28a745;
    font-weight: 600;
}

.card-detail-value.status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.card-detail-value .badge {
    font-size: 0.65rem;
    padding: 3px 6px;
}

/* Card Description */
.card-description {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 15px;
    border-left: 3px solid #667eea;
}

.card-description-text {
    font-size: 0.8rem;
    color: #495057;
    margin: 0;
    line-height: 1.4;
}

/* Card Actions */
.card-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    padding-top: 10px;
    border-top: 1px solid #e9ecef;
}

.card-action-btn {
    padding: 6px 12px;
    font-size: 0.75rem;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 4px;
}

.card-action-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.card-action-btn.primary {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.card-action-btn.primary:hover {
    background: #5a6fd8;
    border-color: #5a6fd8;
}

.card-action-btn i {
    font-size: 0.7rem;
}

/* Card Loading State */
.payment-card.loading {
    opacity: 0.6;
    pointer-events: none;
}

.payment-card.loading .payment-card-header::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 15px;
    width: 16px;
    height: 16px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: cardLoadingSpin 1s linear infinite;
}

@keyframes cardLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State for Cards */
.cards-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.cards-empty-state i {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 15px;
}

.cards-empty-state h6 {
    color: #495057;
    margin-bottom: 8px;
}

.cards-empty-state p {
    font-size: 0.85rem;
    margin: 0;
}

/* Card Pagination */
.card-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 15px;
    gap: 10px;
}

.card-load-more {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-load-more:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.card-load-more:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.card-load-more i {
    font-size: 0.8rem;
}

/* Card Search Results */
.card-search-info {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 10px 15px;
    margin-bottom: 15px;
    font-size: 0.8rem;
    color: #0066cc;
}

.card-search-info i {
    margin-right: 5px;
}

/* Card Animation */
.payment-card {
    animation: cardSlideIn 0.3s ease-out;
}

@keyframes cardSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card Responsive Adjustments */
@media (max-width: 576px) {
    .mobile-card-view {
        padding: 10px;
    }
    
    .payment-card {
        border-radius: 8px;
    }
    
    .payment-card-header {
        padding: 12px;
    }
    
    .card-body-content {
        padding: 12px;
    }
    
    .card-details-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .card-amount-value {
        font-size: 0.9rem;
    }
    
    .card-date {
        font-size: 0.8rem;
    }
    
    .card-actions {
        flex-direction: column;
        gap: 6px;
    }
    
    .card-action-btn {
        justify-content: center;
        padding: 8px 12px;
    }
}

/* Card Accessibility */
.payment-card-header:focus {
    outline: 3px solid #667eea;
    outline-offset: 2px;
}

.card-action-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 1px;
}

/* Card Print Styles */
@media print {
    .mobile-card-view {
        display: none !important;
    }
}
```

## 📋 Parça Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Mobile card CSS implementation
- [ ] Card layout HTML structure
- [ ] Expandable card functionality
- [ ] View toggle controls
- [ ] Card-specific JavaScript
- [ ] Mobile card interactions
- [ ] Card loading states
- [ ] Card pagination

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 📝 Notlar

### Card Layout Features
- **Expandable cards**: Tap to expand/collapse details
- **View toggle**: Switch between table and card view
- **Touch-friendly**: Large touch targets
- **Responsive**: Adapts to different screen sizes
- **Animated**: Smooth expand/collapse animations

### User Experience
- **Quick overview**: Essential info in card header
- **Detailed view**: Full details in expandable body
- **Easy navigation**: Load more pagination
- **Visual feedback**: Hover and focus states

### Accessibility
- **Keyboard navigation**: Full keyboard support
- **Focus indicators**: Clear focus outlines
- **Screen reader**: Proper semantic structure
- **Touch targets**: Minimum 44px touch targets

#### *******.2.2 Card JavaScript Implementation

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Mobile card functionality ekleme)
```javascript
// Mobile Card View Manager
class MobileCardManager {
    constructor(tableInstance) {
        this.table = tableInstance;
        this.currentView = 'table'; // 'table' or 'card'
        this.expandedCards = new Set();
        this.cardContainer = null;
        this.loadMoreBtn = null;
        this.currentPage = 1;
        this.cardsPerPage = 10;

        this.init();
    }

    init() {
        this.createCardContainer();
        this.createViewToggle();
        this.bindCardEvents();
        this.setupCardPagination();
    }

    createCardContainer() {
        const tableContainer = document.querySelector('.table-container');
        if (!tableContainer) return;

        // Create card view container
        const cardViewHTML = `
            <div class="mobile-card-view" id="mobileCardView">
                <div class="card-search-info" id="cardSearchInfo" style="display: none;">
                    <i class="fas fa-search"></i>
                    <span id="cardSearchText"></span>
                </div>
                <div class="interest-payment-cards" id="interestPaymentCards">
                    <!-- Cards will be populated here -->
                </div>
                <div class="cards-empty-state" id="cardsEmptyState" style="display: none;">
                    <i class="fas fa-receipt"></i>
                    <h6>No Interest Payments Found</h6>
                    <p>There are no interest payments to display for the selected criteria.</p>
                </div>
                <div class="card-pagination" id="cardPagination" style="display: none;">
                    <button type="button" class="card-load-more" id="cardLoadMore">
                        <i class="fas fa-plus"></i>
                        <span>Load More</span>
                    </button>
                </div>
            </div>
        `;

        tableContainer.insertAdjacentHTML('afterend', cardViewHTML);
        this.cardContainer = document.getElementById('interestPaymentCards');
        this.loadMoreBtn = document.getElementById('cardLoadMore');
    }

    createViewToggle() {
        const tableContainer = document.querySelector('.table-container');
        if (!tableContainer) return;

        const toggleHTML = `
            <div class="view-toggle-controls" id="viewToggleControls">
                <button type="button" class="view-toggle-btn active" data-view="table">
                    <i class="fas fa-table"></i>
                    <span>Table</span>
                </button>
                <button type="button" class="view-toggle-btn" data-view="card">
                    <i class="fas fa-th-large"></i>
                    <span>Cards</span>
                </button>
            </div>
        `;

        tableContainer.insertAdjacentHTML('beforebegin', toggleHTML);
    }

    bindCardEvents() {
        // View toggle events
        document.addEventListener('click', (e) => {
            if (e.target.closest('.view-toggle-btn')) {
                const btn = e.target.closest('.view-toggle-btn');
                const view = btn.getAttribute('data-view');
                this.switchView(view);
            }
        });

        // Card expand/collapse events
        document.addEventListener('click', (e) => {
            if (e.target.closest('.payment-card-header')) {
                const card = e.target.closest('.payment-card');
                if (card) {
                    this.toggleCard(card);
                }
            }
        });

        // Card action events
        document.addEventListener('click', (e) => {
            if (e.target.closest('.card-action-btn')) {
                const btn = e.target.closest('.card-action-btn');
                const action = btn.getAttribute('data-action');
                const cardId = btn.closest('.payment-card').getAttribute('data-id');
                this.handleCardAction(action, cardId);
            }
        });

        // Load more events
        if (this.loadMoreBtn) {
            this.loadMoreBtn.addEventListener('click', () => {
                this.loadMoreCards();
            });
        }
    }

    switchView(view) {
        if (this.currentView === view) return;

        this.currentView = view;

        // Update toggle buttons
        document.querySelectorAll('.view-toggle-btn').forEach(btn => {
            btn.classList.toggle('active', btn.getAttribute('data-view') === view);
        });

        // Switch containers
        const tableView = document.querySelector('.table-view, .table-container');
        const cardView = document.getElementById('mobileCardView');

        if (view === 'card') {
            if (tableView) tableView.classList.add('mobile-hidden');
            if (cardView) cardView.classList.add('active');
            this.loadCards();
        } else {
            if (tableView) tableView.classList.remove('mobile-hidden');
            if (cardView) cardView.classList.remove('active');
        }
    }

    async loadCards(append = false) {
        if (!this.cardContainer) return;

        try {
            // Show loading state
            if (!append) {
                this.cardContainer.innerHTML = this.createLoadingCards();
                this.currentPage = 1;
            }

            // Get current filters from table
            const filters = this.table.getCurrentFilters();

            // Add pagination parameters
            const params = {
                ...filters,
                page: this.currentPage,
                pageSize: this.cardsPerPage,
                format: 'card'
            };

            // Make API request
            const response = await fetch('/api/rzw-savings/interest-history?' + new URLSearchParams(params));
            const data = await response.json();

            if (data.success) {
                if (append) {
                    this.appendCards(data.data);
                } else {
                    this.renderCards(data.data);
                }

                this.updateCardPagination(data.hasMore);
                this.updateSearchInfo(data.totalCount, filters);
            } else {
                this.showError('Failed to load interest history');
            }

        } catch (error) {
            console.error('Error loading cards:', error);
            this.showError('An error occurred while loading data');
        }
    }

    renderCards(data) {
        if (!this.cardContainer) return;

        if (!data || data.length === 0) {
            this.showEmptyState();
            return;
        }

        this.cardContainer.innerHTML = data.map(item => this.createCardHTML(item)).join('');
        this.hideEmptyState();
    }

    appendCards(data) {
        if (!this.cardContainer || !data || data.length === 0) return;

        const cardsHTML = data.map(item => this.createCardHTML(item)).join('');
        this.cardContainer.insertAdjacentHTML('beforeend', cardsHTML);
    }

    createCardHTML(item) {
        const isExpanded = this.expandedCards.has(item.id);
        const expandedClass = isExpanded ? 'expanded' : '';

        return `
            <div class="payment-card ${expandedClass}" data-id="${item.id}">
                <div class="payment-card-header">
                    <div class="card-header-content">
                        <div class="card-primary-info">
                            <div class="card-date">${this.formatDate(item.paymentDate)}</div>
                            <div class="card-time">${this.formatTime(item.paymentDate)}</div>
                        </div>
                        <div class="card-amount">
                            <div class="card-amount-value">+${this.formatAmount(item.interestAmount)}</div>
                            <div class="card-amount-currency">RZW</div>
                        </div>
                        <i class="fas fa-chevron-down card-expand-icon"></i>
                    </div>
                </div>
                <div class="payment-card-body">
                    <div class="card-body-content">
                        <div class="card-details-grid">
                            <div class="card-detail-item">
                                <div class="card-detail-label">Principal Amount</div>
                                <div class="card-detail-value amount">${this.formatAmount(item.principalAmount)} RZW</div>
                            </div>
                            <div class="card-detail-item">
                                <div class="card-detail-label">Total Balance</div>
                                <div class="card-detail-value amount">${this.formatAmount(item.totalBalance)} RZW</div>
                            </div>
                            <div class="card-detail-item">
                                <div class="card-detail-label">Interest Rate</div>
                                <div class="card-detail-value">${item.interestRate}% ${item.ratePeriod}</div>
                            </div>
                            <div class="card-detail-item">
                                <div class="card-detail-label">Status</div>
                                <div class="card-detail-value status">
                                    <span class="badge badge-${this.getStatusClass(item.status)}">${item.status}</span>
                                </div>
                            </div>
                        </div>

                        ${item.description ? `
                            <div class="card-description">
                                <p class="card-description-text">${item.description}</p>
                            </div>
                        ` : ''}

                        <div class="card-actions">
                            <button type="button" class="card-action-btn" data-action="view-details">
                                <i class="fas fa-eye"></i>
                                <span>Details</span>
                            </button>
                            <button type="button" class="card-action-btn" data-action="download-receipt">
                                <i class="fas fa-download"></i>
                                <span>Receipt</span>
                            </button>
                            <button type="button" class="card-action-btn primary" data-action="view-transaction">
                                <i class="fas fa-external-link-alt"></i>
                                <span>Transaction</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createLoadingCards() {
        return Array(3).fill(0).map(() => `
            <div class="payment-card loading">
                <div class="payment-card-header">
                    <div class="card-header-content">
                        <div class="card-primary-info">
                            <div class="card-date">Loading...</div>
                            <div class="card-time">Please wait</div>
                        </div>
                        <div class="card-amount">
                            <div class="card-amount-value">---</div>
                            <div class="card-amount-currency">RZW</div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    toggleCard(card) {
        const cardId = card.getAttribute('data-id');
        const isExpanded = card.classList.contains('expanded');

        if (isExpanded) {
            card.classList.remove('expanded');
            this.expandedCards.delete(cardId);
        } else {
            card.classList.add('expanded');
            this.expandedCards.add(cardId);
        }
    }

    handleCardAction(action, cardId) {
        switch (action) {
            case 'view-details':
                this.viewCardDetails(cardId);
                break;
            case 'download-receipt':
                this.downloadReceipt(cardId);
                break;
            case 'view-transaction':
                this.viewTransaction(cardId);
                break;
        }
    }

    async loadMoreCards() {
        this.currentPage++;
        await this.loadCards(true);
    }

    setupCardPagination() {
        // Initial setup - pagination will be updated based on data
    }

    updateCardPagination(hasMore) {
        const pagination = document.getElementById('cardPagination');
        if (!pagination) return;

        if (hasMore) {
            pagination.style.display = 'flex';
            this.loadMoreBtn.disabled = false;
        } else {
            pagination.style.display = 'none';
        }
    }

    updateSearchInfo(totalCount, filters) {
        const searchInfo = document.getElementById('cardSearchInfo');
        const searchText = document.getElementById('cardSearchText');

        if (!searchInfo || !searchText) return;

        const hasFilters = Object.values(filters).some(value => value && value !== '');

        if (hasFilters) {
            searchText.textContent = `Found ${totalCount} interest payments matching your search criteria`;
            searchInfo.style.display = 'block';
        } else {
            searchInfo.style.display = 'none';
        }
    }

    showEmptyState() {
        const emptyState = document.getElementById('cardsEmptyState');
        if (emptyState) {
            emptyState.style.display = 'block';
        }
        if (this.cardContainer) {
            this.cardContainer.style.display = 'none';
        }
    }

    hideEmptyState() {
        const emptyState = document.getElementById('cardsEmptyState');
        if (emptyState) {
            emptyState.style.display = 'none';
        }
        if (this.cardContainer) {
            this.cardContainer.style.display = 'block';
        }
    }

    showError(message) {
        if (this.cardContainer) {
            this.cardContainer.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    ${message}
                </div>
            `;
        }
    }

    // Utility methods
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('tr-TR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    formatTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleTimeString('tr-TR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    formatAmount(amount) {
        return parseFloat(amount).toLocaleString('tr-TR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 8
        }).replace(/\.?0+$/, '');
    }

    getStatusClass(status) {
        const statusMap = {
            'Completed': 'success',
            'Pending': 'warning',
            'Failed': 'danger',
            'Processing': 'info'
        };
        return statusMap[status] || 'secondary';
    }

    // Action handlers
    viewCardDetails(cardId) {
        // Implement view details functionality
        console.log('View details for card:', cardId);
    }

    downloadReceipt(cardId) {
        // Implement download receipt functionality
        console.log('Download receipt for card:', cardId);
    }

    viewTransaction(cardId) {
        // Implement view transaction functionality
        console.log('View transaction for card:', cardId);
    }

    // Public methods
    refreshCards() {
        if (this.currentView === 'card') {
            this.loadCards();
        }
    }

    getCurrentView() {
        return this.currentView;
    }

    expandAllCards() {
        document.querySelectorAll('.payment-card').forEach(card => {
            const cardId = card.getAttribute('data-id');
            card.classList.add('expanded');
            this.expandedCards.add(cardId);
        });
    }

    collapseAllCards() {
        document.querySelectorAll('.payment-card').forEach(card => {
            card.classList.remove('expanded');
        });
        this.expandedCards.clear();
    }
}

// Initialize card manager
InterestHistoryTable.prototype.initCardManager = function() {
    this.cardManager = new MobileCardManager(this);
};

// Update main initialization
document.addEventListener('DOMContentLoaded', function() {
    const accountId = window.rzwSavingsDetails?.accountId;
    if (accountId) {
        window.interestHistoryTable = new InterestHistoryTable(accountId);
        window.interestHistoryTable.initPagination();
        window.interestHistoryTable.initFilters();
        window.interestHistoryTable.initAdvancedSearch();
        window.interestHistoryTable.initExportManager();
        window.interestHistoryTable.initMobileManager();
        window.interestHistoryTable.initCardManager();
    }
});

// Global card functions
function switchToCardView() {
    if (window.interestHistoryTable?.cardManager) {
        window.interestHistoryTable.cardManager.switchView('card');
    }
}

function switchToTableView() {
    if (window.interestHistoryTable?.cardManager) {
        window.interestHistoryTable.cardManager.switchView('table');
    }
}

function refreshCardView() {
    if (window.interestHistoryTable?.cardManager) {
        window.interestHistoryTable.cardManager.refreshCards();
    }
}
```

### Sonraki Adım
Bu parça tamamlandıktan sonra **Parça *******.3: Touch Interactions** başlayacak.

---
**Tahmini Süre**: 15-20 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Parça *******.1 tamamlanmış olmalı

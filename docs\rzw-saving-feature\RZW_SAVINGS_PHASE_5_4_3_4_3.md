# Parça *******.3: Touch Interactions (15-20 dakika)

## 📋 <PERSON><PERSON><PERSON>ti
Mobile devices için touch interactions implementasyonu. Swipe gestures, touch feedback ve mobile navigation enhancements.

## 🎯 Hedefler
- ✅ Touch gestures (swipe, tap, hold)
- ✅ Swipe functionality for cards
- ✅ Mobile navigation enhancements
- ✅ Touch feedback improvements

## 📊 Yapılacak İşler

### ✅ YAPILACAKLAR

#### *******.3.1 Touch Gesture CSS

**Dosya**: `src/wwwroot/css/rzw-savings-details.css` (Touch interaction styles ekleme)
```css
/* Touch Interactions */

/* Touch Feedback */
.touch-feedback {
    position: relative;
    overflow: hidden;
}

.touch-feedback::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
    pointer-events: none;
    z-index: 1;
}

.touch-feedback.active::before {
    width: 200px;
    height: 200px;
}

/* Swipe Indicators */
.swipe-indicator {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(102, 126, 234, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.2s;
    pointer-events: none;
}

.swipe-indicator.left {
    left: 10px;
    animation: swipeIndicatorLeft 2s infinite;
}

.swipe-indicator.right {
    right: 10px;
    animation: swipeIndicatorRight 2s infinite;
}

.swipe-indicator.visible {
    opacity: 1;
}

@keyframes swipeIndicatorLeft {
    0%, 100% { transform: translateY(-50%) translateX(0); }
    50% { transform: translateY(-50%) translateX(-10px); }
}

@keyframes swipeIndicatorRight {
    0%, 100% { transform: translateY(-50%) translateX(0); }
    50% { transform: translateY(-50%) translateX(10px); }
}

/* Card Swipe Actions */
.payment-card {
    position: relative;
    transform: translateX(0);
    transition: transform 0.3s ease-out;
}

.payment-card.swiping {
    transition: none;
}

.payment-card.swipe-left {
    transform: translateX(-100px);
}

.payment-card.swipe-right {
    transform: translateX(100px);
}

.card-swipe-actions {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s;
}

.card-swipe-actions.left {
    left: -100px;
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.card-swipe-actions.right {
    right: -100px;
    background: linear-gradient(90deg, #28a745, #218838);
}

.payment-card.swipe-left .card-swipe-actions.left,
.payment-card.swipe-right .card-swipe-actions.right {
    opacity: 1;
}

.swipe-action-icon {
    color: white;
    font-size: 1.5rem;
    animation: swipeActionPulse 1s infinite;
}

@keyframes swipeActionPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Long Press Actions */
.long-press-menu {
    position: fixed;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.2);
    padding: 8px 0;
    z-index: 1000;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s;
    min-width: 180px;
}

.long-press-menu.visible {
    opacity: 1;
    transform: scale(1);
}

.long-press-menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: background 0.2s;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 0.9rem;
}

.long-press-menu-item:hover {
    background: #f8f9fa;
}

.long-press-menu-item i {
    width: 16px;
    color: #667eea;
}

.long-press-menu-item.danger {
    color: #dc3545;
}

.long-press-menu-item.danger i {
    color: #dc3545;
}

/* Touch Scroll Enhancements */
.touch-scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

.touch-scroll-container::-webkit-scrollbar {
    width: 4px;
    height: 4px;
}

.touch-scroll-container::-webkit-scrollbar-track {
    background: transparent;
}

.touch-scroll-container::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 2px;
}

.touch-scroll-container::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

/* Pull to Refresh */
.pull-to-refresh {
    position: relative;
    overflow: hidden;
}

.pull-refresh-indicator {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: top 0.3s;
    z-index: 10;
}

.pull-to-refresh.pulling .pull-refresh-indicator {
    top: 10px;
}

.pull-refresh-icon {
    color: #667eea;
    font-size: 1.2rem;
    transition: transform 0.3s;
}

.pull-to-refresh.refreshing .pull-refresh-icon {
    animation: refreshSpin 1s linear infinite;
}

@keyframes refreshSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Touch Selection */
.touch-selectable {
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
}

.touch-selectable.selecting {
    background: rgba(102, 126, 234, 0.1);
    border: 2px solid #667eea;
    border-radius: 8px;
}

/* Haptic Feedback Simulation */
.haptic-feedback {
    animation: hapticVibrate 0.1s;
}

@keyframes hapticVibrate {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-1px); }
    75% { transform: translateX(1px); }
}

/* Touch Accessibility */
@media (max-width: 768px) {
    /* Larger touch targets */
    .touch-target {
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Touch focus indicators */
    .touch-focusable:focus {
        outline: 3px solid #667eea;
        outline-offset: 2px;
        border-radius: 4px;
    }
    
    /* Touch hover states */
    .touch-hover:active {
        background: rgba(102, 126, 234, 0.1);
        transform: scale(0.98);
    }
}

/* Gesture Zones */
.gesture-zone {
    position: relative;
    touch-action: pan-y;
}

.gesture-zone.horizontal {
    touch-action: pan-x;
}

.gesture-zone.none {
    touch-action: none;
}

/* Touch Performance */
.touch-optimized {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Touch States */
.touch-active {
    transform: scale(0.98);
    opacity: 0.8;
}

.touch-disabled {
    pointer-events: none;
    opacity: 0.5;
}

/* Mobile Navigation Enhancements */
@media (max-width: 768px) {
    .mobile-nav-enhancement {
        position: sticky;
        bottom: 0;
        background: white;
        border-top: 1px solid #e9ecef;
        padding: 10px 15px;
        z-index: 20;
    }
    
    .mobile-nav-buttons {
        display: flex;
        gap: 10px;
        justify-content: space-between;
    }
    
    .mobile-nav-btn {
        flex: 1;
        padding: 12px;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        background: white;
        color: #495057;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
    }
    
    .mobile-nav-btn:active {
        transform: scale(0.98);
        background: #f8f9fa;
    }
    
    .mobile-nav-btn.primary {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }
    
    .mobile-nav-btn.primary:active {
        background: #5a6fd8;
    }
    
    .mobile-nav-btn i {
        font-size: 0.8rem;
    }
}

/* Touch Gesture Hints */
.gesture-hint {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 15;
    opacity: 0;
    animation: gestureHintFade 3s;
}

@keyframes gestureHintFade {
    0% { opacity: 0; }
    20% { opacity: 1; }
    80% { opacity: 1; }
    100% { opacity: 0; }
}

/* Touch Loading States */
.touch-loading {
    position: relative;
    pointer-events: none;
}

.touch-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: touchLoadingSpin 1s linear infinite;
    z-index: 10;
}

@keyframes touchLoadingSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
```

## 📋 Parça Kontrol Listesi

### ✅ YAPILACAKLAR
- [ ] Touch gesture CSS implementation
- [ ] Swipe actions styling
- [ ] Long press menu design
- [ ] Touch feedback animations
- [ ] Pull to refresh styling
- [ ] Touch JavaScript implementation
- [ ] Gesture event handlers
- [ ] Mobile navigation enhancements

### 🔄 YAPILMAKTA OLANLAR
- (Henüz başlanmadı)

### ✅ YAPILMIŞLAR
- (Henüz tamamlanmadı)

## 📝 Notlar

### Touch Interaction Features
- **Swipe gestures**: Left/right swipe for actions
- **Long press**: Context menu on long press
- **Touch feedback**: Visual feedback on touch
- **Pull to refresh**: Pull down to refresh data
- **Haptic simulation**: Visual haptic feedback

### User Experience
- **Intuitive gestures**: Natural touch interactions
- **Visual feedback**: Clear touch responses
- **Accessibility**: Proper touch target sizes
- **Performance**: Optimized for smooth interactions

#### *******.3.2 Touch JavaScript Implementation

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Touch interaction functionality ekleme)
```javascript
// Touch Interaction Manager
class TouchInteractionManager {
    constructor(cardManager) {
        this.cardManager = cardManager;
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.touchStartTime = 0;
        this.longPressTimer = null;
        this.longPressThreshold = 500; // ms
        this.swipeThreshold = 50; // px
        this.currentTouchElement = null;
        this.longPressMenu = null;
        this.pullToRefreshEnabled = true;
        this.pullStartY = 0;
        this.isPulling = false;

        this.init();
    }

    init() {
        this.createLongPressMenu();
        this.bindTouchEvents();
        this.setupPullToRefresh();
        this.initializeGestureHints();
    }

    bindTouchEvents() {
        const cardContainer = document.getElementById('interestPaymentCards');
        if (!cardContainer) return;

        // Touch events for cards
        cardContainer.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: false });
        cardContainer.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: false });
        cardContainer.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: false });
        cardContainer.addEventListener('touchcancel', this.handleTouchCancel.bind(this));

        // Click events for touch feedback
        cardContainer.addEventListener('click', this.handleTouchFeedback.bind(this));

        // Global touch events
        document.addEventListener('touchstart', this.handleGlobalTouchStart.bind(this));
        document.addEventListener('click', this.handleGlobalClick.bind(this));
    }

    handleTouchStart(e) {
        const card = e.target.closest('.payment-card');
        if (!card) return;

        this.currentTouchElement = card;
        this.touchStartX = e.touches[0].clientX;
        this.touchStartY = e.touches[0].clientY;
        this.touchStartTime = Date.now();

        // Add touch feedback
        this.addTouchFeedback(card, e.touches[0]);

        // Start long press timer
        this.longPressTimer = setTimeout(() => {
            this.handleLongPress(card, e.touches[0]);
        }, this.longPressThreshold);

        // Add swiping class
        card.classList.add('swiping');
    }

    handleTouchMove(e) {
        if (!this.currentTouchElement) return;

        const touchX = e.touches[0].clientX;
        const touchY = e.touches[0].clientY;
        const deltaX = touchX - this.touchStartX;
        const deltaY = touchY - this.touchStartY;

        // Clear long press if moved too much
        if (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10) {
            this.clearLongPress();
        }

        // Handle horizontal swipe
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            e.preventDefault();
            this.handleSwipeMove(deltaX);
        }

        // Handle pull to refresh
        if (this.pullToRefreshEnabled && this.touchStartY < 100 && deltaY > 0) {
            this.handlePullMove(deltaY);
        }
    }

    handleTouchEnd(e) {
        if (!this.currentTouchElement) return;

        const touchEndX = e.changedTouches[0].clientX;
        const touchEndY = e.changedTouches[0].clientY;
        const deltaX = touchEndX - this.touchStartX;
        const deltaY = touchEndY - this.touchStartY;
        const deltaTime = Date.now() - this.touchStartTime;

        this.clearLongPress();
        this.removeTouchFeedback();

        // Handle swipe
        if (Math.abs(deltaX) > this.swipeThreshold && deltaTime < 300) {
            this.handleSwipeEnd(deltaX);
        } else {
            this.resetCardPosition();
        }

        // Handle pull to refresh
        if (this.isPulling && deltaY > 80) {
            this.triggerRefresh();
        } else {
            this.resetPullToRefresh();
        }

        this.currentTouchElement.classList.remove('swiping');
        this.currentTouchElement = null;
    }

    handleTouchCancel(e) {
        this.clearLongPress();
        this.removeTouchFeedback();
        this.resetCardPosition();
        this.resetPullToRefresh();

        if (this.currentTouchElement) {
            this.currentTouchElement.classList.remove('swiping');
            this.currentTouchElement = null;
        }
    }

    handleSwipeMove(deltaX) {
        if (!this.currentTouchElement) return;

        const card = this.currentTouchElement;
        const maxSwipe = 100;
        const clampedDelta = Math.max(-maxSwipe, Math.min(maxSwipe, deltaX));

        card.style.transform = `translateX(${clampedDelta}px)`;

        // Show swipe actions
        if (Math.abs(clampedDelta) > 30) {
            if (clampedDelta < 0) {
                card.classList.add('swipe-left');
                card.classList.remove('swipe-right');
            } else {
                card.classList.add('swipe-right');
                card.classList.remove('swipe-left');
            }
        } else {
            card.classList.remove('swipe-left', 'swipe-right');
        }
    }

    handleSwipeEnd(deltaX) {
        if (!this.currentTouchElement) return;

        const card = this.currentTouchElement;
        const cardId = card.getAttribute('data-id');

        if (Math.abs(deltaX) > this.swipeThreshold) {
            if (deltaX < 0) {
                // Swipe left - Delete action
                this.handleSwipeAction('delete', cardId);
            } else {
                // Swipe right - Archive action
                this.handleSwipeAction('archive', cardId);
            }
        }

        this.resetCardPosition();
    }

    handleSwipeAction(action, cardId) {
        this.triggerHapticFeedback();

        switch (action) {
            case 'delete':
                this.showSwipeConfirmation('Delete this payment record?', () => {
                    this.deletePaymentRecord(cardId);
                });
                break;
            case 'archive':
                this.showSwipeConfirmation('Archive this payment record?', () => {
                    this.archivePaymentRecord(cardId);
                });
                break;
        }
    }

    resetCardPosition() {
        if (!this.currentTouchElement) return;

        this.currentTouchElement.style.transform = '';
        this.currentTouchElement.classList.remove('swipe-left', 'swipe-right');
    }

    handleLongPress(card, touch) {
        this.triggerHapticFeedback();
        this.showLongPressMenu(card, touch.clientX, touch.clientY);
    }

    addTouchFeedback(element, touch) {
        element.classList.add('touch-feedback', 'active');

        // Create ripple effect
        const ripple = document.createElement('div');
        ripple.className = 'touch-ripple';
        ripple.style.position = 'absolute';
        ripple.style.borderRadius = '50%';
        ripple.style.background = 'rgba(102, 126, 234, 0.3)';
        ripple.style.transform = 'scale(0)';
        ripple.style.animation = 'ripple 0.6s linear';
        ripple.style.pointerEvents = 'none';

        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = (touch.clientX - rect.left - size / 2) + 'px';
        ripple.style.top = (touch.clientY - rect.top - size / 2) + 'px';

        element.appendChild(ripple);

        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    removeTouchFeedback() {
        if (this.currentTouchElement) {
            this.currentTouchElement.classList.remove('touch-feedback', 'active');
        }
    }

    handleTouchFeedback(e) {
        const element = e.target.closest('.touch-target, .card-action-btn, .view-toggle-btn');
        if (element) {
            element.classList.add('haptic-feedback');
            setTimeout(() => {
                element.classList.remove('haptic-feedback');
            }, 100);
        }
    }

    clearLongPress() {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }

    createLongPressMenu() {
        this.longPressMenu = document.createElement('div');
        this.longPressMenu.className = 'long-press-menu';
        this.longPressMenu.innerHTML = `
            <button class="long-press-menu-item" data-action="view-details">
                <i class="fas fa-eye"></i>
                <span>View Details</span>
            </button>
            <button class="long-press-menu-item" data-action="download-receipt">
                <i class="fas fa-download"></i>
                <span>Download Receipt</span>
            </button>
            <button class="long-press-menu-item" data-action="copy-transaction">
                <i class="fas fa-copy"></i>
                <span>Copy Transaction ID</span>
            </button>
            <button class="long-press-menu-item" data-action="share">
                <i class="fas fa-share"></i>
                <span>Share</span>
            </button>
            <button class="long-press-menu-item danger" data-action="report-issue">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Report Issue</span>
            </button>
        `;

        document.body.appendChild(this.longPressMenu);

        // Bind menu events
        this.longPressMenu.addEventListener('click', (e) => {
            const item = e.target.closest('.long-press-menu-item');
            if (item) {
                const action = item.getAttribute('data-action');
                this.handleLongPressAction(action);
                this.hideLongPressMenu();
            }
        });
    }

    showLongPressMenu(card, x, y) {
        this.currentMenuCard = card;
        this.longPressMenu.style.left = x + 'px';
        this.longPressMenu.style.top = y + 'px';
        this.longPressMenu.classList.add('visible');

        // Adjust position if menu goes off screen
        setTimeout(() => {
            const rect = this.longPressMenu.getBoundingClientRect();
            if (rect.right > window.innerWidth) {
                this.longPressMenu.style.left = (x - rect.width) + 'px';
            }
            if (rect.bottom > window.innerHeight) {
                this.longPressMenu.style.top = (y - rect.height) + 'px';
            }
        }, 10);
    }

    hideLongPressMenu() {
        this.longPressMenu.classList.remove('visible');
        this.currentMenuCard = null;
    }

    handleLongPressAction(action) {
        if (!this.currentMenuCard) return;

        const cardId = this.currentMenuCard.getAttribute('data-id');

        switch (action) {
            case 'view-details':
                this.viewPaymentDetails(cardId);
                break;
            case 'download-receipt':
                this.downloadReceipt(cardId);
                break;
            case 'copy-transaction':
                this.copyTransactionId(cardId);
                break;
            case 'share':
                this.sharePayment(cardId);
                break;
            case 'report-issue':
                this.reportIssue(cardId);
                break;
        }
    }

    handleGlobalTouchStart(e) {
        // Hide long press menu if touching outside
        if (this.longPressMenu && this.longPressMenu.classList.contains('visible')) {
            if (!this.longPressMenu.contains(e.target)) {
                this.hideLongPressMenu();
            }
        }
    }

    handleGlobalClick(e) {
        // Hide long press menu on any click outside
        if (this.longPressMenu && this.longPressMenu.classList.contains('visible')) {
            if (!this.longPressMenu.contains(e.target)) {
                this.hideLongPressMenu();
            }
        }
    }

    setupPullToRefresh() {
        const cardView = document.getElementById('mobileCardView');
        if (!cardView) return;

        cardView.classList.add('pull-to-refresh');

        const indicator = document.createElement('div');
        indicator.className = 'pull-refresh-indicator';
        indicator.innerHTML = '<i class="fas fa-sync-alt pull-refresh-icon"></i>';
        cardView.appendChild(indicator);
    }

    handlePullMove(deltaY) {
        const cardView = document.getElementById('mobileCardView');
        if (!cardView || deltaY < 0) return;

        this.isPulling = true;
        cardView.classList.add('pulling');

        const indicator = cardView.querySelector('.pull-refresh-indicator');
        if (indicator) {
            const rotation = Math.min(deltaY * 2, 180);
            indicator.querySelector('.pull-refresh-icon').style.transform = `rotate(${rotation}deg)`;
        }
    }

    triggerRefresh() {
        const cardView = document.getElementById('mobileCardView');
        if (!cardView) return;

        cardView.classList.add('refreshing');
        this.triggerHapticFeedback();

        // Trigger actual refresh
        if (this.cardManager) {
            this.cardManager.refreshCards();
        }

        // Reset after delay
        setTimeout(() => {
            this.resetPullToRefresh();
        }, 1500);
    }

    resetPullToRefresh() {
        const cardView = document.getElementById('mobileCardView');
        if (!cardView) return;

        cardView.classList.remove('pulling', 'refreshing');
        this.isPulling = false;

        const indicator = cardView.querySelector('.pull-refresh-indicator');
        if (indicator) {
            indicator.querySelector('.pull-refresh-icon').style.transform = '';
        }
    }

    initializeGestureHints() {
        // Show gesture hints for first-time users
        if (!localStorage.getItem('rzw-gesture-hints-shown')) {
            setTimeout(() => {
                this.showGestureHints();
                localStorage.setItem('rzw-gesture-hints-shown', 'true');
            }, 2000);
        }
    }

    showGestureHints() {
        const hints = [
            { text: 'Swipe left to delete', delay: 0 },
            { text: 'Swipe right to archive', delay: 1500 },
            { text: 'Long press for menu', delay: 3000 },
            { text: 'Pull down to refresh', delay: 4500 }
        ];

        hints.forEach(hint => {
            setTimeout(() => {
                this.showGestureHint(hint.text);
            }, hint.delay);
        });
    }

    showGestureHint(text) {
        const hint = document.createElement('div');
        hint.className = 'gesture-hint';
        hint.textContent = text;

        const cardView = document.getElementById('mobileCardView');
        if (cardView) {
            cardView.appendChild(hint);
            setTimeout(() => {
                if (hint.parentNode) {
                    hint.parentNode.removeChild(hint);
                }
            }, 3000);
        }
    }

    triggerHapticFeedback() {
        // Trigger device haptic feedback if available
        if ('vibrate' in navigator) {
            navigator.vibrate(50);
        }
    }

    // Action handlers
    deletePaymentRecord(cardId) {
        console.log('Delete payment record:', cardId);
        // Implement delete functionality
    }

    archivePaymentRecord(cardId) {
        console.log('Archive payment record:', cardId);
        // Implement archive functionality
    }

    viewPaymentDetails(cardId) {
        console.log('View payment details:', cardId);
        // Implement view details functionality
    }

    downloadReceipt(cardId) {
        console.log('Download receipt:', cardId);
        // Implement download functionality
    }

    copyTransactionId(cardId) {
        console.log('Copy transaction ID:', cardId);
        // Implement copy functionality
    }

    sharePayment(cardId) {
        console.log('Share payment:', cardId);
        // Implement share functionality
    }

    reportIssue(cardId) {
        console.log('Report issue:', cardId);
        // Implement report functionality
    }

    showSwipeConfirmation(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }

    // Public methods
    enablePullToRefresh() {
        this.pullToRefreshEnabled = true;
    }

    disablePullToRefresh() {
        this.pullToRefreshEnabled = false;
    }

    resetGestureHints() {
        localStorage.removeItem('rzw-gesture-hints-shown');
    }
}

// Initialize touch interaction manager
MobileCardManager.prototype.initTouchInteractions = function() {
    this.touchManager = new TouchInteractionManager(this);
};

// Update card manager initialization
document.addEventListener('DOMContentLoaded', function() {
    const accountId = window.rzwSavingsDetails?.accountId;
    if (accountId) {
        window.interestHistoryTable = new InterestHistoryTable(accountId);
        window.interestHistoryTable.initPagination();
        window.interestHistoryTable.initFilters();
        window.interestHistoryTable.initAdvancedSearch();
        window.interestHistoryTable.initExportManager();
        window.interestHistoryTable.initMobileManager();
        window.interestHistoryTable.initCardManager();

        // Initialize touch interactions for mobile
        if (window.interestHistoryTable.cardManager) {
            window.interestHistoryTable.cardManager.initTouchInteractions();
        }
    }
});

// Global touch functions
function enableTouchGestures() {
    if (window.interestHistoryTable?.cardManager?.touchManager) {
        window.interestHistoryTable.cardManager.touchManager.enablePullToRefresh();
    }
}

function disableTouchGestures() {
    if (window.interestHistoryTable?.cardManager?.touchManager) {
        window.interestHistoryTable.cardManager.touchManager.disablePullToRefresh();
    }
}

function showGestureHints() {
    if (window.interestHistoryTable?.cardManager?.touchManager) {
        window.interestHistoryTable.cardManager.touchManager.showGestureHints();
    }
}
```

### Sonraki Adım
Bu parça ile **Alt Adım *******: Mobile Optimization** tamamlanmış olacak. Sonraki ana adım **Phase 5.4.4: Performance Optimization** olacak.

---
**Tahmini Süre**: 15-20 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Parça *******.2 tamamlanmış olmalı

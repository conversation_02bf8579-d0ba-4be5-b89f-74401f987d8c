# Phase 5.4.4: Performance Optimization (30-45 dakika)

## 📋 Phase Özeti
Interest history table için comprehensive performance optimization implementasyonu. Lazy loading, virtual scrolling, caching strategies ve bundle optimization.

## 🎯 Hedefler
- ✅ Lazy loading implementation
- ✅ Virtual scrolling for large datasets
- ✅ Caching strategies
- ✅ Bundle optimization
- ✅ Performance monitoring
- ✅ Memory management
- ✅ Network optimization

## 📊 Bu Phase 4 Alt Adıma Bölünmüştür

### **Alt Adım *********: Lazy Loading & Virtual Scrolling (10-15 dakika)
- Virtual scrolling implementation
- Lazy loading for cards
- Intersection Observer API
- Performance optimizations

### **Alt Adım *********: Caching Strategies (10-15 dakika)
- Browser caching
- Memory caching
- API response caching
- Cache invalidation

### **Alt Adım *********: Bundle & Network Optimization (10-15 dakika)
- Code splitting
- Resource compression
- Network request optimization
- Asset optimization

### **Alt Adım *********: Performance Monitoring (5-10 dakika)
- Performance metrics
- Memory usage monitoring
- Network performance tracking
- User experience metrics

## 📋 Phase Kontrol Listesi

### ✅ YAPILACAKLAR
- [x] **Alt Adım *********: Lazy Loading & Virtual Scrolling
- [x] **Alt Adım *********: Caching Strategies
- [x] **Alt Adım *********: Bundle & Network Optimization
- [x] **Alt Adım *********: Performance Monitoring

### 🔄 YAPILMAKTA OLANLAR
- (Tüm alt adımlar tamamlandı)

### ✅ YAPILMIŞLAR
- ✅ **Alt Adım *********: Lazy Loading & Virtual Scrolling (TAMAMLANDI)
- ✅ **Alt Adım *********: Caching Strategies (TAMAMLANDI)
- ✅ **Alt Adım *********: Bundle & Network Optimization (TAMAMLANDI)
- ✅ **Alt Adım *********: Performance Monitoring (TAMAMLANDI)

---

# Alt Adım *******: Lazy Loading & Virtual Scrolling (10-15 dakika)

## 📋 Alt Adım Özeti
Virtual scrolling ve lazy loading implementasyonu ile büyük veri setleri için performance optimization.

## 🎯 Hedefler
- ✅ Virtual scrolling implementation
- ✅ Lazy loading for cards
- ✅ Intersection Observer API
- ✅ Performance optimizations

## 📊 Implementation

### *******.1 Virtual Scrolling CSS

**Dosya**: `src/wwwroot/css/rzw-savings-details.css` (Virtual scrolling styles ekleme)
```css
/* Virtual Scrolling */
.virtual-scroll-container {
    height: 400px;
    overflow-y: auto;
    position: relative;
    contain: layout style paint;
}

.virtual-scroll-viewport {
    position: relative;
    overflow: hidden;
}

.virtual-scroll-spacer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    pointer-events: none;
}

.virtual-scroll-content {
    position: relative;
    will-change: transform;
}

/* Lazy Loading */
.lazy-load-container {
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lazy-load-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: lazyLoadSpin 1s linear infinite;
}

@keyframes lazyLoadSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.lazy-load-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: lazyLoadShimmer 1.5s infinite;
    border-radius: 8px;
    height: 120px;
    margin-bottom: 12px;
}

@keyframes lazyLoadShimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Intersection Observer */
.intersection-target {
    height: 1px;
    position: absolute;
    bottom: 200px;
    left: 0;
    right: 0;
    pointer-events: none;
}

/* Performance Optimizations */
.performance-optimized {
    contain: layout style paint;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
}

.gpu-accelerated {
    transform: translate3d(0, 0, 0);
    will-change: transform;
}

/* Memory Efficient */
.memory-efficient {
    contain: layout style;
}

.memory-efficient * {
    contain: layout;
}
```

### *******.2 Virtual Scrolling JavaScript

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Virtual scrolling implementation ekleme)
```javascript
// Virtual Scrolling Manager
class VirtualScrollManager {
    constructor(container, itemHeight = 120, bufferSize = 5) {
        this.container = container;
        this.itemHeight = itemHeight;
        this.bufferSize = bufferSize;
        this.scrollTop = 0;
        this.containerHeight = 0;
        this.totalItems = 0;
        this.visibleItems = [];
        this.startIndex = 0;
        this.endIndex = 0;
        this.data = [];
        this.renderCallback = null;
        
        this.init();
    }

    init() {
        this.setupContainer();
        this.bindEvents();
        this.updateDimensions();
    }

    setupContainer() {
        this.container.classList.add('virtual-scroll-container');
        this.container.innerHTML = `
            <div class="virtual-scroll-viewport">
                <div class="virtual-scroll-spacer"></div>
                <div class="virtual-scroll-content"></div>
            </div>
        `;
        
        this.viewport = this.container.querySelector('.virtual-scroll-viewport');
        this.spacer = this.container.querySelector('.virtual-scroll-spacer');
        this.content = this.container.querySelector('.virtual-scroll-content');
    }

    bindEvents() {
        this.container.addEventListener('scroll', this.handleScroll.bind(this));
        window.addEventListener('resize', this.debounce(this.updateDimensions.bind(this), 250));
    }

    handleScroll() {
        this.scrollTop = this.container.scrollTop;
        this.updateVisibleRange();
        this.renderVisibleItems();
    }

    updateDimensions() {
        this.containerHeight = this.container.clientHeight;
        this.updateVisibleRange();
        this.renderVisibleItems();
    }

    updateVisibleRange() {
        const visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
        this.startIndex = Math.max(0, Math.floor(this.scrollTop / this.itemHeight) - this.bufferSize);
        this.endIndex = Math.min(this.totalItems, this.startIndex + visibleCount + (this.bufferSize * 2));
    }

    setData(data, renderCallback) {
        this.data = data;
        this.totalItems = data.length;
        this.renderCallback = renderCallback;
        
        // Update spacer height
        this.spacer.style.height = (this.totalItems * this.itemHeight) + 'px';
        
        this.updateVisibleRange();
        this.renderVisibleItems();
    }

    renderVisibleItems() {
        if (!this.renderCallback) return;

        const fragment = document.createDocumentFragment();
        const offsetY = this.startIndex * this.itemHeight;
        
        for (let i = this.startIndex; i < this.endIndex; i++) {
            if (this.data[i]) {
                const item = this.renderCallback(this.data[i], i);
                if (item) {
                    item.style.position = 'absolute';
                    item.style.top = (i * this.itemHeight) + 'px';
                    item.style.width = '100%';
                    item.classList.add('virtual-scroll-item');
                    fragment.appendChild(item);
                }
            }
        }
        
        this.content.innerHTML = '';
        this.content.appendChild(fragment);
    }

    scrollToIndex(index) {
        const targetScrollTop = index * this.itemHeight;
        this.container.scrollTop = targetScrollTop;
    }

    getVisibleRange() {
        return { start: this.startIndex, end: this.endIndex };
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Lazy Loading Manager
class LazyLoadManager {
    constructor() {
        this.observers = new Map();
        this.loadingElements = new Set();
        this.loadedElements = new Set();
        this.intersectionObserver = null;
        
        this.init();
    }

    init() {
        this.setupIntersectionObserver();
    }

    setupIntersectionObserver() {
        const options = {
            root: null,
            rootMargin: '50px',
            threshold: 0.1
        };

        this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadElement(entry.target);
                }
            });
        }, options);
    }

    observe(element, loadCallback) {
        if (this.loadedElements.has(element)) return;
        
        this.observers.set(element, loadCallback);
        this.intersectionObserver.observe(element);
        
        // Add placeholder
        this.addPlaceholder(element);
    }

    unobserve(element) {
        this.intersectionObserver.unobserve(element);
        this.observers.delete(element);
        this.loadingElements.delete(element);
    }

    async loadElement(element) {
        if (this.loadingElements.has(element) || this.loadedElements.has(element)) return;
        
        this.loadingElements.add(element);
        const loadCallback = this.observers.get(element);
        
        if (loadCallback) {
            try {
                this.showLoadingState(element);
                await loadCallback(element);
                this.showLoadedState(element);
                this.loadedElements.add(element);
            } catch (error) {
                console.error('Lazy loading error:', error);
                this.showErrorState(element);
            } finally {
                this.loadingElements.delete(element);
                this.unobserve(element);
            }
        }
    }

    addPlaceholder(element) {
        if (!element.querySelector('.lazy-load-placeholder')) {
            const placeholder = document.createElement('div');
            placeholder.className = 'lazy-load-placeholder';
            element.appendChild(placeholder);
        }
    }

    showLoadingState(element) {
        const placeholder = element.querySelector('.lazy-load-placeholder');
        if (placeholder) {
            placeholder.innerHTML = `
                <div class="lazy-load-container">
                    <div class="lazy-load-spinner"></div>
                </div>
            `;
        }
    }

    showLoadedState(element) {
        const placeholder = element.querySelector('.lazy-load-placeholder');
        if (placeholder) {
            placeholder.remove();
        }
        element.classList.add('lazy-loaded');
    }

    showErrorState(element) {
        const placeholder = element.querySelector('.lazy-load-placeholder');
        if (placeholder) {
            placeholder.innerHTML = `
                <div class="lazy-load-container">
                    <div class="text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Failed to load content
                    </div>
                </div>
            `;
        }
    }

    destroy() {
        if (this.intersectionObserver) {
            this.intersectionObserver.disconnect();
        }
        this.observers.clear();
        this.loadingElements.clear();
        this.loadedElements.clear();
    }
}

// Performance Optimization Manager
class PerformanceOptimizationManager {
    constructor() {
        this.rafId = null;
        this.pendingUpdates = new Set();
        this.updateQueue = [];
        
        this.init();
    }

    init() {
        this.setupRequestAnimationFrame();
        this.optimizeElements();
    }

    setupRequestAnimationFrame() {
        const processUpdates = () => {
            if (this.updateQueue.length > 0) {
                const updates = this.updateQueue.splice(0, 10); // Process max 10 updates per frame
                updates.forEach(update => update());
            }
            
            this.rafId = requestAnimationFrame(processUpdates);
        };
        
        this.rafId = requestAnimationFrame(processUpdates);
    }

    scheduleUpdate(updateFunction) {
        if (!this.pendingUpdates.has(updateFunction)) {
            this.pendingUpdates.add(updateFunction);
            this.updateQueue.push(() => {
                updateFunction();
                this.pendingUpdates.delete(updateFunction);
            });
        }
    }

    optimizeElements() {
        // Add performance classes to elements
        document.querySelectorAll('.payment-card, .interest-history-table tr').forEach(element => {
            element.classList.add('performance-optimized');
        });

        // GPU acceleration for animations
        document.querySelectorAll('.card-expand-icon, .loading-spinner').forEach(element => {
            element.classList.add('gpu-accelerated');
        });

        // Memory efficient containers
        document.querySelectorAll('.table-container, .mobile-card-view').forEach(element => {
            element.classList.add('memory-efficient');
        });
    }

    destroy() {
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
        }
        this.pendingUpdates.clear();
        this.updateQueue.length = 0;
    }
}
```

## 📝 Notlar

### Virtual Scrolling Benefits
- **Memory efficiency**: Only renders visible items
- **Smooth scrolling**: Maintains 60fps performance
- **Large datasets**: Handles thousands of items
- **GPU acceleration**: Optimized rendering

### Lazy Loading Benefits
- **Faster initial load**: Content loads on demand
- **Reduced bandwidth**: Only loads visible content
- **Better UX**: Progressive content loading
- **Memory optimization**: Unloads off-screen content

### Performance Optimizations
- **RAF scheduling**: Batched DOM updates
- **CSS containment**: Isolated rendering contexts
- **GPU acceleration**: Hardware-accelerated animations
- **Memory management**: Efficient element lifecycle

# Alt Adım *******: Caching Strategies (10-15 dakika)

## 📋 Alt Adım Özeti
Comprehensive caching implementation için browser caching, memory caching ve API response caching strategies.

## 🎯 Hedefler
- ✅ Browser caching
- ✅ Memory caching
- ✅ API response caching
- ✅ Cache invalidation

## 📊 Implementation

### *******.1 Cache Manager JavaScript

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Caching implementation ekleme)
```javascript
// Cache Manager
class CacheManager {
    constructor() {
        this.memoryCache = new Map();
        this.cacheConfig = {
            maxSize: 100, // Maximum number of cached items
            ttl: 5 * 60 * 1000, // 5 minutes TTL
            maxAge: 30 * 60 * 1000 // 30 minutes max age
        };
        this.cacheStats = {
            hits: 0,
            misses: 0,
            evictions: 0
        };

        this.init();
    }

    init() {
        this.setupStorageCache();
        this.setupPeriodicCleanup();
    }

    setupStorageCache() {
        // Check if localStorage is available
        this.hasLocalStorage = this.isLocalStorageAvailable();

        // Check if sessionStorage is available
        this.hasSessionStorage = this.isSessionStorageAvailable();
    }

    setupPeriodicCleanup() {
        // Clean expired cache entries every 5 minutes
        setInterval(() => {
            this.cleanExpiredEntries();
        }, 5 * 60 * 1000);
    }

    // Memory Cache Methods
    set(key, value, options = {}) {
        const ttl = options.ttl || this.cacheConfig.ttl;
        const maxAge = options.maxAge || this.cacheConfig.maxAge;

        const cacheEntry = {
            value,
            timestamp: Date.now(),
            ttl,
            maxAge,
            accessCount: 0,
            lastAccessed: Date.now()
        };

        // Check cache size limit
        if (this.memoryCache.size >= this.cacheConfig.maxSize) {
            this.evictLeastRecentlyUsed();
        }

        this.memoryCache.set(key, cacheEntry);

        // Also store in localStorage if available and not sensitive
        if (this.hasLocalStorage && !options.sensitive) {
            this.setLocalStorage(key, cacheEntry);
        }
    }

    get(key) {
        // Try memory cache first
        let cacheEntry = this.memoryCache.get(key);

        // If not in memory, try localStorage
        if (!cacheEntry && this.hasLocalStorage) {
            cacheEntry = this.getLocalStorage(key);
            if (cacheEntry) {
                // Restore to memory cache
                this.memoryCache.set(key, cacheEntry);
            }
        }

        if (!cacheEntry) {
            this.cacheStats.misses++;
            return null;
        }

        // Check if expired
        if (this.isExpired(cacheEntry)) {
            this.delete(key);
            this.cacheStats.misses++;
            return null;
        }

        // Update access statistics
        cacheEntry.accessCount++;
        cacheEntry.lastAccessed = Date.now();
        this.cacheStats.hits++;

        return cacheEntry.value;
    }

    delete(key) {
        this.memoryCache.delete(key);
        if (this.hasLocalStorage) {
            this.deleteLocalStorage(key);
        }
    }

    clear() {
        this.memoryCache.clear();
        if (this.hasLocalStorage) {
            this.clearLocalStorage();
        }
    }

    // Cache Validation
    isExpired(cacheEntry) {
        const now = Date.now();
        const age = now - cacheEntry.timestamp;
        const timeSinceAccess = now - cacheEntry.lastAccessed;

        return age > cacheEntry.maxAge || timeSinceAccess > cacheEntry.ttl;
    }

    // Cache Eviction
    evictLeastRecentlyUsed() {
        let lruKey = null;
        let lruTime = Date.now();

        for (const [key, entry] of this.memoryCache) {
            if (entry.lastAccessed < lruTime) {
                lruTime = entry.lastAccessed;
                lruKey = key;
            }
        }

        if (lruKey) {
            this.delete(lruKey);
            this.cacheStats.evictions++;
        }
    }

    cleanExpiredEntries() {
        const keysToDelete = [];

        for (const [key, entry] of this.memoryCache) {
            if (this.isExpired(entry)) {
                keysToDelete.push(key);
            }
        }

        keysToDelete.forEach(key => this.delete(key));
    }

    // LocalStorage Methods
    setLocalStorage(key, cacheEntry) {
        try {
            const storageKey = `rzw_cache_${key}`;
            localStorage.setItem(storageKey, JSON.stringify(cacheEntry));
        } catch (error) {
            console.warn('Failed to set localStorage cache:', error);
        }
    }

    getLocalStorage(key) {
        try {
            const storageKey = `rzw_cache_${key}`;
            const stored = localStorage.getItem(storageKey);
            return stored ? JSON.parse(stored) : null;
        } catch (error) {
            console.warn('Failed to get localStorage cache:', error);
            return null;
        }
    }

    deleteLocalStorage(key) {
        try {
            const storageKey = `rzw_cache_${key}`;
            localStorage.removeItem(storageKey);
        } catch (error) {
            console.warn('Failed to delete localStorage cache:', error);
        }
    }

    clearLocalStorage() {
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('rzw_cache_')) {
                    localStorage.removeItem(key);
                }
            });
        } catch (error) {
            console.warn('Failed to clear localStorage cache:', error);
        }
    }

    // Utility Methods
    isLocalStorageAvailable() {
        try {
            const test = '__localStorage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (error) {
            return false;
        }
    }

    isSessionStorageAvailable() {
        try {
            const test = '__sessionStorage_test__';
            sessionStorage.setItem(test, test);
            sessionStorage.removeItem(test);
            return true;
        } catch (error) {
            return false;
        }
    }

    // Cache Statistics
    getStats() {
        const hitRate = this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) * 100;
        return {
            ...this.cacheStats,
            hitRate: isNaN(hitRate) ? 0 : hitRate.toFixed(2),
            memorySize: this.memoryCache.size,
            maxSize: this.cacheConfig.maxSize
        };
    }

    // Cache Keys Management
    generateKey(prefix, params) {
        const paramString = typeof params === 'object' ?
            Object.keys(params).sort().map(key => `${key}=${params[key]}`).join('&') :
            String(params);
        return `${prefix}_${this.hashCode(paramString)}`;
    }

    hashCode(str) {
        let hash = 0;
        if (str.length === 0) return hash;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return Math.abs(hash).toString(36);
    }
}

// API Cache Manager
class ApiCacheManager extends CacheManager {
    constructor() {
        super();
        this.pendingRequests = new Map();
        this.requestQueue = [];
        this.maxConcurrentRequests = 5;
        this.activeRequests = 0;
    }

    async cachedFetch(url, options = {}) {
        const cacheKey = this.generateKey('api', { url, ...options });

        // Check cache first
        const cached = this.get(cacheKey);
        if (cached && !options.bypassCache) {
            return cached;
        }

        // Check if request is already pending
        if (this.pendingRequests.has(cacheKey)) {
            return this.pendingRequests.get(cacheKey);
        }

        // Create request promise
        const requestPromise = this.executeRequest(url, options, cacheKey);
        this.pendingRequests.set(cacheKey, requestPromise);

        try {
            const result = await requestPromise;

            // Cache successful responses
            if (result && !result.error) {
                this.set(cacheKey, result, {
                    ttl: options.cacheTtl || this.cacheConfig.ttl,
                    sensitive: options.sensitive || false
                });
            }

            return result;
        } finally {
            this.pendingRequests.delete(cacheKey);
        }
    }

    async executeRequest(url, options, cacheKey) {
        // Queue request if too many concurrent requests
        if (this.activeRequests >= this.maxConcurrentRequests) {
            await this.queueRequest();
        }

        this.activeRequests++;

        try {
            const response = await fetch(url, {
                ...options,
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            return { error: error.message };
        } finally {
            this.activeRequests--;
            this.processQueue();
        }
    }

    queueRequest() {
        return new Promise(resolve => {
            this.requestQueue.push(resolve);
        });
    }

    processQueue() {
        if (this.requestQueue.length > 0 && this.activeRequests < this.maxConcurrentRequests) {
            const resolve = this.requestQueue.shift();
            resolve();
        }
    }

    invalidatePattern(pattern) {
        const keysToDelete = [];

        for (const key of this.memoryCache.keys()) {
            if (key.includes(pattern)) {
                keysToDelete.push(key);
            }
        }

        keysToDelete.forEach(key => this.delete(key));
    }

    preloadData(urls, options = {}) {
        const preloadPromises = urls.map(url =>
            this.cachedFetch(url, { ...options, priority: 'low' })
        );

        return Promise.allSettled(preloadPromises);
    }
}

// Initialize cache managers
const globalCacheManager = new CacheManager();
const apiCacheManager = new ApiCacheManager();

// Export for global use
window.cacheManager = globalCacheManager;
window.apiCacheManager = apiCacheManager;
```

# Alt Adım *******: Bundle & Network Optimization (10-15 dakika)

## 📋 Alt Adım Özeti
Code splitting, resource compression ve network request optimization implementation.

## 🎯 Hedefler
- ✅ Code splitting
- ✅ Resource compression
- ✅ Network request optimization
- ✅ Asset optimization

## 📊 Implementation

### *******.1 Network Optimization JavaScript

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Network optimization ekleme)
```javascript
// Network Optimization Manager
class NetworkOptimizationManager {
    constructor() {
        this.connectionType = this.getConnectionType();
        this.isOnline = navigator.onLine;
        this.requestQueue = [];
        this.retryQueue = [];
        this.maxRetries = 3;
        this.retryDelay = 1000;

        this.init();
    }

    init() {
        this.bindNetworkEvents();
        this.setupRequestOptimization();
        this.startQueueProcessor();
    }

    bindNetworkEvents() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.processRetryQueue();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
        });

        // Monitor connection changes
        if ('connection' in navigator) {
            navigator.connection.addEventListener('change', () => {
                this.connectionType = this.getConnectionType();
                this.adjustRequestStrategy();
            });
        }
    }

    getConnectionType() {
        if ('connection' in navigator) {
            return {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt,
                saveData: navigator.connection.saveData
            };
        }
        return { effectiveType: 'unknown' };
    }

    adjustRequestStrategy() {
        const { effectiveType, saveData } = this.connectionType;

        if (saveData || effectiveType === 'slow-2g' || effectiveType === '2g') {
            // Reduce request frequency and size
            this.enableDataSavingMode();
        } else {
            this.disableDataSavingMode();
        }
    }

    enableDataSavingMode() {
        // Reduce image quality, disable auto-refresh, etc.
        document.body.classList.add('data-saving-mode');
    }

    disableDataSavingMode() {
        document.body.classList.remove('data-saving-mode');
    }

    async optimizedFetch(url, options = {}) {
        if (!this.isOnline) {
            return this.handleOfflineRequest(url, options);
        }

        const optimizedOptions = this.optimizeRequestOptions(options);

        try {
            const response = await this.executeWithRetry(url, optimizedOptions);
            return response;
        } catch (error) {
            return this.handleRequestError(error, url, options);
        }
    }

    optimizeRequestOptions(options) {
        const optimized = { ...options };

        // Add compression headers
        optimized.headers = {
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept': 'application/json',
            ...optimized.headers
        };

        // Add cache control for GET requests
        if (!optimized.method || optimized.method === 'GET') {
            optimized.headers['Cache-Control'] = 'max-age=300'; // 5 minutes
        }

        // Add request priority
        if ('priority' in Request.prototype) {
            optimized.priority = options.priority || 'auto';
        }

        return optimized;
    }

    async executeWithRetry(url, options, retryCount = 0) {
        try {
            const response = await fetch(url, options);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response;
        } catch (error) {
            if (retryCount < this.maxRetries && this.shouldRetry(error)) {
                await this.delay(this.retryDelay * Math.pow(2, retryCount));
                return this.executeWithRetry(url, options, retryCount + 1);
            }
            throw error;
        }
    }

    shouldRetry(error) {
        // Retry on network errors, timeouts, and 5xx status codes
        return error.name === 'TypeError' ||
               error.message.includes('Failed to fetch') ||
               error.message.includes('5');
    }

    handleOfflineRequest(url, options) {
        // Queue request for when online
        return new Promise((resolve, reject) => {
            this.retryQueue.push({ url, options, resolve, reject });
        });
    }

    handleRequestError(error, url, options) {
        console.error('Request failed:', error);

        // Return cached data if available
        const cacheKey = apiCacheManager.generateKey('api', { url, ...options });
        const cached = apiCacheManager.get(cacheKey);

        if (cached) {
            console.warn('Using stale cached data due to network error');
            return cached;
        }

        throw error;
    }

    processRetryQueue() {
        const queue = [...this.retryQueue];
        this.retryQueue = [];

        queue.forEach(async ({ url, options, resolve, reject }) => {
            try {
                const response = await this.optimizedFetch(url, options);
                resolve(response);
            } catch (error) {
                reject(error);
            }
        });
    }

    startQueueProcessor() {
        setInterval(() => {
            if (this.isOnline && this.retryQueue.length > 0) {
                this.processRetryQueue();
            }
        }, 5000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Resource preloading
    preloadCriticalResources() {
        const criticalResources = [
            '/css/rzw-savings-details.css',
            '/js/rzw-savings-details.js'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    }

    // Image optimization
    optimizeImages() {
        const images = document.querySelectorAll('img[data-src]');

        images.forEach(img => {
            const { effectiveType } = this.connectionType;
            let quality = 'high';

            if (effectiveType === 'slow-2g' || effectiveType === '2g') {
                quality = 'low';
            } else if (effectiveType === '3g') {
                quality = 'medium';
            }

            const src = img.dataset.src;
            img.src = this.addQualityParam(src, quality);
        });
    }

    addQualityParam(url, quality) {
        const separator = url.includes('?') ? '&' : '?';
        return `${url}${separator}quality=${quality}`;
    }
}

// Bundle Optimization Manager
class BundleOptimizationManager {
    constructor() {
        this.loadedModules = new Set();
        this.moduleCache = new Map();
        this.loadingPromises = new Map();

        this.init();
    }

    init() {
        this.setupDynamicImports();
        this.optimizeInitialLoad();
    }

    async loadModule(moduleName, moduleUrl) {
        if (this.loadedModules.has(moduleName)) {
            return this.moduleCache.get(moduleName);
        }

        if (this.loadingPromises.has(moduleName)) {
            return this.loadingPromises.get(moduleName);
        }

        const loadPromise = this.dynamicImport(moduleUrl);
        this.loadingPromises.set(moduleName, loadPromise);

        try {
            const module = await loadPromise;
            this.moduleCache.set(moduleName, module);
            this.loadedModules.add(moduleName);
            return module;
        } finally {
            this.loadingPromises.delete(moduleName);
        }
    }

    async dynamicImport(moduleUrl) {
        // Fallback for browsers without dynamic import support
        if (typeof import === 'function') {
            return import(moduleUrl);
        } else {
            return this.loadScriptModule(moduleUrl);
        }
    }

    loadScriptModule(moduleUrl) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = moduleUrl;
            script.onload = () => resolve(window.lastLoadedModule);
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    setupDynamicImports() {
        // Load modules on demand
        document.addEventListener('click', async (e) => {
            const target = e.target.closest('[data-module]');
            if (target) {
                const moduleName = target.dataset.module;
                const moduleUrl = target.dataset.moduleUrl;

                if (moduleUrl) {
                    try {
                        await this.loadModule(moduleName, moduleUrl);
                    } catch (error) {
                        console.error(`Failed to load module ${moduleName}:`, error);
                    }
                }
            }
        });
    }

    optimizeInitialLoad() {
        // Defer non-critical JavaScript
        const deferredScripts = document.querySelectorAll('script[data-defer]');

        deferredScripts.forEach(script => {
            const newScript = document.createElement('script');
            newScript.src = script.src;
            newScript.defer = true;

            // Load after initial page load
            window.addEventListener('load', () => {
                document.head.appendChild(newScript);
            });
        });
    }

    // Code splitting utilities
    splitByRoute(routes) {
        routes.forEach(route => {
            if (window.location.pathname.includes(route.path)) {
                this.loadModule(route.name, route.moduleUrl);
            }
        });
    }

    splitByFeature(features) {
        features.forEach(feature => {
            const elements = document.querySelectorAll(feature.selector);
            if (elements.length > 0) {
                this.loadModule(feature.name, feature.moduleUrl);
            }
        });
    }
}

// Initialize optimization managers
const networkOptimizer = new NetworkOptimizationManager();
const bundleOptimizer = new BundleOptimizationManager();

// Export for global use
window.networkOptimizer = networkOptimizer;
window.bundleOptimizer = bundleOptimizer;
```

# Alt Adım *******: Performance Monitoring (5-10 dakika)

## 📋 Alt Adım Özeti
Performance metrics, memory usage monitoring ve user experience tracking implementation.

## 🎯 Hedefler
- ✅ Performance metrics
- ✅ Memory usage monitoring
- ✅ Network performance tracking
- ✅ User experience metrics

## 📊 Implementation

### *******.1 Performance Monitoring JavaScript

**Dosya**: `src/wwwroot/js/rzw-savings-details.js` (Performance monitoring ekleme)
```javascript
// Performance Monitoring Manager
class PerformanceMonitoringManager {
    constructor() {
        this.metrics = {
            pageLoad: {},
            userInteractions: [],
            apiRequests: [],
            memoryUsage: [],
            errors: []
        };
        this.observers = {};
        this.startTime = performance.now();

        this.init();
    }

    init() {
        this.setupPerformanceObservers();
        this.trackPageLoad();
        this.trackUserInteractions();
        this.trackMemoryUsage();
        this.trackErrors();
        this.startPeriodicReporting();
    }

    setupPerformanceObservers() {
        // Performance Observer for navigation timing
        if ('PerformanceObserver' in window) {
            try {
                const navObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.recordNavigationTiming(entry);
                    }
                });
                navObserver.observe({ entryTypes: ['navigation'] });
                this.observers.navigation = navObserver;

                // Performance Observer for resource timing
                const resourceObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.recordResourceTiming(entry);
                    }
                });
                resourceObserver.observe({ entryTypes: ['resource'] });
                this.observers.resource = resourceObserver;

                // Performance Observer for user timing
                const userObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        this.recordUserTiming(entry);
                    }
                });
                userObserver.observe({ entryTypes: ['measure', 'mark'] });
                this.observers.user = userObserver;

                // Performance Observer for long tasks
                if ('longtask' in PerformanceObserver.supportedEntryTypes) {
                    const longTaskObserver = new PerformanceObserver((list) => {
                        for (const entry of list.getEntries()) {
                            this.recordLongTask(entry);
                        }
                    });
                    longTaskObserver.observe({ entryTypes: ['longtask'] });
                    this.observers.longtask = longTaskObserver;
                }
            } catch (error) {
                console.warn('Performance Observer not supported:', error);
            }
        }
    }

    trackPageLoad() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                this.recordPageLoadMetrics();
            }, 0);
        });

        // Track First Contentful Paint
        if ('PerformanceObserver' in window) {
            try {
                const paintObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.name === 'first-contentful-paint') {
                            this.metrics.pageLoad.firstContentfulPaint = entry.startTime;
                        }
                    }
                });
                paintObserver.observe({ entryTypes: ['paint'] });
                this.observers.paint = paintObserver;
            } catch (error) {
                console.warn('Paint timing not supported:', error);
            }
        }

        // Track Largest Contentful Paint
        if ('LargestContentfulPaint' in window) {
            try {
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    this.metrics.pageLoad.largestContentfulPaint = lastEntry.startTime;
                });
                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
                this.observers.lcp = lcpObserver;
            } catch (error) {
                console.warn('LCP not supported:', error);
            }
        }

        // Track Cumulative Layout Shift
        if ('LayoutShift' in window) {
            try {
                let clsValue = 0;
                const clsObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (!entry.hadRecentInput) {
                            clsValue += entry.value;
                        }
                    }
                    this.metrics.pageLoad.cumulativeLayoutShift = clsValue;
                });
                clsObserver.observe({ entryTypes: ['layout-shift'] });
                this.observers.cls = clsObserver;
            } catch (error) {
                console.warn('CLS not supported:', error);
            }
        }
    }

    trackUserInteractions() {
        // Track click interactions
        document.addEventListener('click', (e) => {
            this.recordInteraction('click', e.target, performance.now());
        });

        // Track input interactions
        document.addEventListener('input', (e) => {
            this.recordInteraction('input', e.target, performance.now());
        });

        // Track scroll interactions
        let scrollTimeout;
        document.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                this.recordInteraction('scroll', document.documentElement, performance.now());
            }, 100);
        });

        // Track First Input Delay
        if ('PerformanceEventTiming' in window) {
            try {
                const fidObserver = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.processingStart && entry.startTime) {
                            const fid = entry.processingStart - entry.startTime;
                            this.metrics.pageLoad.firstInputDelay = fid;
                        }
                    }
                });
                fidObserver.observe({ entryTypes: ['first-input'] });
                this.observers.fid = fidObserver;
            } catch (error) {
                console.warn('FID not supported:', error);
            }
        }
    }

    trackMemoryUsage() {
        if ('memory' in performance) {
            setInterval(() => {
                const memInfo = {
                    timestamp: Date.now(),
                    usedJSHeapSize: performance.memory.usedJSHeapSize,
                    totalJSHeapSize: performance.memory.totalJSHeapSize,
                    jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
                };

                this.metrics.memoryUsage.push(memInfo);

                // Keep only last 100 entries
                if (this.metrics.memoryUsage.length > 100) {
                    this.metrics.memoryUsage.shift();
                }

                // Check for memory leaks
                this.checkMemoryLeaks(memInfo);
            }, 30000); // Every 30 seconds
        }
    }

    trackErrors() {
        window.addEventListener('error', (e) => {
            this.recordError({
                type: 'javascript',
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                stack: e.error ? e.error.stack : null,
                timestamp: Date.now()
            });
        });

        window.addEventListener('unhandledrejection', (e) => {
            this.recordError({
                type: 'promise',
                message: e.reason ? e.reason.toString() : 'Unhandled promise rejection',
                stack: e.reason ? e.reason.stack : null,
                timestamp: Date.now()
            });
        });
    }

    recordNavigationTiming(entry) {
        this.metrics.pageLoad.navigation = {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            domInteractive: entry.domInteractive - entry.fetchStart,
            domComplete: entry.domComplete - entry.fetchStart,
            networkLatency: entry.responseEnd - entry.fetchStart,
            serverResponseTime: entry.responseEnd - entry.requestStart,
            pageProcessingTime: entry.loadEventEnd - entry.responseEnd
        };
    }

    recordResourceTiming(entry) {
        if (entry.name.includes('rzw-savings')) {
            const resourceMetric = {
                name: entry.name,
                type: this.getResourceType(entry.name),
                duration: entry.duration,
                size: entry.transferSize || 0,
                cached: entry.transferSize === 0 && entry.decodedBodySize > 0,
                timestamp: entry.startTime
            };

            this.metrics.apiRequests.push(resourceMetric);
        }
    }

    recordUserTiming(entry) {
        if (!this.metrics.pageLoad.userTiming) {
            this.metrics.pageLoad.userTiming = [];
        }

        this.metrics.pageLoad.userTiming.push({
            name: entry.name,
            type: entry.entryType,
            duration: entry.duration || 0,
            startTime: entry.startTime
        });
    }

    recordLongTask(entry) {
        if (!this.metrics.pageLoad.longTasks) {
            this.metrics.pageLoad.longTasks = [];
        }

        this.metrics.pageLoad.longTasks.push({
            duration: entry.duration,
            startTime: entry.startTime,
            attribution: entry.attribution
        });
    }

    recordPageLoadMetrics() {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            this.recordNavigationTiming(navigation);
        }

        // Record custom metrics
        this.metrics.pageLoad.timeToInteractive = this.calculateTimeToInteractive();
        this.metrics.pageLoad.totalPageSize = this.calculateTotalPageSize();
        this.metrics.pageLoad.resourceCount = performance.getEntriesByType('resource').length;
    }

    recordInteraction(type, target, timestamp) {
        const interaction = {
            type,
            target: this.getElementSelector(target),
            timestamp,
            pageX: window.pageXOffset,
            pageY: window.pageYOffset
        };

        this.metrics.userInteractions.push(interaction);

        // Keep only last 50 interactions
        if (this.metrics.userInteractions.length > 50) {
            this.metrics.userInteractions.shift();
        }
    }

    recordError(error) {
        this.metrics.errors.push(error);

        // Keep only last 20 errors
        if (this.metrics.errors.length > 20) {
            this.metrics.errors.shift();
        }

        // Send critical errors immediately
        if (this.isCriticalError(error)) {
            this.sendErrorReport(error);
        }
    }

    checkMemoryLeaks(currentMemory) {
        const memoryHistory = this.metrics.memoryUsage;
        if (memoryHistory.length < 10) return;

        const recentMemory = memoryHistory.slice(-10);
        const avgIncrease = recentMemory.reduce((sum, mem, index) => {
            if (index === 0) return 0;
            return sum + (mem.usedJSHeapSize - recentMemory[index - 1].usedJSHeapSize);
        }, 0) / 9;

        // If memory consistently increases by more than 1MB per measurement
        if (avgIncrease > 1024 * 1024) {
            console.warn('Potential memory leak detected');
            this.recordError({
                type: 'memory-leak',
                message: `Average memory increase: ${(avgIncrease / 1024 / 1024).toFixed(2)}MB`,
                timestamp: Date.now()
            });
        }
    }

    calculateTimeToInteractive() {
        // Simplified TTI calculation
        const navigation = performance.getEntriesByType('navigation')[0];
        if (!navigation) return null;

        return navigation.domInteractive - navigation.fetchStart;
    }

    calculateTotalPageSize() {
        const resources = performance.getEntriesByType('resource');
        return resources.reduce((total, resource) => {
            return total + (resource.transferSize || 0);
        }, 0);
    }

    getResourceType(url) {
        if (url.includes('.css')) return 'stylesheet';
        if (url.includes('.js')) return 'script';
        if (url.includes('.png') || url.includes('.jpg') || url.includes('.gif')) return 'image';
        if (url.includes('/api/')) return 'api';
        return 'other';
    }

    getElementSelector(element) {
        if (!element) return 'unknown';

        let selector = element.tagName.toLowerCase();
        if (element.id) selector += `#${element.id}`;
        if (element.className) selector += `.${element.className.split(' ').join('.')}`;

        return selector;
    }

    isCriticalError(error) {
        return error.type === 'javascript' &&
               (error.message.includes('ReferenceError') ||
                error.message.includes('TypeError') ||
                error.message.includes('SyntaxError'));
    }

    startPeriodicReporting() {
        // Send performance report every 5 minutes
        setInterval(() => {
            this.sendPerformanceReport();
        }, 5 * 60 * 1000);

        // Send report before page unload
        window.addEventListener('beforeunload', () => {
            this.sendPerformanceReport(true);
        });
    }

    sendPerformanceReport(isBeforeUnload = false) {
        const report = {
            sessionId: this.getSessionId(),
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            metrics: this.metrics,
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            connection: this.getConnectionInfo()
        };

        if (isBeforeUnload && 'sendBeacon' in navigator) {
            navigator.sendBeacon('/api/performance/report', JSON.stringify(report));
        } else {
            this.sendReportAsync(report);
        }
    }

    async sendReportAsync(report) {
        try {
            await fetch('/api/performance/report', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(report)
            });
        } catch (error) {
            console.warn('Failed to send performance report:', error);
        }
    }

    sendErrorReport(error) {
        const report = {
            sessionId: this.getSessionId(),
            timestamp: Date.now(),
            url: window.location.href,
            error: error
        };

        if ('sendBeacon' in navigator) {
            navigator.sendBeacon('/api/performance/error', JSON.stringify(report));
        }
    }

    getSessionId() {
        let sessionId = sessionStorage.getItem('rzw-session-id');
        if (!sessionId) {
            sessionId = 'sess_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            sessionStorage.setItem('rzw-session-id', sessionId);
        }
        return sessionId;
    }

    getConnectionInfo() {
        if ('connection' in navigator) {
            return {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt,
                saveData: navigator.connection.saveData
            };
        }
        return null;
    }

    // Public methods for manual tracking
    markStart(name) {
        performance.mark(`${name}-start`);
    }

    markEnd(name) {
        performance.mark(`${name}-end`);
        performance.measure(name, `${name}-start`, `${name}-end`);
    }

    trackCustomMetric(name, value, unit = '') {
        if (!this.metrics.custom) {
            this.metrics.custom = {};
        }

        this.metrics.custom[name] = {
            value,
            unit,
            timestamp: Date.now()
        };
    }

    getMetrics() {
        return { ...this.metrics };
    }

    clearMetrics() {
        this.metrics = {
            pageLoad: {},
            userInteractions: [],
            apiRequests: [],
            memoryUsage: [],
            errors: []
        };
    }

    destroy() {
        // Disconnect all observers
        Object.values(this.observers).forEach(observer => {
            if (observer && observer.disconnect) {
                observer.disconnect();
            }
        });

        // Send final report
        this.sendPerformanceReport(true);
    }
}

// Initialize performance monitoring
const performanceMonitor = new PerformanceMonitoringManager();

// Export for global use
window.performanceMonitor = performanceMonitor;

// Integration with existing managers
document.addEventListener('DOMContentLoaded', function() {
    const accountId = window.rzwSavingsDetails?.accountId;
    if (accountId) {
        // Mark start of table initialization
        performanceMonitor.markStart('interest-history-init');

        window.interestHistoryTable = new InterestHistoryTable(accountId);
        window.interestHistoryTable.initPagination();
        window.interestHistoryTable.initFilters();
        window.interestHistoryTable.initAdvancedSearch();
        window.interestHistoryTable.initExportManager();
        window.interestHistoryTable.initMobileManager();
        window.interestHistoryTable.initCardManager();

        // Initialize touch interactions for mobile
        if (window.interestHistoryTable.cardManager) {
            window.interestHistoryTable.cardManager.initTouchInteractions();
        }

        // Mark end of table initialization
        performanceMonitor.markEnd('interest-history-init');

        // Track custom metrics
        performanceMonitor.trackCustomMetric('account-id', accountId);
        performanceMonitor.trackCustomMetric('features-loaded', 6, 'count');
    }
});
```

## 📝 Notlar

### Performance Monitoring Benefits
- **Real-time metrics**: Live performance tracking
- **User experience**: Core Web Vitals monitoring
- **Error tracking**: Automatic error reporting
- **Memory monitoring**: Memory leak detection
- **Network tracking**: API performance analysis

### Monitored Metrics
- **Page Load**: FCP, LCP, CLS, FID, TTI
- **User Interactions**: Clicks, inputs, scrolls
- **API Requests**: Response times, success rates
- **Memory Usage**: Heap size, leak detection
- **Errors**: JavaScript errors, promise rejections

### Reporting Features
- **Periodic reports**: Every 5 minutes
- **Before unload**: Final report on page exit
- **Critical errors**: Immediate error reporting
- **Session tracking**: User session correlation
- **Connection aware**: Network condition tracking

---

## 🎉 Phase 5.4.4 TAMAMLANDI!

**Performance Optimization** phase'i başarıyla tamamlandı. Tüm alt adımlar implement edildi:

### ✅ **Tamamlanan Alt Adımlar:**
- **Alt Adım *********: Lazy Loading & Virtual Scrolling
- **Alt Adım *********: Caching Strategies
- **Alt Adım *********: Bundle & Network Optimization
- **Alt Adım *********: Performance Monitoring

### 🚀 **Sonraki Phase:**
**Phase 5.5: Final Integration & Testing** (45-60 dakika)

---
**Tahmini Süre**: 30-45 dakika
**Öncelik**: Yüksek
**Bağımlılıklar**: Phase 5.4.1, 5.4.2, 5.4.3 tamamlanmış olmalı

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("BALANCE_TRANSACTION")]
    public class BalanceTransaction
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("USER_ID")]
        public int UserId { get; set; }

        [Required]
        [Column("TYPE")]
        [StringLength(50)]
        public string TransactionType { get; set; } = null!;

        [Required]
        [Column("AMOUNT", TypeName = "decimal(20,2)")]
        public decimal Amount { get; set; }

        [Required]
        [Column("PREVIOUS_BALANCE", TypeName = "decimal(20,2)")]
        public decimal PreviousBalance { get; set; }

        [Required]
        [Column("NEW_BALANCE", TypeName = "decimal(20,2)")]
        public decimal NewBalance { get; set; }

        [Column("REFERENCE_ID")]
        public int? ReferenceId { get; set; }

        [Column("REFERENCE_TYPE")]
        [StringLength(50)]
        public string? ReferenceType { get; set; }

        [Column("REFERRAL_REWARD_ID")]
        public int? ReferralRewardId { get; set; }

        [Column("DESCRIPTION")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Required]
        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Required]
        [Column("IS_ACTIVE")]
        public bool IsActive { get; set; } = true;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("ReferralRewardId")]
        public virtual ReferralReward? ReferralReward { get; set; }
    }

    public static class TransactionType
    {
        public const string Deposit = "Deposit";
        public const string Withdrawal = "Withdrawal";
        public const string PackagePurchase = "Package Purchase";
        public const string TradeProfit = "Trade Profit";
        public const string TradeLoss = "TradeLoss";
        public const string ReferralReward = "Referral Reward";
        public const string AdminAdjustment = "Admin Adjustment";
        public const string Other = "Other";
        public const string Buy = "Buy";
        public const string Sell = "Sell";
    }
    public static class BalanceTransactionReferenceTypes
    {
        public const string Deposit = "Deposit";
        public const string Trade = "Trade";
        public const string Withdrawal = "Withdrawal";
        //[Obsolete]
        //public const string WithdrawalReversal = "WithdrawalReversal";
        //[Obsolete]
        //public const string DepositReversal = "WithdrawalReversal";

        public const string Package = "Package";

    }
}

using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("BANK")]
    public class Bank
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("BANK_NAME")]
        [StringLength(100)]
        public string BankName { get; set; } = null!;

        [Required]
        [Column("ACCOUNT_HOLDER")]
        [StringLength(100)]
        public string AccountHolder { get; set; } = null!;

        [Required]
        [Column("IBAN")]
        [StringLength(50)]
        public string Iban { get; set; } = null!;

        [Column("IS_ACTIVE")]
        public bool IsActive { get; set; } = true;

        [Column("ORDER")]
        public int Order { get; set; }

        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Column("MOD_DATE", TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }
    }
}

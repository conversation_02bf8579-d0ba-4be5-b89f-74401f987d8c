using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;
using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("PAYMENT")]
    public class Deposit
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("USER_ID")]
        public int UserId { get; set; }

        [Required]
        [Column("TYPE")]
        [StringLength(50)]
        public string DepositType { get; set; } = null!;

        [Required]
        [Column("AMOUNT", TypeName = "decimal(10,2)")]

        public decimal Amount { get; set; }

        [Required]
        [Column("FULL_NAME")]
        [StringLength(100)]
        public string FullName { get; set; } = null!;

        [Column("EXTRA_DATA")]
        public string? ExtraData { get; set; }

        [Required]
        [Column("IP_ADDRESS")]
        [StringLength(50)]
        public string IpAddress { get; set; } = null!;

        [Required]
        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; }

        [Column("PROCESS_STATUS")]
        [StringLength(50)]
        public string? ProcessStatus { get; set; }

        [Column("STATUS")]
        public DepositStatus Status { get; set; } = DepositStatus.Pending;

        [Column("LAST_ONLINE_DATE", TypeName = "datetime")]
        public DateTime? LastOnlineDate { get; set; }

        [Column("REWARD_STATUS")]
        public DepositRewardStatus RewardStatus { get; set; } = DepositRewardStatus.Pending;

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        // Helper method to work with JSON extra data
        public T? GetExtraData<T>() where T : class
        {
            return string.IsNullOrEmpty(ExtraData) ? null : JsonSerializer.Deserialize<T>(ExtraData);
        }

        public void SetExtraData<T>(T data) where T : class
        {
            ExtraData = JsonSerializer.Serialize(data);
        }
    }

    public enum DepositStatus
    {
        Pending = 0,
        Approved = 1,
        Rejected = 2
    }
}

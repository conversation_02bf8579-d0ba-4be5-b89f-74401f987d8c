using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("PACKAGE")]
    public class Package
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("NAME")]
        [StringLength(50)]
        public string Name { get; set; } = null!;

        [Required]
        [Column("PRICE", TypeName = "decimal(20,8)")]
        public decimal Price { get; set; }

        [Column("DESCRIPTION")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Column("BENEFITS")]
        public string? Benefits { get; set; }

        [Column("INVITE_LIMIT")]
        public int? InviteLimit { get; set; }

        [Column("EARNINGS_CAP", TypeName = "decimal(20,8)")]
        public decimal? EarningsCap { get; set; }

        [Column("IS_ACTIVE")]
        public bool IsActive { get; set; } = true;

        [Column("ORDER")]
        public int Order { get; set; }

        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Column("MOD_DATE", TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }

        // Helper method for benefits JSON
        [NotMapped]
        public PackageBenefits? PackageBenefitsObject
        {
            get => string.IsNullOrEmpty(Benefits) ? null : JsonSerializer.Deserialize<PackageBenefits>(Benefits);
            set => Benefits = value == null ? null : JsonSerializer.Serialize(value);
        }

        [InverseProperty("Package")]
        public virtual ICollection<UserPackage> UserPackages { get; set; } = new List<UserPackage>();

        [InverseProperty("Package")]
        public virtual ICollection<PackageRewardPercentage> RewardPercentages { get; set; } = new List<PackageRewardPercentage>();
    }

    public class PackageBenefits
    {
        public List<string> Features { get; set; } = new List<string>();
        // Add other benefit properties as needed
    }
}

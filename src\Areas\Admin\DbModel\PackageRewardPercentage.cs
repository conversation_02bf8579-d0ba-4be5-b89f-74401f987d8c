using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("PACKAGE_REWARD_PERCENTAGE")]
    public class PackageRewardPercentage
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("PACKAGE_ID")]
        public int PackageId { get; set; }

        [Required]
        [Column("LEVEL")]
        public int Level { get; set; } // 1 for direct referral, 2 for second level

        [Required]
        [Column("RZW_PERCENTAGE", TypeName = "decimal(5,2)")]
        public decimal RzwPercentage { get; set; }

        [Required]
        [Column("TL_PERCENTAGE", TypeName = "decimal(5,2)")]
        public decimal TlPercentage { get; set; } = 0; // 15.00 for 15%, 5.00 for 5%

        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Column("MOD_DATE", TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }

        [ForeignKey("PackageId")]
        public virtual Package Package { get; set; } = null!;
    }
}

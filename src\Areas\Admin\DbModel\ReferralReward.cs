using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("REFERRAL_REWARD")]
    public class ReferralReward
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("USER_ID")]
        public int UserId { get; set; }

        [Required]
        [Column("REFERRED_USER_ID")]
        public int ReferredUserId { get; set; }

        [Required]
        [Column("PACKAGE_ID")]
        public int PackageId { get; set; }

        [Column("DEPOSIT_ID")]
        public int? DepositId { get; set; }

        [Required]
        [Column("LEVEL")]
        public int Level { get; set; } // 1 for direct referral, 2 for second level

        [Required]
        [Column("RZW_AMOUNT", TypeName = "decimal(20,8)")]
        public decimal RzwAmount { get; set; }

        [Required]
        [Column("TL_AMOUNT", TypeName = "decimal(20,2)")]
        public decimal TlAmount { get; set; }

        [Required]
        [Column("RZW_PERCENTAGE", TypeName = "decimal(5,2)")]
        public decimal RzwPercentage { get; set; } // 5.00 for 5%, 2.00 for 2%

        [Required]
        [Column("TL_PERCENTAGE", TypeName = "decimal(5,2)")]
        public decimal TlPercentage { get; set; } // 15.00 for 15%, 5.00 for 5%

        [Required]
        [Column("DEPOSIT_AMOUNT", TypeName = "decimal(20,8)")]
        public decimal DepositAmount { get; set; }

        [Column("STATUS")]
        public ReferralRewardStatus Status { get; set; } = ReferralRewardStatus.Paid;

        [Column("DEPOSIT_DATE", TypeName = "datetime")]
        public DateTime? DepositDate { get; set; }

        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Column("MOD_DATE", TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }

        [Column("RZW_PRICE", TypeName = "decimal(20,8)")]
        public decimal RzwPrice { get; set; }

        [Column("REWARD_TYPE")]
        [StringLength(50)]
        public string RewardType { get; set; } = "DEPOSIT";

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("ReferredUserId")]
        public virtual User ReferredUser { get; set; } = null!;

        [ForeignKey("PackageId")]
        public virtual Package Package { get; set; } = null!;

        [ForeignKey("DepositId")]
        public virtual Deposit? Deposit { get; set; }

        [InverseProperty("ReferralReward")]
        public virtual ICollection<Trade> Trades { get; set; } = new List<Trade>();

        [InverseProperty("ReferralReward")]
        public virtual ICollection<BalanceTransaction> BalanceTransactions { get; set; } = new List<BalanceTransaction>();
    }

    public enum ReferralRewardStatus
    {
        Pending = 0,
        Paid = 1,
        Cancelled = 2
    }
}

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.DbModel
{
    [Table("USER_PACKAGE")]
    public class UserPackage
    {
        [Key]
        [Column("ID")]
        public int Id { get; set; }

        [Required]
        [Column("USER_ID")]
        public int UserId { get; set; }

        [Required]
        [Column("PACKAGE_ID")]
        public int PackageId { get; set; }

        [Required]
        [Column("PURCHASE_DATE", TypeName = "datetime")]
        public DateTime PurchaseDate { get; set; } = DateTime.UtcNow;

        [Column("EXPIRY_DATE", TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }

        [Column("BALANCE_TRANSACTION_ID")]
        public int? BalanceTransactionId { get; set; }

        [Column("STATUS")]
        public UserPackageStatus Status { get; set; } = UserPackageStatus.Active;

        [Column("CR_DATE", TypeName = "datetime")]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        [Column("MOD_DATE", TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("PackageId")]
        public virtual Package Package { get; set; } = null!;

        [ForeignKey("BalanceTransactionId")]
        public virtual BalanceTransaction? BalanceTransaction { get; set; }
    }

    public enum UserPackageStatus
    {
        Active = 1,
        Expired = 2,
        Cancelled = 3
    }
}

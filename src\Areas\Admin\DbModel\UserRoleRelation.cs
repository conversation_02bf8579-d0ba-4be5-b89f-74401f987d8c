﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable enable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace RazeWinComTr.Areas.Admin.DbModel;

[Table("USER_ROLE_RELATION")]
[Index("RoleId", Name = "IX_USER_ROLE_RELATION_ROLE_ID")]
[Index("UserId", Name = "IX_USER_ROLE_RELATION_USER_ID")]
public partial class UserRoleRelation
{
    [Key]
    [Column("ID")]
    public int Id { get; set; }

    [Column("USER_ID")]
    public int UserId { get; set; }

    [Column("ROLE_ID")]
    public int RoleId { get; set; }

    [Column("IS_ACTIVE")]
    public int IsActive { get; set; }

    [ForeignKey("RoleId")]
    [InverseProperty("UserRoleRelations")]
    public virtual Role Role { get; set; } = null!;

    [ForeignKey("UserId")]
    [InverseProperty("UserRoleRelations")]
    public virtual User User { get; set; } = null!;
}
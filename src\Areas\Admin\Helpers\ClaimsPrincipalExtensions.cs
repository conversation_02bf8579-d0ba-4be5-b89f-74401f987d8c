﻿using System.Globalization;
using System.Security.Claims;
using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.Areas.Admin.Helpers;

public static class ClaimsPrincipalExtensions
{
    public static bool IsInAdminRole(this ClaimsPrincipal user)
    {
        return user.IsInRole(Roller.Admin.ToString());
    }
    //add a method to get user creation date from claims
    public static DateTime? GetClaimUserCreationDate(this ClaimsPrincipal user)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));
        var claimValue = user.FindFirstValue(CustomClaims.DateOfUserCreation.ToString());
        if (claimValue == null)
            return null;
        return DateTime.TryParse(claimValue, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedValue) ? parsedValue : null;
    }
    //public static bool IsInDistributorRole(this ClaimsPrincipal user)
    //{
    //    return user.IsInRole(Roller.Dealer.ToString());
    //}

    //public static bool IsInCompanyRole(this ClaimsPrincipal user)
    //{
    //    return user.IsInRole(Roller.Company.ToString());
    //}

    public static int? GetClaimUserId(this ClaimsPrincipal user)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));

        var claimValue = user.FindFirstValue(ClaimTypes.NameIdentifier);
        if (claimValue == null)
            return null;

        return int.TryParse(claimValue, out var parsedValue) ? parsedValue : null;
    }

    public static string? GetClaimEmail(this ClaimsPrincipal user)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));

        var claimValue = user.FindFirstValue(ClaimTypes.Email);
        return claimValue;
    }

    public static string? GetClaimMobilePhone(this ClaimsPrincipal user)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));

        var claimValue = user.FindFirstValue(ClaimTypes.MobilePhone);
        return claimValue;
    }

    public static DateTime? GetClaimBirthDate(this ClaimsPrincipal user)
    {
        if (user == null)
            throw new ArgumentNullException(nameof(user));
        var claimValue = user.FindFirstValue(ClaimTypes.DateOfBirth);
        if (claimValue == null)
            return null;
        return DateTime.TryParse(claimValue, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedValue) ? parsedValue : null;
    }

    //public static int? GetClaimCompanyId(this ClaimsPrincipal user)
    //{
    //    if (user == null)
    //        throw new ArgumentNullException(nameof(user));

    //    var claimValue = user.FindFirstValue(CustomClaims.CompanyId.ToString());
    //    if (claimValue == null)
    //        return null;

    //    return int.TryParse(claimValue, out var parsedValue) ? parsedValue : null;
    //}

    //public static int? GetClaimDistributorId(this ClaimsPrincipal user)
    //{
    //    if (user == null)
    //        throw new ArgumentNullException(nameof(user));

    //    var claimValue = user.FindFirstValue(CustomClaims.DistributorId.ToString());
    //    if (claimValue == null)
    //        return null;

    //    return int.TryParse(claimValue, out var parsedValue) ? parsedValue : null;
    //}

    //public static string? GetClaimDeviceCode(this ClaimsPrincipal user)
    //{
    //    if (user == null)
    //        throw new ArgumentNullException(nameof(user));

    //    var claimValue = user.FindFirstValue(CustomClaims.DeviceCode.ToString());
    //    return claimValue;
    //}

    public static string? GetClaimValue(this ClaimsPrincipal user, string claimType)
    {
        return user.FindFirst(claimType)?.Value;
    }

    public static List<string> GetClaimValues(this ClaimsPrincipal user, string claimType)
    {
        return user.Claims
            .Where(c => c.Type == claimType)
            .Select(c => c.Value)
            .ToList();
    }
}
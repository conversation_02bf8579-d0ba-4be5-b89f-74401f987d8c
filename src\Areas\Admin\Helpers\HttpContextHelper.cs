﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using System.Globalization;
using System.Security.Claims;

namespace RazeWinComTr.Areas.Admin.Helpers;

public class HttpContextHelper
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public HttpContextHelper(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public string GetBaseUrl()
    {
        var request = _httpContextAccessor.HttpContext?.Request;
        var baseUrl = $"{request?.Scheme}://{request?.Host}";
        return baseUrl;
    }

    /// <summary>
    /// Starts a user session with the specified authentication scheme using a User object
    /// </summary>
    /// <param name="user">The user object containing all user information</param>
    /// <param name="returnUrl">The URL to redirect to after authentication</param>
    /// <param name="roles">The user's roles (if not included in the user object)</param>
    /// <param name="authenticationSchemeName">The authentication scheme to use. If not specified, the default scheme will be used.</param>
    /// <returns>A task representing the asynchronous operation</returns>
    public async Task StartUserSession(User user, string? returnUrl, string authenticationSchemeName)
    {
        if (user == null) throw new ArgumentNullException(nameof(user));

        // Get roles from user object if not provided
        var userRoles = user.UserRoleRelations?.Select(r => r.RoleId) ?? Enumerable.Empty<int>();

        // Create full name from user properties
        string? fullName = null;
        if (!string.IsNullOrEmpty(user.Name) || !string.IsNullOrEmpty(user.Surname))
        {
            fullName = $"{user.Name} {user.Surname}".Trim();
        }

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.UserId.ToString()),
            new(ClaimTypes.Email, user.Email),
            new(ClaimTypes.DateOfBirth, user.BirthDate.ToString(CultureInfo.InvariantCulture)),
            new(CustomClaims.DateOfUserCreation.ToString(), user.CrDate.ToString(CultureInfo.InvariantCulture)),
        };

        if (!string.IsNullOrEmpty(fullName))
        {
            claims.Add(new Claim(ClaimTypes.Name, fullName));
        }

        if (!string.IsNullOrEmpty(user.PhoneNumber))
        {
            claims.Add(new Claim(ClaimTypes.MobilePhone, user.PhoneNumber));
        }   

        if (user.Email == "<EMAIL>")
        {
            claims.Add(new Claim(CustomClaims.SuperAdmin.ToString(), CustomClaims.SuperAdmin.ToString()));
        }

        foreach (var roleId in userRoles)
        {
            var role = (Roller)Enum.ToObject(typeof(Roller), roleId);
            claims.Add(new Claim(ClaimTypes.Role, role.ToString()));
        }

        var claimsIdentity = new ClaimsIdentity(claims, authenticationSchemeName);
        var authProperties = new AuthenticationProperties
        {
            RedirectUri = returnUrl
        };

        if (_httpContextAccessor == null) throw new Exception("_httpContextAccessor is null");
        if (_httpContextAccessor.HttpContext == null) throw new Exception("_httpContextAccessor.HttpContext is null");

        // Use the specified authentication scheme or default to CookieAuthenticationDefaults.AuthenticationScheme
        string scheme;

        if (!string.IsNullOrEmpty(authenticationSchemeName))
        {
            scheme = authenticationSchemeName;
        }
        else
        {
            // Check if the user has admin role
            bool isAdmin = userRoles.Contains((int)Roller.Admin);
            if (isAdmin)
            {
                scheme = AuthConstants.AdminAuthenticationScheme;
            }
            else
            {
                scheme = AuthConstants.UserAuthenticationScheme;
            }
        }

        await _httpContextAccessor.HttpContext.SignInAsync(
            scheme,
            new ClaimsPrincipal(claimsIdentity),
            authProperties);
    }
    
    public void CacheSetString(string key, string value)
    {
        _httpContextAccessor?.HttpContext?.Session.SetString(key, value);
    }

    public string? CacheGetString(string key, string value)
    {
        return _httpContextAccessor?.HttpContext?.Session.GetString(key);
    }
}
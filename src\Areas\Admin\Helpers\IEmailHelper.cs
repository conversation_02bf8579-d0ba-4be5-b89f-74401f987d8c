﻿﻿using RazeWinComTr.Areas.Admin.Models;

namespace RazeWinComTr.Areas.Admin.Helpers
{
    public interface IEmailHelper
    {
        /// <summary>
        /// Sends an email message
        /// </summary>
        /// <param name="message">The email message to send</param>
        /// <returns>True if the email was sent successfully, false otherwise</returns>
        Task<bool> SendEmailAsync(EmailMessage message);

        /// <summary>
        /// Sends a simple email message to a single recipient
        /// </summary>
        /// <param name="to">Recipient email address</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body</param>
        /// <param name="isHtml">Whether the body is HTML</param>
        /// <returns>True if the email was sent successfully, false otherwise</returns>
        Task<bool> SendSimpleEmailAsync(string to, string subject, string body, bool isHtml = true);

        /// <summary>
        /// Sends a simple email message to multiple recipients
        /// </summary>
        /// <param name="to">List of recipient email addresses</param>
        /// <param name="subject">Email subject</param>
        /// <param name="body">Email body</param>
        /// <param name="isHtml">Whether the body is HTML</param>
        /// <returns>True if the email was sent successfully, false otherwise</returns>
        Task<bool> SendSimpleEmailAsync(List<string> to, string subject, string body, bool isHtml = true);

        /// <summary>
        /// Gets the current SMTP settings from the database
        /// </summary>
        /// <returns>SMTP settings</returns>
        Task<SmtpSettings> GetSmtpSettingsAsync();
    }
}

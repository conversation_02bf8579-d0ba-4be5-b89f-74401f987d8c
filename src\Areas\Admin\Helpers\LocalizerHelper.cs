﻿using System.Web;
using Microsoft.Extensions.Localization;

namespace RazeWinComTr.Areas.Admin.Helpers;

public class LocalizerHelper
{
    private LocalizerHelper()
    {
    }

    public static string get(IStringLocalizer _localizer, string key)
    {
        return HttpUtility.JavaScriptStringEncode(_localizer.GetString(key));
    }

    /// <summary>
    /// Gets a localized string and decodes any HTML entities in it
    /// </summary>
    /// <param name="_localizer">The string localizer</param>
    /// <param name="key">The resource key</param>
    /// <returns>The decoded localized string</returns>
    public static string getDecoded(IStringLocalizer _localizer, string key)
    {
        return HttpUtility.HtmlDecode(_localizer.GetString(key));
    }

    /// <summary>
    /// Gets a formatted localized string with parameters and decodes any HTML entities in it
    /// </summary>
    /// <param name="_localizer">The string localizer</param>
    /// <param name="key">The resource key</param>
    /// <param name="args">The format arguments</param>
    /// <returns>The decoded formatted localized string</returns>
    public static string getDecodedFormat(IStringLocalizer _localizer, string key, params object[] args)
    {
        return HttpUtility.HtmlDecode(_localizer[key, args].Value);
    }
}
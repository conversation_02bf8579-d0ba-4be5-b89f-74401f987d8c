﻿﻿using System.Collections.Generic;

namespace RazeWinComTr.Areas.Admin.Models
{
    public class EmailMessage
    {
        public string Subject { get; set; } = string.Empty;
        public string Body { get; set; } = string.Empty;
        public bool IsHtml { get; set; } = true;
        public List<EmailAddress> ToAddresses { get; set; } = new List<EmailAddress>();
        public List<EmailAddress> CcAddresses { get; set; } = new List<EmailAddress>();
        public List<EmailAddress> BccAddresses { get; set; } = new List<EmailAddress>();
        public List<EmailAttachment> Attachments { get; set; } = new List<EmailAttachment>();
    }

    public class EmailAddress
    {
        public string Name { get; set; } = string.Empty;
        public string Address { get; set; } = string.Empty;

        public EmailAddress() { }

        public EmailAddress(string address)
        {
            Address = address;
        }

        public EmailAddress(string name, string address)
        {
            Name = name;
            Address = address;
        }
    }

    public class EmailAttachment
    {
        public string FileName { get; set; } = string.Empty;
        public byte[] Content { get; set; } = Array.Empty<byte>();
        public string ContentType { get; set; } = "application/octet-stream";

        public EmailAttachment() { }

        public EmailAttachment(string fileName, byte[] content, string contentType = "application/octet-stream")
        {
            FileName = fileName;
            Content = content;
            ContentType = contentType;
        }
    }

    public class SmtpSettings
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string Security { get; set; } = "tls"; // tls or ssl
        public string SenderEmail { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;
    }
}

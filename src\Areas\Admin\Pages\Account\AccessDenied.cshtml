﻿@page
@using Microsoft.Extensions.Localization
@model AccessDeniedModel
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["Access Denied"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@ViewData["Title"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["Access Denied"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="error-page">
        <h2 class="headline text-danger">403</h2>
        <div class="error-content">
            <h3><i class="fas fa-exclamation-triangle text-danger"></i> @Localizer["Access Denied"]</h3>
            <p>
                @Localizer["You do not have permission to view this page"]               
            </p>
            <p class="text-muted">
                @Localizer["If you believe this is an error, please contact the site administrator"]
            </p>
            <div class="mt-3">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home mr-1"></i> @Localizer["Go to Home Page"]
                </a>
            </div>
        </div>
    </div>
</section>

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace RazeWinComTr.Areas.Admin.Pages.Account;

[AllowAnonymous]
public class AccessDeniedModel : PageModel
{
    private readonly ILogger<AccessDeniedModel> _logger;

    // Inject ILogger into the page model
    public AccessDeniedModel(ILogger<AccessDeniedModel> logger)
    {
        _logger = logger;
    }

    public void OnGet()
    {
        // Log the access denied event
        _logger.LogWarning("Access Denied for user '{User}' attempting to access '{Url}'.",
            User.Identity?.Name,
            Request.Path);
    }
}
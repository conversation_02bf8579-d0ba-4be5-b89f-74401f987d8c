﻿@page
@using System.Globalization
@using Microsoft.Extensions.Localization
@model LoginModel
@inject IStringLocalizer<SharedResource> Localizer
@{
    Layout = null;
    var currentCultureName = CultureInfo.CurrentCulture.Name;
    List<SelectListItem> languages = [];
    @foreach (var ci in StaticConfig.supportedCultures)
    {
        // Use only the language name without country
        string languageName = ci.NativeName.Split(' ')[0];
        languages.Add(new SelectListItem { Value = ci.Name, Text = languageName, Selected = currentCultureName == ci.Name });
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@Localizer["Management Panel"] | @Localizer["Log in"]</title>

    @* <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback"> *@
    <!-- Font Awesome -->
    <link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css">
    <!-- icheck bootstrap -->
    <link rel="stylesheet" href="/plugins/icheck-bootstrap/icheck-bootstrap.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="/dist/css/adminlte.min.css">
</head>
<body class="hold-transition login-page">
    <div class="login-box">
        <!-- /.login-logo -->
        <div class="card card-outline card-success">
            <div class="card-header text-center">

                <!-- Language Selection -->
                <div class="form-group">
                    <label for="languageSelect">@Localizer["Select Language"]</label>
                    <select asp-items="languages" id="languageSelect" name="languageSelect" title="Language Selection"
                            class="form-control" onchange="changeLanguage(this.value)">
                        @*                         @foreach (var ci in @StaticConfig.supportedCultures)
                        {
                        <option value="@ci.TwoLetterISOLanguageName" >@ci.DisplayName</option>
                        } *@
                    </select>
                </div>
                <a href="#" class="h1"><b>@Localizer["Management Panel"]</b></a>
            </div>
            <div class="card-body">
                <p class="login-box-msg">@Localizer["Sign in to start your session"]</p>
                <form method="post">
                    <!-- Display Validation Errors Here -->
                    @Html.ValidationSummary(true, "", new { @class = "alert alert-danger" })
                    <div class="input-group mb-3">
                        <input type="email" class="form-control" placeholder="@Localizer["Email"]" asp-for="Input.Email"
                               required>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-envelope"></span>
                            </div>
                        </div>
                    </div>
                    <div class="input-group mb-3">
                        <input type="password" class="form-control" placeholder="@Localizer["Password"]"
                               asp-for="Input.Password" required>
                        <div class="input-group-append">
                            <div class="input-group-text">
                                <span class="fas fa-lock"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-8">
                            <div class="icheck-primary">
                                <input type="checkbox" id="remember" asp-for="Input.RememberMe" checked>
                                <label for="remember">
                                    @Localizer["Remember Me"]
                                </label>
                            </div>
                        </div>
                        <!-- /.col -->
                        <div class="col-4">
                            <button type="submit" class="btn btn-primary btn-block">@Localizer["Sign In"]</button>
                        </div>
                        <!-- /.col -->
                    </div>
                </form>

                @*             <p class="mb-1">
                <a href="javascript:void(0)" disabled>@Localizer["I forgot my password"]</a>
            </p>
            <p class="mb-0">
                <a href="javascript:void(0)" class="text-center" disabled>@Localizer["Register a new membership"]</a>
            </p> *@
            </div>
            <!-- /.card-body -->
        </div>
        <!-- /.card -->
    </div>
    <!-- /.login-box -->
    <!-- jQuery -->
    <script src="/plugins/jquery/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="/dist/js/adminlte.min.js"></script>
    <script>
        function changeLanguage(language) {
            // Get the current page's relative URL (path + query string)
            const currentUrl = window.location.pathname + window.location.search;

            // Redirect to the SetLanguage page with language and returnUrl
            window.location.href = `/Admin/SetLanguage?lang=${language}&returnUrl=${encodeURIComponent(currentUrl)}`;
        }
    </script>
</body>
</html>
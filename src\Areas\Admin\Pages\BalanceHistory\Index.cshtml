@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@model IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Balance History"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Balance History"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Balance History"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@L["Count"]: @(Model.Transactions.Count)</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                        <tr>
                            <th>@L["User"]</th>
                            <th>@L["Transaction Type"]</th>
                            <th>@L["Amount"]</th>
                            <th>@L["Previous Balance"]</th>
                            <th>@L["New Balance"]</th>
                            <th>@L["Description"]</th>
                            <th>@L["Date"]</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.Transactions)
                        {
                            <tr>
                                <td>@item.UserEmail</td>
                                <td>@item.TransactionType</td>
                                <td>@item.Amount.ToString("N2")</td>
                                <td>@item.PreviousBalance.ToString("N2")</td>
                                <td>@item.NewBalance.ToString("N2")</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(item.Description))
                                    {
                                        <div>@item.Description</div>
                                    }
                                    @if (item.ReferralReward != null)
                                    {
                                        <div class="text-muted small mt-1">
                                            @item.ReferralReward.GetReferralRewardDescription()
                                        </div>
                                    }
                                </td>
                                <td>@item.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

@section Styles {
    <style>
        .datatable td {
            vertical-align: middle;
        }
        .text-muted.small {
            line-height: 1.4;
        }
    </style>
}

using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction;

namespace RazeWinComTr.Areas.Admin.Pages.BalanceHistory;

public class IndexModel : PageModel
{
    private readonly IBalanceTransactionService _balanceTransactionService;

    public IndexModel(IBalanceTransactionService balanceTransactionService)
    {
        _balanceTransactionService = balanceTransactionService;
    }

    public List<BalanceTransactionViewModel> Transactions { get; set; } = new();

    public async Task OnGetAsync()
    {
        Transactions = await _balanceTransactionService.GetListAsync();
    }
}

@page
@using Microsoft.Extensions.Localization
@model CreateModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = $"{L["New"]} {L["Bank"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@($"{L["Create a New"]} {L["Bank"]}")</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/bank">@L["Banks"]</a></li>
                    <li class="breadcrumb-item active">@L["New"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <div class="card-body">
                    <div class="form-group">
                        <label asp-for="ViewEntity.BankName">@L["Bank Name"]</label>
                        <input asp-for="ViewEntity.BankName" class="form-control" required/>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.AccountHolder">@L["Account Holder"]</label>
                        <input asp-for="ViewEntity.AccountHolder" class="form-control" required/>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Iban">@L["IBAN"]</label>
                        <input asp-for="ViewEntity.Iban" class="form-control" required/>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Order">@L["Order"]</label>
                        <input asp-for="ViewEntity.Order" class="form-control" type="number" required/>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input asp-for="ViewEntity.IsActive" class="custom-control-input" />
                            <label asp-for="ViewEntity.IsActive" class="custom-control-label">@L["Active"]</label>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/Bank" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

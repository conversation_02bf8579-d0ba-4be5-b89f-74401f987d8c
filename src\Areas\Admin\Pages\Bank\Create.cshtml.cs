using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Bank;

public class CreateModel : PageModel
{
    private readonly BankService _bankService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        BankService bankService,
        IStringLocalizer<SharedResource> localizer)
    {
        _bankService = bankService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public BankCreateViewModel ViewEntity { get; set; } = new();



    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            if (!ModelState.IsValid) return Page();

            var entity = new DbModel.Bank
            {
                BankName = ViewEntity.BankName,
                AccountHolder = ViewEntity.AccountHolder,
                Iban = ViewEntity.Iban,
                IsActive = ViewEntity.IsActive,
                Order = ViewEntity.Order,
                CreatedDate = DateTime.UtcNow
            };
            await _bankService.CreateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/Admin/Bank"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = ex.Message,
                Icon = "error"
            };
            return Page();
        }
    }
}

public class BankCreateViewModel
{
    public string BankName { get; set; } = null!;
    public string AccountHolder { get; set; } = null!;
    public string Iban { get; set; } = null!;
    public bool IsActive { get; set; } = true;
    public int Order { get; set; } = 0;
}

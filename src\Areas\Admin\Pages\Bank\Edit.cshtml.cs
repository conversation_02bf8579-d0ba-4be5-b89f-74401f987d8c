using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Bank;

public class EditModel : PageModel
{
    private readonly BankService _bankService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(
        BankService bankService,
        IStringLocalizer<SharedResource> localizer)
    {
        _bankService = bankService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public BankEditViewModel Entity { get; set; } = new();

    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _bankService.GetByIdAsync(id);

        if (entity == null) return NotFound();
        Entity = new BankEditViewModel
        {
            Id = entity.Id,
            BankName = entity.BankName,
            AccountHolder = entity.AccountHolder,
            Iban = entity.Iban,
            IsActive = entity.IsActive,
            Order = entity.Order
        };

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid) return Page();
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();
            var entity = await _bankService.GetByIdAsync(Entity.Id);
            if (entity == null) return NotFound();

            entity.BankName = Entity.BankName;
            entity.AccountHolder = Entity.AccountHolder;
            entity.Iban = Entity.Iban;
            entity.IsActive = Entity.IsActive;
            entity.Order = Entity.Order;
            entity.ModifiedDate = DateTime.UtcNow;
            await _bankService.UpdateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = "/Admin/Bank"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            return Page();
        }
    }
}

public class BankEditViewModel
{
    public int Id { get; set; }
    public string BankName { get; set; } = null!;
    public string AccountHolder { get; set; } = null!;
    public string Iban { get; set; } = null!;
    public bool IsActive { get; set; }
    public int Order { get; set; }
}

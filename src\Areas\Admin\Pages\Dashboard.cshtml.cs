using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Market;
using RazeWinComTr.BackgroundServices;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RazeWinComTr.Areas.Admin.Pages;

public class DashboardModel : PageModel
{
    private readonly AppDbContext _context;

    public DashboardModel(AppDbContext context)
    {
        _context = context;
    }

    public int ProductCount { get; set; }
    public int ProjectCount { get; set; }
    public int ReferenceCount { get; set; }
    public int UserCount { get; set; }
    public int ActiveProductCount { get; set; }
    public int ActiveProjectCount { get; set; }
    public int ActiveUserCount { get; set; }
    public decimal Balance { get; set; }
    public int PendingDepositsCount { get; set; }
    public int PendingWithdrawalsCount { get; set; }
    public List<OrderHistory> RecentOrders { get; set; } = new();
    public List<WalletBalance> TopBalances { get; set; } = new();

    // Background service statuses
    public Dictionary<string, BackgroundServiceStatusViewModel> BackgroundServiceStatuses { get; set; } = new();

    public async Task<IActionResult> OnGetAsync()
    {
        // Get counts
        //ProductCount = await _context.Products.CountAsync();
        //ProjectCount = await _context.Projects.CountAsync();
        //ReferenceCount = await _context.References.CountAsync();
        UserCount = await _context.Users.CountAsync();

        // Get active counts
        //ActiveProductCount = await _context.Products.CountAsync(p => p.IsActive == 1);
        //ActiveProjectCount = await _context.Projects.CountAsync(p => p.IsActive == 1);
        ActiveUserCount = await _context.Users.CountAsync(u => u.IsActive == 1);

        // Get pending payments count
        PendingDepositsCount = await _context.Deposits
            .CountAsync(p => p.Status == DepositStatus.Pending);

        // Get pending withdrawals count
        PendingWithdrawalsCount = await _context.Withdrawals
            .CountAsync(w => w.Status == WithdrawalStatus.Pending);

        var userId = User.GetClaimUserId();
        if (userId == null)
        {
            return Unauthorized();
        }
        // Get  balance of user from user table
        Balance = await _context.Users
                            .Where(p => p.UserId == userId)
                            .Select(p => (decimal?)p.Balance)
                            .FirstOrDefaultAsync() ?? 0;

        // Get background service statuses
        var serviceStatuses = BackgroundServiceStatus.GetAllStatuses();
        foreach (var status in serviceStatuses)
        {
            BackgroundServiceStatuses[status.Key] = new BackgroundServiceStatusViewModel
            {
                ServiceName = status.Key,
                Status = status.Value.Status,
                LastSuccessfulRunTime = status.Value.LastSuccessfulRunTime,
                LastFailureTime = status.Value.LastFailureTime,
                SuccessCount = status.Value.SuccessCount,
                FailureCount = status.Value.FailureCount,
                LastErrorMessage = status.Value.LastErrorMessage,
                TimeSinceLastSuccess = status.Value.TimeSinceLastSuccess
            };
        }
        return Page();
    }

    public class OrderHistory
    {
        public int Id { get; set; }
        public string MarketName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // "BUY" or "SELL"
        public decimal Price { get; set; }
        public decimal Amount { get; set; }
        public decimal OldBalance { get; set; }
        public decimal NewBalance { get; set; }
        public DateTime CrDate { get; set; }
    }

    public class WalletBalance
    {
        public int Id { get; set; }
        public string Currency { get; set; } = string.Empty;
        public decimal Balance { get; set; }
        public decimal PendingBalance { get; set; }
        public decimal AvailableBalance { get; set; }
        public decimal ValueTRY { get; set; }
    }
}
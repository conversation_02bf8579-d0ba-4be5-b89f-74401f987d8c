using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.User;

namespace RazeWinComTr.Areas.Admin.Pages.Deposit;

public class CreateModel : PageModel
{
    private readonly DepositService _depositService;
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        DepositService depositService,
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _depositService = depositService;
        _context = context;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public DepositCreateViewModel ViewEntity { get; set; } = new();

    public List<UserViewModel> Users { get; set; } = new();



    public async Task OnGetAsync()
    {
        Users = await _context.Users
            .Where(u => u.IsActive == 1)
            .Select(u => new UserViewModel
            {
                UserId = u.UserId,
                Email = u.Email,
                FullName = u.Name + " " + u.Surname,
                IsActive = u.IsActive,
                CrDate = u.CrDate
            })
            .ToListAsync();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            if (!ModelState.IsValid) return Page();

            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == ViewEntity.UserId);
            var entity = new DbModel.Deposit
            {
                UserId = ViewEntity.UserId,
                DepositType = _localizer["Admin"].Value,
                Amount = ViewEntity.Amount,
                FullName = user?.Name + " " + user?.Surname ?? _localizer["Unknown"].Value,
                ExtraData = _localizer["Created by admin"].Value,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? _localizer["0.0.0.0"].Value,
                ProcessStatus = _localizer["Completed"].Value,
                Status = (DepositStatus)ViewEntity.Status,
                CreatedDate = DateTime.UtcNow
            };

            // Use the new method that only adds to balance history if approved
            await _depositService.CreateWithBalanceUpdateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/Admin/Deposit"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["An error occurred: {0}", ex.Message],
                Icon = "error"
            };
            await OnGetAsync();
            return Page();
        }
    }
}

public class DepositCreateViewModel
{
    public int UserId { get; set; }
    public decimal Amount { get; set; } = 0;
    public int Status { get; set; } = 0; // 0: Pending, 1: Approved, 2: Rejected
}

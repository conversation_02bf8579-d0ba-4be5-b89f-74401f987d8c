using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;

namespace RazeWinComTr.Areas.Admin.Pages.Deposit;

public class IndexModel : PageModel
{
    private readonly DepositService _depositService;

    public IndexModel(DepositService depositService)
    {
        _depositService = depositService;
    }

    public List<DepositViewModel> Deposits { get; set; } = new();

    [BindProperty(SupportsGet = true)]
    public string? DepositTypeFilter { get; set; }

    public bool ShowingCryptoOnly { get; set; }
    public bool ShowingFiatOnly { get; set; }

    // We'll use direct TempData access instead of property to avoid serialization issues

    public async Task OnGetAsync(string? filter = null)
    {
        DepositTypeFilter = filter;

        if (filter == "crypto")
        {
            Deposits = await _depositService.GetCryptoDepositsAsync();
            ShowingCryptoOnly = true;
        }
        else if (filter == "fiat")
        {
            Deposits = await _depositService.GetFiatDepositsAsync();
            ShowingFiatOnly = true;
        }
        else
        {
            Deposits = await _depositService.GetListAsync();
        }
    }
}

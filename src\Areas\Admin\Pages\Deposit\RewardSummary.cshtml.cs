using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;

namespace RazeWinComTr.Areas.Admin.Pages.Deposit
{
    public class RewardSummaryModel : PageModel
    {
        private readonly ReferralRewardService _referralRewardService;
        private readonly DepositService _depositService;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public RewardSummaryModel(
            ReferralRewardService referralRewardService,
            DepositService depositService,
            IStringLocalizer<SharedResource> localizer)
        {
            _referralRewardService = referralRewardService;
            _depositService = depositService;
            _localizer = localizer;
        }

        [BindProperty(SupportsGet = true)]
        public int Id { get; set; }

        public DepositRewardSummary? Summary { get; set; }

        public SweetAlert2Message? AlertMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                // Get payment reward summary
                Summary = await _referralRewardService.GetDepositRewardSummaryAsync(Id);

                return Page();
            }
            catch (Exception ex)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Icon = "error",
                    Title = _localizer["Error"],
                    Text = _localizer["An error occurred while retrieving reward summary: {0}", ex.Message]
                };

                return Page();
            }
        }
    }
}

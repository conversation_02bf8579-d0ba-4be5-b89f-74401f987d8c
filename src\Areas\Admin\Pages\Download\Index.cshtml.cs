using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Areas.Admin.Pages.Download;

[AllowAnonymous] //todo Auth
public class IndexModel : PageModel
{
    private readonly FileService _fileService;

    public IndexModel(FileService fileService)
    {
        _fileService = fileService;
    }

    public IActionResult OnGet() //todo AUTH
    {
        if (!Request.Query.ContainsKey("file") || !Request.Query["file"].Any()) return NotFound();

        var fileRelativePath = Request.Query["file"][0];
        // Ensure the filename is provided
        if (string.IsNullOrEmpty(fileRelativePath)) return NotFound(); // Return 404 if filename is missing
        if (string.IsNullOrEmpty(_fileService.FileStoragePath)) return NotFound(); // Return 404 if filename is missing

        // Resolve the full file path by combining the storage path with the relative filename
        var filePath = Path.Combine(_fileService.FileStoragePath, fileRelativePath);

        // Normalize the file path to prevent directory traversal
        var fullFilePath = Path.Combine(_fileService.FileStoragePath, filePath);

        // Check if the file exists
        if (!System.IO.File.Exists(fullFilePath)) return NotFound(); // Return 404 if the file doesn't exist

        // Return the file content with the appropriate MIME type
        var fileBytes = System.IO.File.ReadAllBytes(fullFilePath);
        var fileName = Path.GetFileName(fullFilePath); // Ensure only the file name is returned
        return File(fileBytes, "application/octet-stream", fileName);
    }
}
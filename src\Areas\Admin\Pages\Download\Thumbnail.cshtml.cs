using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;

namespace RazeWinComTr.Areas.Admin.Pages.Download;

[AllowAnonymous] //todo Auth
public class ThumbnailModel : PageModel
{
    private readonly FileService _fileService;

    public ThumbnailModel(FileService fileService)
    {
        _fileService = fileService;
    }

    public async Task<IActionResult> OnGetAsync() //todo AUTH
    {
        if (!Request.Query.ContainsKey("file") || !Request.Query["file"].Any()) return NotFound();
        var fileRelativePath = Request.Query["file"][0];
        // Ensure the filename is provided
        if (string.IsNullOrEmpty(fileRelativePath)) return NotFound(); // Return 404 if filename is missing
        if (string.IsNullOrEmpty(_fileService.FileStoragePath)) return NotFound(); // Return 404 if filename is missing
        // Resolve the full file path by combining the storage path with the relative filename
        var filePath = Path.Combine(_fileService.FileStoragePath, fileRelativePath);

        // Normalize the file path to prevent directory traversal
        var fullFilePath = Path.Combine(_fileService.FileStoragePath, filePath);
        var fileName = Path.GetFileName(fullFilePath); // Ensure only the file name is returned

        // Check if the file exists
        if (!System.IO.File.Exists(fullFilePath)) return NotFound(); // Return 404 if the file doesn't exist

        // Process the image and create a thumbnail
        try
        {
            var fileBytes = await System.IO.File.ReadAllBytesAsync(fullFilePath); // Read the file into memory

            using (var image = Image.Load(fileBytes)) // Load from byte array
            {
                // Resize the image to a thumbnail (200x200 for example)
                var thumbnail = image.Clone(ctx => ctx.Resize(64, 64));

                // Compress the thumbnail to reduce file size
                using (var thumbnailStream = new MemoryStream())
                {
                    // Save the thumbnail image to the memory stream
                    await thumbnail.SaveAsync(thumbnailStream,
                        new JpegEncoder { Quality = 50 }); // 50% quality for smaller file size

                    // Ensure the stream's position is set to the beginning before using it
                    thumbnailStream.Position = 0;
                    // Return the thumbnail with cache headers
                    Response.Headers.CacheControl = "public, max-age=86400"; // Cache for 30 days
                    Response.Headers.Expires = DateTime.UtcNow.AddDays(1).ToString("R");
                    var contentType = ContentTypesHelper.GetImageContentType(fileName);

                    // Return the thumbnail as a JPEG image
                    return File(thumbnailStream.ToArray(), contentType, fileName);
                }
            }
        }
        catch (Exception ex)
        {
            // Log the error if needed
            return StatusCode(500, "Error processing the image: " + ex.Message);
        }
    }
}
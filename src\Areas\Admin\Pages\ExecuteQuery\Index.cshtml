@page
@model RazeWinComTr.Areas.Admin.Pages.ExecuteQuery.IndexModel
@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SQLite Query Executor</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="/lib/fontawesome/css/all.min.css" />

    <!-- CodeMirror for SQL syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/codemirror.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/theme/dracula.min.css" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/site/pages/admin/executequery/styles.css" asp-append-version="true" />
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-database me-2"></i>SQLite Query Executor
            </a>
            <div class="d-flex">
                <a href="/Admin" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-arrow-left me-1"></i>Back to Admin
                </a>
            </div>
        </div>
    </nav>

    <div class="main-container">
        @if (!Model.IsValidRequest)
        {
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>Invalid access attempt. Please provide a valid security key.
            </div>
        }
        else
        {
            <div class="query-container">
                <form method="post" asp-page="Index" asp-route-key="@Request.Query["key"]">
                    <div class="card bg-dark border-0 shadow-sm mb-3">
                        <div class="card-header bg-dark text-light border-bottom border-secondary">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-code me-2"></i>SQL Query</h5>
                                <div class="editor-toolbar">
                                    <button type="button" id="commentBtn" class="btn btn-sm btn-outline-light me-1" title="Comment/Uncomment (Ctrl+/)">
                                        <i class="fas fa-comment"></i>
                                    </button>
                                    <button type="button" id="formatBtn" class="btn btn-sm btn-outline-light me-1" title="Format SQL">
                                        <i class="fas fa-align-left"></i>
                                    </button>
                                    <button type="button" id="undoBtn" class="btn btn-sm btn-outline-light me-1" title="Undo (Ctrl+Z)">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    <button type="button" id="redoBtn" class="btn btn-sm btn-outline-light me-1" title="Redo (Ctrl+Y)">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button type="button" id="helpBtn" class="btn btn-sm btn-outline-light" title="Keyboard Shortcuts">
                                        <i class="fas fa-keyboard"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <textarea id="sqlEditor" asp-for="SqlQuery" class="form-control bg-dark text-light"></textarea>
                            <input type="hidden" id="selectedText" asp-for="SelectedText" />
                        </div>
                        <div class="card-footer bg-dark border-top border-secondary d-flex justify-content-between">
                            <button type="button" id="clearBtn" class="btn btn-clear">
                                <i class="fas fa-eraser me-1"></i>Clear
                            </button>
                            <div>
                                <button type="button" id="executeSelectedBtn" class="btn btn-info me-2" disabled>
                                    <i class="fas fa-code me-1"></i>Execute Selected
                                </button>
                                <button type="submit" id="executeAllBtn" class="btn btn-execute">
                                    <i class="fas fa-play me-1"></i>Execute All
                                </button>
                            </div>
                        </div>
                    </div>
                </form>

                @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                {
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>@Model.ErrorMessage
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                {
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>@Model.SuccessMessage
                    </div>
                }
            </div>

            <div class="results-container">
                <h5 class="mb-3">
                    <i class="fas fa-table me-2"></i>Results
                    @if (Model.UsedSelectedText)
                    {
                        <span class="badge bg-info ms-2">Selected Text</span>
                    }
                </h5>

                @if (Model.HasResults && Model.IsSelectQuery)
                {
                    <div class="execution-info">
                        <span><i class="fas fa-info-circle me-1"></i>Showing @Model.RowsAffected rows</span>
                        <span class="ms-3"><i class="fas fa-clock me-1"></i>Execution time: @Model.ExecutionTime.ToString("F2") ms</span>
                    </div>

                    <div class="table-responsive">
                        @{
                            // Determine if we should use compact mode based on number of columns and rows
                            bool useCompactMode = Model.ResultTable.Columns.Count > 5 || Model.RowsAffected > 10;
                            string tableClass = useCompactMode ? "table table-striped table-hover table-bordered table-compact" : "table table-striped table-hover table-bordered";
                        }
                        <div class="mb-2">
                            <div class="form-check form-switch d-inline-block">
                                <input class="form-check-input" type="checkbox" id="compactModeSwitch" @(useCompactMode ? "checked" : "")>
                                <label class="form-check-label" for="compactModeSwitch">Compact Mode</label>
                            </div>
                            <span class="ms-3 text-muted small">
                                <i class="fas fa-table me-1"></i>@Model.ResultTable.Columns.Count columns × @Model.RowsAffected rows 
                            </span>
                        </div>
                        <table class="@tableClass" id="resultsTable">
                            <thead>
                                <tr>
                                    @foreach (DataColumn column in Model.ResultTable.Columns)
                                    {
                                        <th>@column.ColumnName</th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (DataRow row in Model.ResultTable.Rows)
                                {
                                    <tr>
                                        @foreach (var item in row.ItemArray)
                                        {
                                            <td>@(item?.ToString() ?? "NULL")</td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else if (Model.RowsAffected > 0 && !Model.IsSelectQuery)
                {
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Non-query executed successfully. @Model.RowsAffected rows affected.    
                        <div class="mt-2">
                            <small><i class="fas fa-clock me-1"></i>Execution time: @Model.ExecutionTime.ToString("F2") ms</small>    
                        </div>
                    </div>
                }
                else if (Model.SqlQuery != null && Model.ErrorMessage == "")
                {
                    <div class="no-results">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <h5>No results to display</h5>
                        <p>Your query executed successfully but returned no data.</p>
                    </div>
                }
                else if (string.IsNullOrEmpty(Model.SqlQuery))
                {
                    <div class="no-results">
                        <i class="fas fa-keyboard fa-3x mb-3"></i>
                        <h5>Enter a SQL query to get started</h5>
                        <p>Type your SQLite query in the editor above and click Execute.</p>
                    </div>
                }
            </div>
        }
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/mode/sql/sql.min.js"></script>

    <!-- Custom JavaScript -->
    <script src="~/site/pages/admin/executequery/scripts.js" asp-append-version="true"></script>

    <!-- Keyboard Shortcuts Modal -->
    <div class="modal fade" id="keyboardShortcutsModal" tabindex="-1" aria-labelledby="keyboardShortcutsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content bg-dark text-light border-secondary">
                <div class="modal-header border-secondary">
                    <h5 class="modal-title" id="keyboardShortcutsModalLabel">
                        <i class="fas fa-keyboard me-2"></i>Keyboard Shortcuts
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>      
                </div>
                <div class="modal-body">
                    <table class="table table-dark table-bordered">
                        <thead>
                            <tr>
                                <th>Action</th>
                                <th>Shortcut</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Execute All</td>
                                <td><kbd>Ctrl</kbd> + <kbd>Enter</kbd></td>
                            </tr>
                            <tr>
                                <td>Execute Selected</td>
                                <td><kbd>Ctrl</kbd> + <kbd>E</kbd></td>
                            </tr>
                            <tr>
                                <td>Comment/Uncomment</td>
                                <td><kbd>Ctrl</kbd> + <kbd>/</kbd></td>
                            </tr>
                            <tr>
                                <td>Undo</td>
                                <td><kbd>Ctrl</kbd> + <kbd>Z</kbd></td>
                            </tr>
                            <tr>
                                <td>Redo</td>
                                <td><kbd>Ctrl</kbd> + <kbd>Y</kbd> or <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>Z</kbd></td>
                            </tr>
                            <tr>
                                <td>Indent</td>
                                <td><kbd>Tab</kbd></td>
                            </tr>
                            <tr>
                                <td>Outdent</td>
                                <td><kbd>Shift</kbd> + <kbd>Tab</kbd></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer border-secondary">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

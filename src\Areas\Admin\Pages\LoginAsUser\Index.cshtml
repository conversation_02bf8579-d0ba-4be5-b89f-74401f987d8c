@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.LoginAsUser.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Login As User"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@section Styles {
    <!-- Select2 -->
    <link rel="stylesheet" href="/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
}

<div class="container-fluid">
    <h1 class="mt-4">
        @L["Login As User"]
    </h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/Admin">@L["Dashboard"]</a></li>
        <li class="breadcrumb-item active">@L["Login As User"]</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-user-secret me-1"></i>
            @L["Select a user to login as"]
        </div>
        <div class="card-body">
            <form method="post">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="SelectedUserId" class="control-label">@L["Select User"]</label>
                            <select asp-for="SelectedUserId" class="form-control select2" required>
                                <option value="">-- @L["Select User"] --</option>
                                @foreach (var user in Model.Users)
                                {
                                    <option value="@user.UserId">@user.Email (@user.FullName)</option>
                                }
                            </select>
                            <span asp-validation-for="SelectedUserId" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i> @L["Login As Selected User"]
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            @if (Model.AlertMessage != null)
            {
                <partial name="_SweetAlert2" model="Model.AlertMessage" />
            }
        </div>
    </div>
</div>

@section Scripts {
    <!-- Select2 -->
    <script src="/plugins/select2/js/select2.full.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.select2').select2({
                placeholder: "@Html.Raw(L["Select User"])",
                allowClear: true,
                width: '100%',
                language: {
                    searching: function() {
                        return "@Html.Raw(L["Searching..."])";
                    },
                    noResults: function() {
                        return "@Html.Raw(L["No results found"])";
                    }
                }
            });

            // Auto-submit form when user is selected
            $('#SelectedUserId').on('change', function() {
                if ($(this).val()) {
                    $(this).closest('form').submit();
                }
            });
        });
    </script>
}

using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.User;
using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Areas.Admin.Pages.LoginAsUser;

public class IndexModel : PageModel
{
    private readonly AppDbContext _context;
    private readonly HttpContextHelper _httpContextHelper;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public IndexModel(
        AppDbContext context,
        HttpContextHelper httpContextHelper,
        IStringLocalizer<SharedResource> localizer)
    {
        _context = context;
        _httpContextHelper = httpContextHelper;
        _localizer = localizer;
        Users = new List<UserViewModel>();
    }

    public List<UserViewModel> Users { get; set; }

    [BindProperty]
    [Required(ErrorMessage = "Please select a user")]
    public int? SelectedUserId { get; set; }

    public SweetAlert2Message? AlertMessage { get; set; }

    public async Task OnGetAsync()
    {
        // Get all active users with User role
        Users = await _context.Users
            .Include(u => u.UserRoleRelations)
            .Where(u => u.IsActive == 1 && u.UserRoleRelations.Any(r => r.RoleId == (int)Roller.User && r.IsActive == 1))
            .Select(u => new UserViewModel
            {
                UserId = u.UserId,
                Email = u.Email,
                FullName = u.Name + " " + u.Surname,
                IsActive = u.IsActive,
                CrDate = u.CrDate
            })
            .OrderBy(u => u.Email)
            .ToListAsync();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid || !SelectedUserId.HasValue)
        {
            await OnGetAsync();
            return Page();
        }

        // Get the selected user with their roles
        var selectedUser = await _context.Users
            .Include(u => u.UserRoleRelations)
            .FirstOrDefaultAsync(u => u.UserId == SelectedUserId.Value && u.IsActive == 1);

        if (selectedUser == null)
        {
            ModelState.AddModelError(string.Empty, _localizer["Selected user not found or is inactive"]);
            await OnGetAsync();
            return Page();
        }

        // Check if the selected user has the User role
        bool isRegularUser = selectedUser.UserRoleRelations.Any(r => r.RoleId == (int)Roller.User && r.IsActive == 1);
        if (!isRegularUser)
        {
            ModelState.AddModelError(string.Empty, _localizer["Selected user does not have the User role"]);
            await OnGetAsync();
            return Page();
        }

        try
        {
            // Sign out from the admin session
            await HttpContext.SignOutAsync(AuthConstants.UserAuthenticationScheme);

            // Sign in as the selected user
            await _httpContextHelper.StartUserSession(
                user: selectedUser,
                returnUrl: "/MyAccount/Dashboard",
                AuthConstants.UserAuthenticationScheme
            );

            // Redirect to the user dashboard
            return RedirectToPage("/Dashboard", new { area = "MyAccount" });
        }
        catch (Exception ex)
        {
            ModelState.AddModelError(string.Empty, $"Error during login: {ex.Message}");
            await OnGetAsync();
            return Page();
        }
    }
}

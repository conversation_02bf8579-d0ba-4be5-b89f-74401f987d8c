@page
@using Microsoft.Extensions.Localization
@model CreateModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = $"{L["New"]} {L["Market"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Create a New Market"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/market">@L["Markets"]</a></li>
                    <li class="breadcrumb-item active">@L["New"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post" enctype="multipart/form-data">
                <div class="card-body">
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input asp-for="ViewEntity.IsActive" class="custom-control-input" />
                            <label asp-for="ViewEntity.IsActive" class="custom-control-label">@L["Active"]</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input asp-for="ViewEntity.IsApi" class="custom-control-input" />
                            <label asp-for="ViewEntity.IsApi" class="custom-control-label">@L["Is API"]</label>
                        </div>
                    </div>
                    <div class="form-group" id="apiServiceNameGroup" style="display: none;">
                        <label asp-for="ViewEntity.ApiServiceName">@L["API Service"]</label>
                        <select asp-for="ViewEntity.ApiServiceName" class="form-control" id="apiServiceSelect">
                            <option value="">@L["Select API Service"]</option>
                            <option value="Bitexen">Bitexen</option>
                            <option value="BTCTurk">BTCTurk</option>
                        </select>
                    </div>
                    <div class="form-group" id="apiPairGroup" style="display: none;">
                        <label for="apiPairSelect">@L["API Pair"]</label>
                        <select id="apiPairSelect" class="form-control">
                            <option value="">@L["Select Pair"]</option>
                        </select>
                        <small class="form-text text-muted">@L["Select a pair from the API to automatically fill the Pair Code field"]</small>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Coin">@L["Coin Symbol"]</label>
                        <input asp-for="ViewEntity.Coin" class="form-control" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Name">@L["Name"]</label>
                        <input asp-for="ViewEntity.Name" class="form-control" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.ShortName">@L["Short Name"]</label>
                        <input asp-for="ViewEntity.ShortName" class="form-control" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.PairCode">@L["Pair Code"]</label>
                        <input asp-for="ViewEntity.PairCode" class="form-control" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.BuyPrice">@L["Buy Price"]</label>
                        <input asp-for="ViewEntity.BuyPrice" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="ViewEntity.BuyPrice" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.SellPrice">@L["Sell Price"]</label>
                        <input asp-for="ViewEntity.SellPrice" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="ViewEntity.SellPrice" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.DecimalPlaces">@L["Decimal Places"]</label>
                        <input asp-for="ViewEntity.DecimalPlaces" class="form-control" required />
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.MinimumBuy">@L["Minimum Buy"]</label>
                        <input asp-for="ViewEntity.MinimumBuy" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="ViewEntity.MinimumBuy" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.MaximumBuy">@L["Maximum Buy"]</label>
                        <input asp-for="ViewEntity.MaximumBuy" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="ViewEntity.MaximumBuy" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.MinimumSell">@L["Minimum Sell"]</label>
                        <input asp-for="ViewEntity.MinimumSell" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="ViewEntity.MinimumSell" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.MaximumSell">@L["Maximum Sell"]</label>
                        <input asp-for="ViewEntity.MaximumSell" type="text" class="form-control decimal-input" data-decimal-places="8" data-val="true" data-val-decimalComma="@L["Please enter a valid number"]" />
                        <span asp-validation-for="ViewEntity.MaximumSell" class="text-danger"></span>
                    </div>
                    <div class="form-group">
                        <label asp-for="ViewEntity.Order">@L["Order"]</label>
                        <input asp-for="ViewEntity.Order" class="form-control" type="number" required />
                    </div>

                    <div class="form-group">
                        <label asp-for="ImageFile">@L["Icon Image"]</label>
                        <input type="file" asp-for="ImageFile" class="form-control" accept="image/*" />
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/Market" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    <script src="/js/decimal-input-handler.js"></script>
    <script>
        $(document).ready(function() {
            // Function to toggle API service name field visibility
            function toggleApiServiceNameField() {
                if ($('#ViewEntity_IsApi').is(':checked')) {//ViewEntity_IsApi id name is auto generated by asp-for
                    $('#apiServiceNameGroup').show();
                    // Show pair dropdown only if an API service is selected
                    if ($('#apiServiceSelect').val()) {
                        $('#apiPairGroup').show();
                        loadApiPairs($('#apiServiceSelect').val());
                    }
                } else {
                    $('#apiServiceNameGroup').hide();
                    $('#apiPairGroup').hide();
                    $('#ViewEntity_ApiServiceName').val('');
                    $('#apiPairSelect').empty().append('<option value="">@L["Select Pair"]</option>');
                }
            }

            // Function to load pairs from the selected API service
            function loadApiPairs(apiServiceName) {
                if (!apiServiceName) {
                    $('#apiPairGroup').hide();
                    return;
                }

                // Show loading indicator
                $('#apiPairSelect').empty().append('<option value="">@L["Loading..."]</option>');
                $('#apiPairGroup').show();

                // Call the API to get the pairs
                $.ajax({
                    url: `/api/ajax/pairs/${apiServiceName}`,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // Clear the dropdown
                        $('#apiPairSelect').empty();
                        $('#apiPairSelect').append('<option value="">@L["Select Pair"]</option>');

                        // Add the pairs to the dropdown
                        if (data && data.length > 0) {
                            $.each(data, function(index, pair) {
                                $('#apiPairSelect').append(`<option value="${pair.pairCode}" data-base="${pair.baseCurrency}" data-quote="${pair.quoteCurrency}">${pair.displayName}</option>`);
                            });
                        } else {
                            $('#apiPairSelect').append('<option value="">@L["No pairs found"]</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading pairs:', error);
                        $('#apiPairSelect').empty().append('<option value="">@L["Error loading pairs"]</option>');
                    }
                });
            }

            // Initial state
            toggleApiServiceNameField();

            // On change event for IsApi checkbox
            $('#ViewEntity_IsApi').change(function() {
                toggleApiServiceNameField();
            });

            // On change event for API service select
            $('#apiServiceSelect').change(function() {
                var apiServiceName = $(this).val();
                if (apiServiceName) {
                    loadApiPairs(apiServiceName);
                } else {
                    $('#apiPairGroup').hide();
                    $('#apiPairSelect').empty().append('<option value="">@L["Select Pair"]</option>');
                }
            });

            // On change event for API pair select
            $('#apiPairSelect').change(function() {
                var selectedPairCode = $(this).val();
                if (selectedPairCode) {
                    // Set the pair code field
                    $('#ViewEntity_PairCode').val(selectedPairCode);

                    // Get the selected option
                    var selectedOption = $(this).find('option:selected');

                    // Set the coin symbol field if it's empty
                    if (!$('#ViewEntity_Coin').val()) {
                        $('#ViewEntity_Coin').val(selectedOption.data('base'));
                    }

                    // Set the name field if it's empty
                    if (!$('#ViewEntity_Name').val()) {
                        $('#ViewEntity_Name').val(selectedOption.data('base'));
                    }

                    // Set the short name field if it's empty
                    if (!$('#ViewEntity_ShortName').val()) {
                        $('#ViewEntity_ShortName').val(selectedOption.data('base'));
                    }
                }
            });

            // Add custom validation method for decimal inputs with comma
            $.validator.addMethod('decimalComma', function (value, element) {
                // Allow empty values for optional fields
                if (this.optional(element)) {
                    return true;
                }

                // Check if the value matches the pattern for decimal with comma
                return /^-?\d+(?:,\d+)?$/.test(value);
            }, '@L["Please enter a valid number with comma as decimal separator (e.g., 123,45)"]');

            // Apply the validation method to the form
            $.validator.unobtrusive.adapters.add('decimalComma', [], function (options) {
                options.rules['decimalComma'] = true;
                options.messages['decimalComma'] = options.message;
            });
        });
    </script>
}

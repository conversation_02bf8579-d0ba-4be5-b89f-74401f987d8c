using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Market;

public class DeleteModel : PageModel
{
    private readonly IMarketService _marketService;
    private readonly string _fileStoragePath;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteModel(
        IMarketService marketService,
        IStringLocalizer<SharedResource> localizer,
        IConfiguration configuration)
    {
        _marketService = marketService;
        _localizer = localizer;
        _fileStoragePath = configuration["FileStoragePath"]!;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public int Id { get; set; }

    public DbModel.Market? Entity { get; set; }
    public string? ErrorMessage { get; set; }

    public IActionResult OnGetAsync(int id)
    {
        // Market silme işlemi devre dışı bırakıldı
        TempData["ErrorMessage"] = _localizer["Market deletion is disabled"].Value;
        return RedirectToPage("./Index");
    }

    public IActionResult OnPostAsync()
    {
        // Market silme işlemi devre dışı bırakıldı
        TempData["ErrorMessage"] = _localizer["Market deletion is disabled"].Value;
        return RedirectToPage("./Index");
    }
}

@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Constants
@model IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Markets"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Markets"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Markets"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <a id="btnCreateNew" asp-page="Create" class="btn btn-success" role="button"
                   aria-label="@($"{L["Create a New"]} {L["Market"]}")" title="@($"{L["Create a New"]} {L["Market"]}")">
                    <i class="fas fa-plus fa-2x"></i>
                </a>
                <h3 class="card-title float-right">@L["Count"]: @(Model.Markets.Count)</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered datatable">
                    <thead>
                        <tr>
                            <th>@L["Pair Code"]</th>
                            <th>@L["Coin"]</th>
                            <th>@L["Name"]</th>
                            <th>@L["Buy Price"]</th>
                            <th>@L["Sell Price"]</th>
                            <th>@L["Change 24h"]</th>
                            <th>@L["API"]</th>
                            <th>@L["API Service"]</th>
                            <th>@L["Status"]</th>
                            <th>@L["Last Price Update"]</th>
                            <th style="width: 150px">@L["Actions"]</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.Markets)
                        {
                            <tr class="@(item.IsApi == 1 ? (item.ApiServiceName == ApiServiceNames.Bitexen ? "api-bitexen-row" : (item.ApiServiceName == ApiServiceNames.BTCTurk ? "api-btcturk-row" : "")) : "")">
                                <td>@item.PairCode</td>
                                <td>@item.Coin</td>
                                <td>@item.Name</td>
                                <td>@item.BuyPrice.ToString("N8")</td>
                                <td>@item.SellPrice.ToString("N8")</td>
                                <td>@(item.Change24h.HasValue ? item.Change24h.Value.ToString("N2") + "%" : "-")</td>
                                <td>@(item.IsApi == 1 ? L["Yes"] : L["No"])</td>
                                <td>@(item.IsApi == 1 ? item.ApiServiceName : "-")</td>
                                <td>@(item.IsActive == 1 ? L["Active"] : L["Inactive"])</td>
                                <td>@(item.LastPriceUpdate.HasValue? item.LastPriceUpdate.Value.ToLocalTime().ToString("dd/MM/yyyy HH:mm:ss") : "-")</td>
                                <td>
                                    <a href="/Admin/Market/Edit?id=@item.Id" class="btn btn-info btn-sm">
                                        <i class="fas fa-pencil-alt"></i>
                                    </a>
                                    @* Silme düğmesi gizlendi *@
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <style>
        .api-bitexen-row {
            background-color: rgba(144, 238, 144, 0.2) !important; /* Light green */
        }

        .api-btcturk-row {
            background-color: rgba(135, 206, 250, 0.2) !important; /* Light blue */
        }
        /* Ensure the colors are maintained when using DataTables striping */
        .table-striped tbody tr.api-bitexen-row:nth-of-type(odd),
        .table-striped tbody tr.api-bitexen-row:nth-of-type(even) {
            background-color: rgba(144, 238, 144, 0.2) !important;
        }

        .table-striped tbody tr.api-btcturk-row:nth-of-type(odd),
        .table-striped tbody tr.api-btcturk-row:nth-of-type(even) {
            background-color: rgba(135, 206, 250, 0.2) !important;
        }
    </style>
}

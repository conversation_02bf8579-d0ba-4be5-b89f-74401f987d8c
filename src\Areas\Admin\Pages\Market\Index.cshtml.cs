using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Market;

namespace RazeWinComTr.Areas.Admin.Pages.Market;

public class IndexModel : PageModel
{
    private readonly IMarketService _marketService;

    public IndexModel(IMarketService marketService)
    {
        _marketService = marketService;
    }

    public List<MarketViewModel> Markets { get; set; } = new();

    public async Task OnGetAsync()
    {
        Markets = await _marketService.GetListAsync(isActive: null);
    }
}

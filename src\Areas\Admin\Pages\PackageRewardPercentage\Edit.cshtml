@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.PackageRewardPercentage.EditModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Edit Package Reward Percentage"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Edit Package Reward Percentage"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/packagerewardpercentage">@L["Package Reward Percentages"]</a></li>
                    <li class="breadcrumb-item active">@L["Edit"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <input type="hidden" asp-for="Entity.Id"/>
                <div class="card-body">
                    <div class="form-group">
                        <label>@L["Package"]</label>
                        <input type="text" class="form-control" value="@Model.PackageName" readonly />
                        <input type="hidden" asp-for="Entity.PackageId" />
                    </div>
                    <div class="form-group">
                        <label>@L["Level"]</label>
                        <input type="text" class="form-control" value="@(Model.GetLevelDisplayText())" readonly />
                        <input type="hidden" asp-for="Entity.Level" />
                    </div>                    
                    <div class="form-group">
                        <label asp-for="Entity.TlPercentage">@L["TL Percentage"] (%)</label>
                        <input asp-for="Entity.TlPercentage" class="form-control" type="number" step="0.01" min="0" max="100" required/>
                        <small class="form-text text-muted">@L["Enter TL percentage value between 0 and 100"]</small>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.RzwPercentage">@L["RZW Percentage"] (%)</label>
                        <input asp-for="Entity.RzwPercentage" class="form-control" type="number" step="0.01" min="0" max="100" required/>
                        <small class="form-text text-muted">@L["Enter RZW percentage value between 0 and 100"]</small>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-1"></i> @L["TL Percentage and RZW Percentage should add up to Total Percentage"]
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/PackageRewardPercentage" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

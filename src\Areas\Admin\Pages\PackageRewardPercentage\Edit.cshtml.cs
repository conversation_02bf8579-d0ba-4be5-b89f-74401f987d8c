using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.PackageRewardPercentage;

public class EditModel : PageModel
{
    private readonly PackageRewardPercentageService _percentageService;
    private readonly PackageService _packageService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(
        PackageRewardPercentageService percentageService,
        PackageService packageService,
        IStringLocalizer<SharedResource> localizer)
    {
        _percentageService = percentageService;
        _packageService = packageService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public PackageRewardPercentageEditViewModel Entity { get; set; } = new();

    public string PackageName { get; set; } = string.Empty;



    public string GetLevelDisplayText()
    {
        return Entity.Level switch
        {
            1 => _localizer["Level 1"],
            2 => _localizer["Level 2"],
            3 => _localizer["Level 3"],
            4 => _localizer["Level 4"],
            5 => _localizer["Level 5"],
            6 => _localizer["Level 6"],
            7 => _localizer["Level 7"],
            8 => _localizer["Level 8"],
            9 => _localizer["Level 9"],
            10 => _localizer["Level 10"],
            _ => _localizer["Level {0}", Entity.Level]
        };
    }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _percentageService.GetByIdAsync(id);

        if (entity == null) return NotFound();

        Entity = new PackageRewardPercentageEditViewModel
        {
            Id = entity.Id,
            PackageId = entity.PackageId,
            Level = entity.Level,
            TlPercentage = entity.TlPercentage,
            RzwPercentage = entity.RzwPercentage
        };

        var package = await _packageService.GetByIdAsync(entity.PackageId);
        if (package != null)
        {
            PackageName = package.Name;
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid) return Page();
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            var entity = await _percentageService.GetByIdAsync(Entity.Id);
            if (entity == null) return NotFound();

            // Package and Level cannot be changed, only percentages
            entity.TlPercentage = Entity.TlPercentage;
            entity.RzwPercentage = Entity.RzwPercentage;
            entity.ModifiedDate = DateTime.UtcNow;

            await _percentageService.UpdateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = "/Admin/PackageRewardPercentage"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = ex.Message,
                Icon = "error"
            };

            var package = await _packageService.GetByIdAsync(Entity.PackageId);
            if (package != null)
            {
                PackageName = package.Name;
            }

            return Page();
        }
    }
}

public class PackageRewardPercentageEditViewModel
{
    public int Id { get; set; }
    public int PackageId { get; set; }
    public int Level { get; set; }
    public decimal TlPercentage { get; set; }
    public decimal RzwPercentage { get; set; }
}

@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.PackageRewardPercentage.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Package Reward Percentages"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Package Reward Percentages"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Package Reward Percentages"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <a id="btnCreateNew" asp-page="Create" class="btn btn-success" role="button"
                   aria-label="@($"{L["Create a New"]} {L["Package Reward Percentage"]}")" title="@($"{L["Create a New"]} {L["Package Reward Percentage"]}")">
                    <i class="fas fa-plus fa-2x"></i>
                </a>
                <h3 class="card-title float-right">@L["Count"]: @(Model.Percentages.Count)</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                        <tr>
                            <th>@L["Package"]</th>
                            <th>@L["Level"]</th>
                            <th>@L["Total Percentage"]</th>
                            <th>@L["TL Percentage"]</th>
                            <th>@L["RZW Percentage"]</th>
                            <th>@L["Created Date"]</th>
                            <th>@L["Modified Date"]</th>
                            <th style="width: 150px">@L["Actions"]</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.Percentages)
                        {
                            <tr>
                                <td>@item.PackageName</td>
                                <td>@L[$"Level {item.Level}"]</td>
                                <td>@((item.RzwPercentage + item.TlPercentage).ToString("N2"))%</td>
                                <td>@item.TlPercentage.ToString("N2")%</td>
                                <td>@item.RzwPercentage.ToString("N2")%</td>
                                <td>@item.CreatedDate.ToLocalTime().ToString("g")</td>
                                <td>@(item.ModifiedDate.HasValue? item.ModifiedDate.Value.ToString("g") : "-")</td>
                                <td>
                                    <a href="/Admin/PackageRewardPercentage/Edit?id=@item.Id" class="btn btn-info btn-sm">
                                        <i class="fas fa-pencil-alt"></i>
                                    </a>
                                    <a href="/Admin/PackageRewardPercentage/Delete?id=@item.Id" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

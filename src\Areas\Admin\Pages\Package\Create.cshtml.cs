using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Package;

public class CreateModel : PageModel
{
    private readonly PackageService _packageService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        PackageService packageService,
        IStringLocalizer<SharedResource> localizer)
    {
        _packageService = packageService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public PackageCreateViewModel ViewEntity { get; set; } = new();

    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            if (!ModelState.IsValid) return Page();

            var entity = new DbModel.Package
            {
                Name = ViewEntity.Name,
                Price = ViewEntity.Price,
                Description = ViewEntity.Description,
                Benefits = ViewEntity.Benefits,
                InviteLimit = ViewEntity.InviteLimit,
                EarningsCap = ViewEntity.EarningsCap,
                IsActive = ViewEntity.IsActive,
                Order = ViewEntity.Order,
                CreatedDate = DateTime.UtcNow
            };
            await _packageService.CreateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/Admin/Package"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            return Page();
        }
    }
}

public class PackageCreateViewModel
{
    public string Name { get; set; } = null!;
    public decimal Price { get; set; }
    public string? Description { get; set; }
    public string? Benefits { get; set; }
    public int? InviteLimit { get; set; }
    public decimal? EarningsCap { get; set; }
    public bool IsActive { get; set; } = true;
    public int Order { get; set; } = 0;
}

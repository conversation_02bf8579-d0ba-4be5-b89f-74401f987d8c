@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.Package.DeleteModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = $"{L["Delete"]} {L["Package"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@($"{L["Delete"]} {L["Package"]}")</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/package">@L["Packages"]</a></li>
                    <li class="breadcrumb-item active">@L["Delete"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                @if (Model.Entity != null)
                {
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <h5><i class="icon fas fa-exclamation-triangle"></i> @ViewData["WarningTitle"]</h5>
                                <ul class="mb-0">
                                    <li>@ViewData["WarningRecord"]</li>
                                    <li>@ViewData["WarningUnrecoverable"]</li>
                                </ul>
                            </div>

                            <table class="table table-bordered">
                                <tbody>
                                <tr>
                                    <th style="width: 200px">@L["Name"]</th>
                                    <td>@Model.Entity.Name</td>
                                </tr>
                                <tr>
                                    <th>@L["Price"]</th>
                                    <td>@Model.Entity.Price.ToString("N8")</td>
                                </tr>
                                <tr>
                                    <th>@L["Description"]</th>
                                    <td>@Model.Entity.Description</td>
                                </tr>
                                <tr>
                                    <th>@L["Invite Limit"]</th>
                                    <td>@(Model.Entity.InviteLimit.HasValue ? Model.Entity.InviteLimit.Value.ToString() : L["Unlimited"])</td>
                                </tr>
                                <tr>
                                    <th>@L["Earnings Cap"]</th>
                                    <td>@(Model.Entity.EarningsCap.HasValue ? Model.Entity.EarningsCap.Value.ToString("N8") : L["Unlimited"])</td>
                                </tr>
                                <tr>
                                    <th>@L["Status"]</th>
                                    <td>@(Model.Entity.IsActive ? L["Active"] : L["Inactive"])</td>
                                </tr>
                                <tr>
                                    <th>@L["Order"]</th>
                                    <td>@Model.Entity.Order</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                {
                    <div class="alert alert-danger mt-3">
                        <h5><i class="icon fas fa-ban"></i> @L["Error"]</h5>
                        @Model.ErrorMessage
                    </div>
                }
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="/Admin/Package" class="btn btn-default">
                        <i class="fas fa-arrow-left mr-1"></i> @L["Cancel"]
                    </a>
                    <form method="post">
                        <input type="hidden" asp-for="Id"/>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash mr-1"></i> @L["Delete"]
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

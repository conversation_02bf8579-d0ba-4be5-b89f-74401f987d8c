@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.Package.EditModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Edit Package"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Edit Package"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/package">@L["Packages"]</a></li>
                    <li class="breadcrumb-item active">@L["Edit"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <input type="hidden" asp-for="Entity.Id"/>
                <div class="card-body">
                    <div class="form-group">
                        <label asp-for="Entity.Name">@L["Name"]</label>
                        <input asp-for="Entity.Name" class="form-control" required/>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Price">@L["Price"]</label>
                        <input asp-for="Entity.Price" class="form-control" type="number" step="0.00000001" required/>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Description">@L["Description"]</label>
                        <textarea asp-for="Entity.Description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Benefits">@L["Benefits"] (@L["JSON Format"])</label>
                        <textarea asp-for="Entity.Benefits" class="form-control" rows="5" placeholder='{"Features":["Feature 1","Feature 2","Feature 3"]}'></textarea>
                        <small class="form-text text-muted">@L["Enter benefits as JSON array of features"]</small>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.InviteLimit">@L["Invite Limit"] (@L["Leave empty for unlimited"])</label>
                        <input asp-for="Entity.InviteLimit" class="form-control" type="number"/>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.EarningsCap">@L["Earnings Cap"] (@L["Leave empty for unlimited"])</label>
                        <input asp-for="Entity.EarningsCap" class="form-control" type="number" step="0.00000001"/>
                    </div>
                    <div class="form-group">
                        <label asp-for="Entity.Order">@L["Order"]</label>
                        <input asp-for="Entity.Order" class="form-control" type="number" required/>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input asp-for="Entity.IsActive" class="custom-control-input" />
                            <label asp-for="Entity.IsActive" class="custom-control-label">@L["Active"]</label>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/Package" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

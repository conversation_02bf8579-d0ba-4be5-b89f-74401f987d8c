using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Referral;
using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Areas.Admin.Pages.ReferralTree
{
    public class IndexModel : PageModel
    {
        private readonly AppDbContext _context;
        private readonly ReferralService _referralService;
        private readonly IStringLocalizer<SharedResource> _localizer;

        public IndexModel(
            AppDbContext context,
            ReferralService referralService,
            IStringLocalizer<SharedResource> localizer)
        {
            _context = context;
            _referralService = referralService;
            _localizer = localizer;
        }

        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [BindProperty(SupportsGet = true)]
        public int? SelectedUserId { get; set; }

        public DbModel.User? SelectedUser { get; set; }

        public DbModel.Package? ActivePackage { get; set; }

        public List<DbModel.User> Users { get; set; } = new List<DbModel.User>();

        public List<ReferralHierarchyItem> ReferralHierarchy { get; set; } = new List<ReferralHierarchyItem>();

        public async Task OnGetAsync()
        {
            // Get users for the dropdown
            IQueryable<DbModel.User> query = _context.Users.AsQueryable();

            // Apply search filter if provided
            if (!string.IsNullOrWhiteSpace(SearchTerm))
            {
                query = query.Where(u =>
                    u.Email.Contains(SearchTerm) ||
                    u.Name.Contains(SearchTerm) ||
                    u.Surname.Contains(SearchTerm) ||
                    u.PhoneNumber.Contains(SearchTerm) ||
                    u.ReferralCode.Contains(SearchTerm));
            }

            // Get users
            Users = await query
                .OrderBy(u => u.Name)
                .ThenBy(u => u.Surname)
                .ToListAsync();

            // If a user is selected, get their referral hierarchy
            if (SelectedUserId.HasValue)
            {
                SelectedUser = await _context.Users.FindAsync(SelectedUserId.Value);

                if (SelectedUser != null)
                {
                    // Get the active package for the selected user
                    ActivePackage = await _context.UserPackages
                        .Where(up => up.UserId == SelectedUserId.Value && up.Status == UserPackageStatus.Active)
                        .OrderByDescending(up => up.PackageId)
                        .Select(up => up.Package)
                        .FirstOrDefaultAsync();

                    ReferralHierarchy = await _referralService.GetReferralHierarchyAsync(SelectedUserId.Value);
                }
            }
            // If no user is selected but we have search results with only one user, select that user
            else if (Users.Count == 1)
            {
                SelectedUserId = Users[0].UserId;
                SelectedUser = Users[0];

                // Get the active package for the selected user
                ActivePackage = await _context.UserPackages
                    .Where(up => up.UserId == SelectedUserId.Value && up.Status == UserPackageStatus.Active)
                    .OrderByDescending(up => up.PackageId)
                    .Select(up => up.Package)
                    .FirstOrDefaultAsync();

                ReferralHierarchy = await _referralService.GetReferralHierarchyAsync(SelectedUserId.Value);
            }
        }
    }
}

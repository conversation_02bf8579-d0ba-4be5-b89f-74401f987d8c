using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.RzwSavings;

namespace RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans;

public class DeleteModel : PageModel
{
    private readonly IRzwSavingsPlanService _planService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteModel(IRzwSavingsPlanService planService, IStringLocalizer<SharedResource> localizer)
    {
        _planService = planService;
        _localizer = localizer;
    }

    [BindProperty]
    public RzwSavingsPlanViewModel Plan { get; set; } = new();

    public SweetAlert2Message? AlertMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var plan = await _planService.GetPlanByIdAsync(id.Value);
        if (plan == null)
        {
            return NotFound();
        }

        Plan = RzwSavingsPlanViewModel.FromEntity(plan);

        // Get statistics
        var stats = await _planService.GetPlanStatisticsAsync(plan.Id);
        Plan.TotalAccounts = stats.TotalAccounts;
        Plan.TotalLockedRzw = stats.TotalLockedRzw;
        Plan.TotalInterestPaid = stats.TotalInterestPaid;

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(int? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        try
        {
            var plan = await _planService.GetPlanByIdAsync(id.Value);
            if (plan == null)
            {
                return NotFound();
            }

            // Check if plan has active accounts
            var stats = await _planService.GetPlanStatisticsAsync(id.Value);
            if (stats.TotalAccounts > 0)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Warning"],
                    Text = _localizer["Cannot delete plan with active accounts. Please wait for all accounts to mature or be withdrawn."].Value,
                    Icon = "warning",
                    RedirectUrl = "/Admin/RzwSavingsPlans"
                };
                return Page();
            }

            await _planService.DeletePlanAsync(id.Value);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Plan deleted successfully"].Value,
                Icon = "success",
                RedirectUrl = "/Admin/RzwSavingsPlans"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["An error occurred while deleting the plan"].Value + ": " + ex.Message,
                Icon = "error",
                RedirectUrl = "/Admin/RzwSavingsPlans"
            };
            return Page();
        }
    }
}

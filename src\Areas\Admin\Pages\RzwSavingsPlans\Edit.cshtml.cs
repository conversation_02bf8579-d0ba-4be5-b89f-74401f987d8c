using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans;

public class EditModel : PageModel
{
    private readonly IRzwSavingsPlanService _planService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(IRzwSavingsPlanService planService, IStringLocalizer<SharedResource> localizer)
    {
        _planService = planService;
        _localizer = localizer;
    }

    [BindProperty]
    public EditPlanViewModel Plan { get; set; } = new();

    public SweetAlert2Message? AlertMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var plan = await _planService.GetPlanByIdAsync(id.Value);
        if (plan == null)
        {
            return NotFound();
        }

        Plan = new EditPlanViewModel
        {
            Id = plan.Id,
            Name = plan.Name,
            TermType = plan.TermType,
            TermDuration = plan.TermDuration,
            InterestRate = plan.InterestRate * 100m, // Convert to percentage
            MinRzwAmount = plan.MinRzwAmount,
            MaxRzwAmount = plan.MaxRzwAmount,
            IsActive = plan.IsActive,
            DisplayOrder = plan.DisplayOrder,
            Description = plan.Description
        };

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        // Custom validation for amount fields
        if (Plan.MinRzwAmount <= 0)
        {
            ModelState.AddModelError(nameof(Plan.MinRzwAmount), "Min Amount must be a positive whole number");
        }
        else if (Plan.MinRzwAmount != Math.Floor(Plan.MinRzwAmount))
        {
            ModelState.AddModelError(nameof(Plan.MinRzwAmount), "Min Amount must be a whole number");
        }

        if (Plan.MaxRzwAmount.HasValue)
        {
            if (Plan.MaxRzwAmount.Value <= 0)
            {
                ModelState.AddModelError(nameof(Plan.MaxRzwAmount), "Max Amount must be a positive whole number");
            }
            else if (Plan.MaxRzwAmount.Value != Math.Floor(Plan.MaxRzwAmount.Value))
            {
                ModelState.AddModelError(nameof(Plan.MaxRzwAmount), "Max Amount must be a whole number");
            }
            else if (Plan.MaxRzwAmount.Value <= Plan.MinRzwAmount)
            {
                ModelState.AddModelError(nameof(Plan.MaxRzwAmount), "Max Amount must be greater than Min Amount");
            }
        }

        if (!ModelState.IsValid)
        {
            return Page();
        }

        try
        {
            var plan = await _planService.GetPlanByIdAsync(Plan.Id);
            if (plan == null)
            {
                return NotFound();
            }

            plan.Name = Plan.Name;
            plan.TermType = Plan.TermType;
            plan.TermDuration = Plan.TermDuration;
            plan.InterestRate = Plan.InterestRate / 100m; // Convert percentage to decimal
            plan.MinRzwAmount = Plan.MinRzwAmount;
            plan.MaxRzwAmount = Plan.MaxRzwAmount;
            plan.IsActive = Plan.IsActive;
            plan.DisplayOrder = Plan.DisplayOrder;
            plan.Description = Plan.Description;
            plan.ModifiedDate = DateTime.UtcNow;

            await _planService.UpdatePlanAsync(plan);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Plan updated successfully"].Value,
                Icon = "success",
                RedirectUrl = "/Admin/RzwSavingsPlans"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["An error occurred while updating the plan"].Value + ": " + ex.Message,
                Icon = "error"
            };
            return Page();
        }
    }

    public class EditPlanViewModel
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        public string TermType { get; set; } = "Daily";

        [Required]
        [Range(1, int.MaxValue)]
        public int TermDuration { get; set; } = 1;

        [Required]
        [Range(0.0001, 100)]
        [Display(Name = "Interest Rate (%)")]
        public decimal InterestRate { get; set; } = 0.03m;

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Min Amount must be a positive whole number")]
        public decimal MinRzwAmount { get; set; } = 100m;

        [Range(1, int.MaxValue, ErrorMessage = "Max Amount must be a positive whole number")]
        public decimal? MaxRzwAmount { get; set; }

        public bool IsActive { get; set; } = true;

        [Range(1, int.MaxValue)]
        public int DisplayOrder { get; set; } = 1;

        [StringLength(500)]
        public string? Description { get; set; }
    }
}

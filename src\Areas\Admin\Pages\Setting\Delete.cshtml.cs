using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Setting;

public class DeleteModel : PageModel
{
    private readonly SettingService _settingService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteModel(
        SettingService settingService,
        IStringLocalizer<SharedResource> localizer)
    {
        _settingService = settingService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public int Id { get; set; }

    public DbModel.Setting? Entity { get; set; }
    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        Id = id;
        Entity = await _settingService.GetByIdAsync(id);

        if (Entity == null) return NotFound();

        ViewData["WarningTitle"] = _localizer["Warning"];
        ViewData["WarningRecord"] = _localizer["This setting record will be permanently deleted"];
        ViewData["WarningUnrecoverable"] = _localizer["This action is irreversible"];

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            await _settingService.DeleteAsync(Id);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Deletion was successful"],
                Icon = "success",
                RedirectUrl = "/Admin/Setting"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            Entity = await _settingService.GetByIdAsync(Id);
            return Page();
        }
    }
}

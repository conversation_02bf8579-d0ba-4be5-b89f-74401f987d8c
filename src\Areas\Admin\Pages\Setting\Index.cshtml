@page
@using Microsoft.Extensions.Localization
@model IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Settings"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<style>
    #btnCreateNew {
        display: none;
    }
    .btn-danger {
        display: none;
    }
</style>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Settings"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Settings"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <a id="btnCreateNew" asp-page="Create" class="btn btn-success" role="button"
                   aria-label="@($"{L["Create a New"]} {L["Setting"]}")" title="@($"{L["Create a New"]} {L["Setting"]}")" >
                    <i class="fas fa-plus fa-2x"></i>
                </a>
                <h3 class="card-title float-right">@L["Count"]: @(Model.Settings.Count)</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                    <tr>
                        <th>@L["Key"]</th>
                        <th>@L["Value"]</th>
                        <th>@L["Description"]</th>
                        <th>@L["Group"]</th>
                        <th>@L["Order"]</th>
                        <th>@L["Created Date"]</th>
                        <th style="width: 150px">@L["Actions"]</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var item in Model.Settings)
                    {
                        <tr>
                            <td>@item.Key</td>
                            <td>@item.Value</td>
                            <td>@item.Description</td>
                            <td>@item.Group</td>
                            <td>@item.Order</td>
                            <td>@item.CrDate.ToString("g")</td>
                            <td>
                                <a href="/Admin/Setting/Edit?id=@item.Id" class="btn btn-info btn-sm">
                                    <i class="fas fa-pencil-alt"></i>
                                </a>
                                <!-- Delete button hidden -->
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

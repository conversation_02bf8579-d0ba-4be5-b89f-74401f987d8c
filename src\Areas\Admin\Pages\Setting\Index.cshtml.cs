using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Setting;

namespace RazeWinComTr.Areas.Admin.Pages.Setting;

public class IndexModel : PageModel
{
    private readonly SettingService _settingService;

    public IndexModel(SettingService settingService)
    {
        _settingService = settingService;
    }

    public List<SettingViewModel> Settings { get; set; } = new();

    public async Task OnGetAsync()
    {
        Settings = await _settingService.GetListAsync();
    }
}

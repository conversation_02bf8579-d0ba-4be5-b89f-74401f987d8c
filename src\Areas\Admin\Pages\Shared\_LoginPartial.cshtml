﻿@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> Localizer

<ul class="navbar-nav">
    @if (User != null && User.Identity != null && User.Identity.IsAuthenticated)
    {
        <li class="nav-item">
            <a id="manage" class="nav-link text-dark" asp-page="/Account/Logout" title="Manage">@Localizer["Logout"]</a>
        </li>
    }
    else
    {
        <li class="nav-item">
            <a class="nav-link text-dark" id="register" asp-page="/Account/Register">@Localizer["Register"]</a>
        </li>
        <li class="nav-item">
            <a class="nav-link text-dark" id="login" asp-page="/Account/Login">@Localizer["Login"]</a>
        </li>
    }
</ul>

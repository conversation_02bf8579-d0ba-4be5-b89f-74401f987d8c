using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.Trade;

public class EditModel : PageModel
{
    private readonly ITradeService _tradeService;
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(
        ITradeService tradeService,
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _tradeService = tradeService;
        _context = context;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public TradeEditViewModel Entity { get; set; } = new();

    public string UserEmail { get; set; } = string.Empty;
    public string CoinName { get; set; } = string.Empty;

    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _tradeService.GetByIdAsync(id);

        if (entity == null) return NotFound();

        Entity = new TradeEditViewModel
        {
            Id = entity.Id,
            UserId = entity.UserId,
            CoinId = entity.CoinId,
            Type = entity.Type,
            Amount = entity.CoinAmount,
            Rate = entity.CoinRate
        };

        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == entity.UserId);
        UserEmail = user?.Email ?? "Unknown";

        var coin = await _context.Markets.FirstOrDefaultAsync(c => c.Id == entity.CoinId);
        CoinName = coin != null ? $"{coin.Name} ({coin.PairCode})" : "Unknown";

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid) return Page();
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            var entity = await _tradeService.GetByIdAsync(Entity.Id);
            if (entity == null) return NotFound();

            // Only update amount and rate, not the balances
            entity.CoinAmount = Entity.Amount;
            entity.CoinRate = Entity.Rate;
            entity.TryAmount = Entity.Amount * Entity.Rate;

            await _tradeService.UpdateAsync(entity, _context);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = "/Admin/Trade"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = ex.Message;
            return Page();
        }
    }
}

public class TradeEditViewModel
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int CoinId { get; set; }
    public TradeType Type { get; set; }
    public decimal Amount { get; set; }
    public decimal Rate { get; set; }
}

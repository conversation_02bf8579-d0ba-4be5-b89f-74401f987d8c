@page
@model RazeWinComTr.Areas.Admin.Pages.TransactionReversal.DepositReversalModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Enums
@inject IStringLocalizer<SharedResource> L
@{
    ViewData["Title"] = L["Reverse Deposit"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>@L["Reverse Deposit"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/Admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/Admin/TransactionReversal">@L["Transaction Reversal"]</a></li>
                    <li class="breadcrumb-item active">@L["Reverse Deposit"]</li>
                </ol>
            </div>
        </div>
    </div>
</section>

<section class="content">
    <div class="container-fluid">
        @if (Model.AlertMessage != null)
        {
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    Swal.fire({
                        title: '@Model.AlertMessage.Title',
                        text: '@Model.AlertMessage.Text',
                        icon: '@Model.AlertMessage.Icon',
                        confirmButtonText: '@L["OK"]'
                    }).then((result) => {
                        @if (!string.IsNullOrEmpty(Model.AlertMessage.RedirectUrl))
                        {
                                <text>
                                window.location.href = '@Model.AlertMessage.RedirectUrl';
                                </text>
                        }
                    });
                });
            </script>
        }

        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@L["Deposit Details"]</h3>
            </div>
            <div class="card-body">
                @if (Model.Deposit != null)
                {
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@L["User"]</label>
                                <input type="text" class="form-control" value="@Model.Deposit.UserEmail" readonly />
                            </div>
                            <div class="form-group">
                                <label>@L["Payment Type"]</label>
                                <input type="text" class="form-control" value="@Model.Deposit.DepositType" readonly />
                            </div>
                            <div class="form-group">
                                <label>@L["Amount"]</label>
                                <input type="text" class="form-control" value="@Model.Deposit.Amount.ToString("N2") @L["Currency_Symbol"]" readonly />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>@L["Status"]</label>
                                <input type="text" class="form-control" value="@L["Approved"]" readonly />
                            </div>
                            <div class="form-group">
                                <label>@L["Reward Status"]</label>
                                @{
                                    string rewardStatusText = Model.Deposit.RewardStatus switch
                                    {
                                        DepositRewardStatus.Pending => L["Pending"],
                                        DepositRewardStatus.Processed => L["Processed"],
                                        DepositRewardStatus.Distributed => L["Distributed"],
                                        DepositRewardStatus.NoRewards => L["No Rewards"],
                                        DepositRewardStatus.Failed => L["Failed"],
                                        _ => L["Unknown"]
                                    };
                                }
                                <input type="text" class="form-control" value="@rewardStatusText" readonly />
                            </div>
                            <div class="form-group">
                                <label>@L["Created Date"]</label>
                                <input type="text" class="form-control" value="@Model.Deposit.CreatedDate.ToLocalTime().ToString("g")" readonly />
                            </div>
                        </div>
                    </div>

                    <div class="card mt-4">
                        <div class="card-header bg-primary">
                            <h3 class="card-title">@L["Reversal Eligibility Check"]</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <h5 class="mb-3">
                                        @if (Model.CanReverse)
                                        {
                                            <span class="badge badge-success">@L["Eligible for Reversal"]</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-danger">@L["Not Eligible for Reversal"]</span>
                                        }
                                    </h5>
                                    <p>@Model.ReversalMessage</p>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-12">
                                    <h5>@L["Verification Checks"]</h5>
                                    <ul class="list-group">
                                        @foreach (var checkItem in Model.ReversalChecks)
                                        {
                                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="mr-2">@checkItem.Description</span>
                                                    @if (!string.IsNullOrEmpty(checkItem.Details))
                                                    {
                                                        <small class="text-muted d-block">@checkItem.Details</small>
                                                    }
                                                </div>
                                                @if (checkItem.Passed)
                                                {
                                                    <span class="badge badge-success badge-pill"><i class="fas fa-check"></i></span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-danger badge-pill"><i class="fas fa-times"></i></span>
                                                }
                                            </li>
                                        }
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <form method="post">
                                <input type="hidden" asp-for="Id" />
                                <button type="submit" class="btn btn-danger" @(Model.CanReverse ? "" : "disabled")>
                                    <i class="fas fa-undo mr-1"></i> @L["Reverse Deposit"]
                                </button>
                                <a href="/Admin/TransactionReversal" class="btn btn-default ml-2">
                                    <i class="fas fa-arrow-left mr-1"></i> @L["Back to List"]
                                </a>
                            </form>
                        </div>
                    </div>
                }
                else
                {
                    <div class="alert alert-warning">
                        @L["Deposit not found"]
                    </div>
                }
            </div>
        </div>
    </div>
</section>

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;
using RazeWinComTr.Areas.Admin.ViewModels.TransactionReversal;
using RazeWinComTr.Models;
using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.Pages.TransactionReversal;

public class DepositReversalModel : PageModel
{
    private readonly TransactionReversalService _reversalService;
    private readonly DepositService _depositService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DepositReversalModel(
        TransactionReversalService reversalService,
        DepositService depositService,
        IStringLocalizer<SharedResource> localizer)
    {
        _reversalService = reversalService;
        _depositService = depositService;
        _localizer = localizer;
    }

    [BindProperty(SupportsGet = true)]
    public int Id { get; set; }

    public DepositViewModel? Deposit { get; set; }

    public bool CanReverse { get; set; }

    public string ReversalMessage { get; set; } = string.Empty;

    public List<ReversalCheckItem> ReversalChecks { get; set; } = new();

    public SweetAlert2Message? AlertMessage { get; set; }

    public async Task<IActionResult> OnGetAsync()
    {
        Deposit = await _depositService.GetDepositViewModelByIdAsync(Id);

        if (Deposit == null)
            return NotFound();

        var result = await _reversalService.CheckDepositReversalEligibilityAsync(Id);

        CanReverse = result.CanReverse;
        ReversalMessage = result.Message;
        ReversalChecks = result.Checks;

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var success = await _reversalService.ReverseDepositAsync(Id);

            if (success)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Success"],
                    Text = _localizer["Deposit has been successfully reversed"],
                    Icon = "success",
                    RedirectUrl = "/Admin/TransactionReversal/Index"
                };
            }
            else
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Error"],
                    Text = _localizer["Failed to reverse deposit"],
                    Icon = "error"
                };
            }

            // Sayfayı yeniden yükle
            Deposit = await _depositService.GetDepositViewModelByIdAsync(Id);
            var result = await _reversalService.CheckDepositReversalEligibilityAsync(Id);
            CanReverse = result.CanReverse;
            ReversalMessage = result.Message;
            ReversalChecks = result.Checks;

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = ex.Message,
                Icon = "error"
            };

            // Sayfayı yeniden yükle
            Deposit = await _depositService.GetDepositViewModelByIdAsync(Id);
            var result = await _reversalService.CheckDepositReversalEligibilityAsync(Id);
            CanReverse = result.CanReverse;
            ReversalMessage = result.Message;
            ReversalChecks = result.Checks;

            return Page();
        }
    }
}

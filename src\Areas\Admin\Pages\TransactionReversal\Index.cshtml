@page
@model RazeWinComTr.Areas.Admin.Pages.TransactionReversal.IndexModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Enums
@inject IStringLocalizer<SharedResource> L
@{
    ViewData["Title"] = L["Transaction Reversal"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<section class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1>@L["Transaction Reversal"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/Admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Transaction Reversal"]</li>
                </ol>
            </div>
        </div>
    </div>
</section>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">@L["Approved Deposits"]</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                        <tr>
                            <th>@L["User"]</th>
                            <th>@L["Payment Type"]</th>
                            <th>@L["Amount"]</th>
                            <th>@L["Status"]</th>
                            <th>@L["Reward Status"]</th>
                            <th>@L["Created Date"]</th>
                            <th>@L["Actions"]</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.Deposits)
                        {
                            <tr>
                                <td>@item.UserEmail</td>
                                <td>
                                    @if (!string.IsNullOrEmpty(item.DepositType))
                                    {
                                        @if (item.IsCryptoDeposit)
                                        {
                                            <span class="badge badge-primary">@item.DepositType</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-info">@item.DepositType</span>
                                        }
                                    }
                                    else
                                    {
                                        <span>-</span>
                                    }
                                </td>
                                <td>@item.Amount.ToString("N2") @L["Currency_Symbol"]</td>
                                <td>
                                    <span class="badge badge-success">@L["Approved"]</span>
                                </td>
                                <td>
                                    @switch (item.RewardStatus)
                                    {
                                        case DepositRewardStatus.Pending:
                                            <span class="badge badge-warning">@L["Pending"]</span>
                                            break;
                                        case DepositRewardStatus.Processed:
                                            <span class="badge badge-info">@L["Processed"]</span>
                                            break;
                                        case DepositRewardStatus.Distributed:
                                            <span class="badge badge-success">@L["Distributed"]</span>
                                            break;
                                        case DepositRewardStatus.NoRewards:
                                            <span class="badge badge-secondary">@L["No Rewards"]</span>
                                            break;
                                        case DepositRewardStatus.Failed:
                                            <span class="badge badge-danger">@L["Failed"]</span>
                                            break;
                                    }
                                </td>
                                <td>@item.CreatedDate.ToLocalTime().ToString("g")</td>
                                <td>
                                    @if (Model.CanReverseDeposits[item.Id])
                                    {
                                        <a href="/Admin/TransactionReversal/DepositReversal?id=@item.Id" class="btn btn-danger btn-sm">
                                            <i class="fas fa-undo"></i> @L["Reverse Deposit"]
                                        </a>
                                    }
                                    else
                                    {
                                        <button class="btn btn-secondary btn-sm" disabled>
                                            <i class="fas fa-undo"></i> @L["Cannot Reverse"]
                                        </button>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        $(document).ready(function () {
            $('.datatable').DataTable({
                "order": [[5, "desc"]]
            });
        });
    </script>
}

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Package;
using RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction;
using System.Net;

namespace RazeWinComTr.Areas.Admin.Pages.UserPackage;

public class CreateModel : PageModel
{
    private readonly UserPackageService _userPackageService;
    private readonly PackageService _packageService;
    private readonly UserService _userService;
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        UserPackageService userPackageService,
        PackageService packageService,
        UserService userService,
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _userPackageService = userPackageService;
        _packageService = packageService;
        _userService = userService;
        _context = context;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty]
    public UserPackageCreateViewModel ViewEntity { get; set; } = new();

    public List<PackageViewModel> Packages { get; set; } = new();
    public List<DbModel.User> Users { get; set; } = new();

    public async Task OnGetAsync()
    {
        Packages = await _packageService.GetListAsync();
        Users = await _userService.GetAllUsersAsync();
        ViewEntity.PurchaseDate = DateTime.UtcNow;
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            if (!ModelState.IsValid)
            {
                Packages = await _packageService.GetListAsync();
                Users = await _userService.GetAllUsersAsync();
                return Page();
            }

            var package = await _packageService.GetByIdAsync(ViewEntity.PackageId);
            if (package == null)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Error"],
                    Text = _localizer["Package not found"],
                    Icon = "error"
                };
                Packages = await _packageService.GetListAsync();
                Users = await _userService.GetAllUsersAsync();
                return Page();
            }

            var user = await _userService.GetByIdAsync(ViewEntity.UserId);
            if (user == null)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Error"],
                    Text = _localizer["User not found"],
                    Icon = "error"
                };
                Packages = await _packageService.GetListAsync();
                Users = await _userService.GetAllUsersAsync();
                return Page();
            }

            // Convert local dates to UTC for storage
            var purchaseDate = DateTimeFormatHelper.ConvertToUtc(ViewEntity.PurchaseDate);
            var expiryDate = DateTimeFormatHelper.ConvertToUtc(ViewEntity.ExpiryDate);

            var entity = new DbModel.UserPackage
            {
                UserId = ViewEntity.UserId,
                PackageId = ViewEntity.PackageId,
                PurchaseDate = purchaseDate,
                ExpiryDate = expiryDate,
                Status = (UserPackageStatus)ViewEntity.Status,
                CreatedDate = DateTime.UtcNow
            };

            await _userPackageService.CreateAsync(entity);

            // Balance transaction is automatically recorded in UserPackageService

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["User package created successfully"],
                Icon = "success",
                RedirectUrl = "/Admin/UserPackage"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = ex.Message,
                Icon = "error"
            };
            Packages = await _packageService.GetListAsync();
            Users = await _userService.GetAllUsersAsync();
            return Page();
        }
    }
}

public class UserPackageCreateViewModel
{
    public int UserId { get; set; }
    public int PackageId { get; set; }
    public DateTime PurchaseDate { get; set; } = DateTime.UtcNow;
    public DateTime? ExpiryDate { get; set; }
    public int Status { get; set; } = 1; // Active by default

}

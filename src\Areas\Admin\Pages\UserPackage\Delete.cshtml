@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.UserPackage.DeleteModel
@inject IStringLocalizer<SharedResource> L
@using RazeWinComTr.Areas.Admin.Helpers

@{
    ViewData["Title"] = $"{L["Delete"]} {L["User Package"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@($"{L["Delete"]} {L["User Package"]}")</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/userpackage">@L["User Packages"]</a></li>
                    <li class="breadcrumb-item active">@L["Delete"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-body">
                @if (Model.Entity != null)
                {
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <h5><i class="icon fas fa-exclamation-triangle"></i> @ViewData["WarningTitle"]</h5>
                                <ul class="mb-0">
                                    <li>@ViewData["WarningRecord"]</li>
                                    <li>@ViewData["WarningUnrecoverable"]</li>
                                    <li>@L["Warning: Deleting this package may affect referral rewards"]</li>
                                </ul>
                            </div>

                            <table class="table table-bordered">
                                <tbody>
                                <tr>
                                    <th style="width: 200px">@L["User"]</th>
                                    <td>@Model.UserFullName</td>
                                </tr>
                                <tr>
                                    <th>@L["Package"]</th>
                                    <td>@Model.PackageName</td>
                                </tr>
                                <tr>
                                    <th>@L["Purchase Date"]</th>
                                    <td>@DateTimeFormatHelper.FormatForDisplay(Model.Entity.PurchaseDate)</td>
                                </tr>
                                @if (Model.Entity.ExpiryDate.HasValue)
                                {
                                    <tr>
                                        <th>@L["Expiry Date"]</th>
                                        <td>@DateTimeFormatHelper.FormatForDisplay(Model.Entity.ExpiryDate)</td>
                                    </tr>
                                }
                                <tr>
                                    <th>@L["Status"]</th>
                                    <td>
                                        @switch (Model.Entity.Status)
                                        {
                                            case RazeWinComTr.Areas.Admin.DbModel.UserPackageStatus.Active:
                                                <span class="badge badge-success">@L["Active"]</span>
                                                break;
                                            case RazeWinComTr.Areas.Admin.DbModel.UserPackageStatus.Expired:
                                                <span class="badge badge-warning">@L["Expired"]</span>
                                                break;
                                            case RazeWinComTr.Areas.Admin.DbModel.UserPackageStatus.Cancelled:
                                                <span class="badge badge-danger">@L["Cancelled"]</span>
                                                break;
                                        }
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                }

                @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                {
                    <div class="alert alert-danger mt-3">
                        <h5><i class="icon fas fa-ban"></i> @L["Error"]</h5>
                        @Model.ErrorMessage
                    </div>
                }
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <a href="/Admin/UserPackage" class="btn btn-default">
                        <i class="fas fa-arrow-left mr-1"></i> @L["Cancel"]
                    </a>
                    <form method="post">
                        <input type="hidden" asp-for="Id"/>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash mr-1"></i> @L["Delete"]
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

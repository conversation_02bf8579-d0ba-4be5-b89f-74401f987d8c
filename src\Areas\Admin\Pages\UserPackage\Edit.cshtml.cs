using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels;

namespace RazeWinComTr.Areas.Admin.Pages.UserPackage;

public class EditModel : PageModel
{
    private readonly UserPackageService _userPackageService;
    private readonly PackageService _packageService;
    private readonly UserService _userService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EditModel(
        UserPackageService userPackageService,
        PackageService packageService,
        UserService userService,
        IStringLocalizer<SharedResource> localizer)
    {
        _userPackageService = userPackageService;
        _packageService = packageService;
        _userService = userService;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty]
    public UserPackageEditViewModel Entity { get; set; } = new();

    public string UserFullName { get; set; } = string.Empty;
    public string PackageName { get; set; } = string.Empty;
    public string? ErrorMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int id)
    {
        var entity = await _userPackageService.GetByIdAsync(id);

        if (entity == null) return NotFound();

        Entity = new UserPackageEditViewModel
        {
            Id = entity.Id,
            UserId = entity.UserId,
            PackageId = entity.PackageId,
            PurchaseDate = entity.PurchaseDate,
            ExpiryDate = entity.ExpiryDate,
            Status = (int)entity.Status
        };

        var user = await _userService.GetByIdAsync(entity.UserId);
        if (user != null)
        {
            UserFullName = $"{user.Name} {user.Surname} ({user.Email})";
        }

        var package = await _packageService.GetByIdAsync(entity.PackageId);
        if (package != null)
        {
            PackageName = $"{package.Name} ({package.Price.ToString("N8")})";
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            var entity = await _userPackageService.GetByIdAsync(Entity.Id);
            if (entity == null)
            {
                return NotFound();
            }

            // Convert local dates to UTC for storage
            var purchaseDate = DateTimeFormatHelper.ConvertToUtc(Entity.PurchaseDate);
            var expiryDate = DateTimeFormatHelper.ConvertToUtc(Entity.ExpiryDate);

            entity.PurchaseDate = purchaseDate;
            entity.ExpiryDate = expiryDate;
            entity.Status = (UserPackageStatus)Entity.Status;
            entity.ModifiedDate = DateTime.UtcNow;

            await _userPackageService.UpdateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["User package updated successfully"],
                Icon = "success",
                RedirectUrl = "/Admin/UserPackage"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = ex.Message,
                Icon = "error"
            };
            return Page();
        }
    }
}

public class UserPackageEditViewModel
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int PackageId { get; set; }
    public DateTime PurchaseDate { get; set; }
    public DateTime? ExpiryDate { get; set; }
    public int Status { get; set; }
}

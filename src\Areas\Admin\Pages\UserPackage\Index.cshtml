@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.UserPackage.IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["User Packages"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["User Packages"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["User Packages"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <a id="btnCreateNew" asp-page="Create" class="btn btn-success" role="button"
                   aria-label="@($"{L["Create a New"]} {L["User Package"]}")" title="@($"{L["Create a New"]} {L["User Package"]}")">
                    <i class="fas fa-plus fa-2x"></i>
                </a>
                <h3 class="card-title float-right">@L["Count"]: @(Model.UserPackages.Count)</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                    <tr>
                        <th>@L["User"]</th>
                        <th>@L["Package"]</th>
                        <th>@L["Purchase Date"]</th>
                        <th>@L["Expiry Date"]</th>
                        <th>@L["Status"]</th>
                        <th style="width: 150px">@L["Actions"]</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var item in Model.UserPackages)
                    {
                        <tr>
                            <td>@item.UserFullName (@item.UserEmail)</td>
                            <td>@item.PackageName</td>
                            <td>@item.PurchaseDate.ToString("g")</td>
                            <td>@(item.ExpiryDate.HasValue ? item.ExpiryDate.Value.ToString("g") : L["No Expiry"])</td>
                            <td>
                                @switch (item.Status)
                                {
                                    case RazeWinComTr.Areas.Admin.DbModel.UserPackageStatus.Active:
                                        <span class="badge badge-success">@L["Active"]</span>
                                        break;
                                    case RazeWinComTr.Areas.Admin.DbModel.UserPackageStatus.Expired:
                                        <span class="badge badge-warning">@L["Expired"]</span>
                                        break;
                                    case RazeWinComTr.Areas.Admin.DbModel.UserPackageStatus.Cancelled:
                                        <span class="badge badge-danger">@L["Cancelled"]</span>
                                        break;
                                }
                            </td>
                            <td>
                                <a href="/Admin/UserPackage/Edit?id=@item.Id" class="btn btn-info btn-sm">
                                    <i class="fas fa-pencil-alt"></i>
                                </a>
                                <a href="/Admin/UserPackage/Delete?id=@item.Id" class="btn btn-danger btn-sm">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

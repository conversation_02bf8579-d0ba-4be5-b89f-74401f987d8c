using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Package;

namespace RazeWinComTr.Areas.Admin.Pages.UserPackage;

public class IndexModel : PageModel
{
    private readonly UserPackageService _userPackageService;

    public IndexModel(UserPackageService userPackageService)
    {
        _userPackageService = userPackageService;
    }

    public List<UserPackageViewModel> UserPackages { get; set; } = new();

    public async Task OnGetAsync()
    {
        UserPackages = await _userPackageService.GetListAsync();
    }
}

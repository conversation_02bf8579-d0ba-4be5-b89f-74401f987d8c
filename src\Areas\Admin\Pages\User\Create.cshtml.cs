using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Enums;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.User;

namespace RazeWinComTr.Areas.Admin.Pages.User;

public class CreateModel : PageModel
{
    private readonly IStringLocalizer<SharedResource> _localizer;

    [BindProperty]
    public UserCreateViewModel ViewModelItem { get; set; } = default!;
    private readonly AppDbContext _context;
    public SweetAlert2Message? AlertMessage { get; set; }

    public CreateModel(IStringLocalizer<SharedResource> localizer, AppDbContext context)
    {
        _localizer = localizer;
        _context = context;
        ViewModelItem = new UserCreateViewModel { IsActive = true };
    }

    public IActionResult OnGet()
    {
        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            var userId = HttpContext.User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            var user = new DbModel.User
            {
                Email = ViewModelItem.Email,
                Name = ViewModelItem.Name.Trim(),
                Surname = ViewModelItem.Surname.Trim(),
                PasswordHash = HashHelper.getHash(ViewModelItem.Password),
                IsActive = ViewModelItem.IsActive ? 1 : 0,
                CrDate = DateTime.UtcNow,
                UserRoleRelations = [new() { IsActive = 1, RoleId = (int)Roller.Admin }]
            };

            _context.Users.Add(user);
            await _context.SaveChangesAsync();

            // Set success alert
            AlertMessage = new SweetAlert2Message
            {
                Title = LocalizerHelper.get(_localizer, "Success"),
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = Url.Page("./Index")
            };
        }
        catch (Exception ex)
        {
            // Set error alert
            AlertMessage = new SweetAlert2Message
            {
                Title = "Error",
                Text = $"An error occurred while creating the device group: {ex.Message}",
                Icon = "error"
            };
        }
        return Page();
    }

    // Helper methods removed as they are no longer needed
}
@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.User.DeleteModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = $"{L["Delete"]} {L["User"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}
@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>

    Model.AlertMessage = null;
}
<div class="container-fluid">
    <h1 class="mt-4">@($"{L["Delete"]} {L["User"]}")</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/Admin">@L["Dashboard"]</a></li>
        <li class="breadcrumb-item"><a asp-page="./Index">@L["Users"]</a></li>
        <li class="breadcrumb-item active">@L["Delete"]</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-user-minus me-1"></i>
            @($"{L["Delete"]} {L["User"]}")
        </div>
        <div class="card-body">
            <h3>@L["Are you sure you want to delete this user?"]</h3>
            <div>
                <dl class="row">
                    <dt class="col-sm-2">@L["Email"]</dt>
                    <dd class="col-sm-10">@Model.ViewModelItem.Email</dd>
                    <dt class="col-sm-2">@L["Status"]</dt>
                    <dd class="col-sm-10">
                        @if (Model.ViewModelItem.IsActive == 1)
                        {
                            <span class="badge bg-success">@L["Active"]</span>
                        }
                        else
                        {
                            <span class="badge bg-danger">@L["Inactive"]</span>
                        }
                    </dd>
                    <dt class="col-sm-2">@L["Created Date"]</dt>
                    <dd class="col-sm-10">@Model.ViewModelItem.CrDate.ToLocalTime().ToString("dd/MM/yyyy HH:mm")</dd>
                </dl>
            </div>
            <form method="post">
                <input type="hidden" asp-for="ViewModelItem.UserId" />
                <button type="submit" class="btn btn-danger">Delete</button>
                <a asp-page="./Index" class="btn btn-secondary">Back to List</a>
            </form>
        </div>
    </div>
</div>
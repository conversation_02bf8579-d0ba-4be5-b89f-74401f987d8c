using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.User;

namespace RazeWinComTr.Areas.Admin.Pages.User;

public class DeleteModel : PageModel
{
    private readonly IStringLocalizer<SharedResource> _localizer;

    [BindProperty]
    public UserViewModel ViewModelItem { get; set; } = default!;
    private readonly AppDbContext _context;
    public SweetAlert2Message? AlertMessage { get; set; }

    public DeleteModel(IStringLocalizer<SharedResource> localizer, AppDbContext context)
    {
        _localizer = localizer;
        _context = context;
    }

    public async Task<IActionResult> OnGetAsync(int? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var user = await _context.Users.FirstOrDefaultAsync(m => m.UserId == id);
        if (user == null)
        {
            return NotFound();
        }

        ViewModelItem = new UserViewModel
        {
            UserId = user.UserId,
            Email = user.Email,
            IsActive = user.IsActive,
            CrDate = user.CrDate
        };

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(int? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var userId = HttpContext.User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        var user = await _context.Users.FindAsync(id);
        if (user == null)
        {
            return NotFound();
        }

        user.IsActive = 0;

        try
        {
            await _context.SaveChangesAsync();
            AlertMessage = new SweetAlert2Message
            {
                Title = LocalizerHelper.get(_localizer, "Success"),
                Text = LocalizerHelper.get(_localizer, "Deletion was successful"),
                Icon = "success",
                RedirectUrl = Url.Page("./Index")
            };
            return Page();
        }
        catch (Exception ex)
        {
            if (!await UserExists(id.Value))
            {
                return NotFound();
            }
            // Set error alert
            AlertMessage = new SweetAlert2Message
            {
                Title = "Error",
                Text = $"An error occurred while creating the device group: {ex.Message}",
                Icon = "error"
            };
            return Page();
        }
    }

    private async Task<bool> UserExists(int id)
    {
        return await _context.Users.AnyAsync(e => e.UserId == id);
    }
}
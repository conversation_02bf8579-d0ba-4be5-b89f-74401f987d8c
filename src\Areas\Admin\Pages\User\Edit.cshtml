@page
@using Microsoft.Extensions.Localization
@model RazeWinComTr.Areas.Admin.Pages.User.EditModel
@inject IStringLocalizer<SharedResource> L
@{
    ViewData["Title"] = $"{L["Edit"]} {L["User"]}";
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}
@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>

    Model.AlertMessage = null;
}
<div class="container-fluid">
    <h1 class="mt-4">@L["Edit"]</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/Admin">@L["Dashboard"]</a></li>
        <li class="breadcrumb-item"><a asp-page="./Index">@L["Users"]</a></li>
        <li class="breadcrumb-item active">@L["Edit"]</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-user-edit me-1"></i>
            @L["Edit"]
        </div>
        <div class="card-body">
            <form method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <input type="hidden" asp-for="ViewModelItem.UserId" />
                <!-- FullName -->
                <div class="form-group">
                    <label asp-for="ViewModelItem.Email" class="control-label">@L["Email"]</label>
                    <input asp-for="ViewModelItem.Email" class="form-control"
                           placeholder="@L["Email"]" readonly />
                </div>
                <!-- Name -->
                <div class="form-group">
                    <label asp-for="ViewModelItem.Name" class="control-label">@L["Name"]</label>
                    <input asp-for="ViewModelItem.Name" class="form-control"
                           placeholder="@L["Name"]" />
                    <span asp-validation-for="ViewModelItem.Name" class="text-danger"></span>
                </div>

                <!-- Surname -->
                <div class="form-group">
                    <label asp-for="ViewModelItem.Surname" class="control-label">@L["Surname"]</label>
                    <input asp-for="ViewModelItem.Surname" class="form-control"
                           placeholder="@L["Surname"]" />
                    <span asp-validation-for="ViewModelItem.Surname" class="text-danger"></span>
                </div>

                <!-- Phone Number -->
                <div class="form-group">
                    <label asp-for="ViewModelItem.PhoneNumber" class="control-label">@L["Phone Number"]</label>
                    <input asp-for="ViewModelItem.PhoneNumber" class="form-control"
                           placeholder="@L["Phone Number"]" />
                    <span asp-validation-for="ViewModelItem.PhoneNumber" class="text-danger"></span>
                </div>

                <!-- Identity Number -->
                <div class="form-group">
                    <label asp-for="ViewModelItem.IdentityNumber" class="control-label">@L["Identity Number"]</label>
                    <input asp-for="ViewModelItem.IdentityNumber" class="form-control"
                           placeholder="@L["Identity Number"]" />
                    <span asp-validation-for="ViewModelItem.IdentityNumber" class="text-danger"></span>
                </div>

                <!-- IBAN -->
                <div class="form-group">
                    <label asp-for="ViewModelItem.Iban" class="control-label">@L["IBAN"]</label>
                    <input asp-for="ViewModelItem.Iban" class="form-control"
                           placeholder="@L["IBAN"]" />
                    <span asp-validation-for="ViewModelItem.Iban" class="text-danger"></span>
                </div>

                <!-- Balance (Commented out for future use) -->
                @*
                <div class="form-group">
                    <label asp-for="ViewModelItem.Balance" class="control-label">@L["Balance"]</label>
                    <input asp-for="ViewModelItem.Balance" class="form-control"
                           placeholder="@L["Balance"]" readonly />
                    <span asp-validation-for="ViewModelItem.Balance" class="text-danger"></span>
                </div>
                *@

                <!-- Password -->
                <div class="form-group">
                    <label asp-for="ViewModelItem.Password" class="control-label">@L["Password"]</label>
                    <input asp-for="ViewModelItem.Password" class="form-control"
                           placeholder="@L["Password"]" />
                    <span asp-validation-for="ViewModelItem.Password" class="text-danger"></span>
                </div>

                <!-- Active Status -->
                <div class="form-group">
                    <label asp-for="ViewModelItem.IsActive">@L["Is Active"]</label>

                    <input asp-for="ViewModelItem.IsActive" type="checkbox" class="form-control"
                           data-bootstrap-switch
                           data-on-color="success"
                           data-off-color="danger"
                           data-on-text="@L["Active"]"
                           data-off-text="@L["Passive"]" />

                    <span asp-validation-for="ViewModelItem.IsActive" class="text-danger"></span>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-primary">@L["Save"]</button>
                        <a asp-page="./Index" class="btn btn-secondary">@L["Back to List"]</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
    <script>
        // Override the default validation message for IBAN
        $(function() {
            // Set the validation message for IBAN
            $.validator.messages.validIban = "@L["Enter a valid IBAN"]";
        });
    </script>
    <script src="~/site/pages/admin/user/edit.js"></script>
}
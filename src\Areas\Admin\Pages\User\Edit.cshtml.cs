using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.User;
using RazeWinComTr.Helpers;

namespace RazeWinComTr.Areas.Admin.Pages.User;

public class EditModel : PageModel
{
    private readonly IStringLocalizer<SharedResource> _localizer;

    [BindProperty]
    public UserEditViewModel ViewModelItem { get; set; } = default!;
    public SweetAlert2Message? AlertMessage { get; set; }

    private readonly AppDbContext _context;

    public EditModel(IStringLocalizer<SharedResource> localizer, AppDbContext context)
    {
        _localizer = localizer;
        _context = context;
    }

    public async Task<IActionResult> OnGetAsync(int? id)
    {
        if (id == null)
        {
            return NotFound();
        }

        var user = await _context.Users.FirstOrDefaultAsync(m => m.UserId == id);
        if (user == null)
        {
            return NotFound();
        }

        ViewModelItem = new UserEditViewModel
        {
            Email = user.Email,
            UserId = user.UserId,
            Name = user.Name,
            Surname = user.Surname,
            PhoneNumber = user.PhoneNumber,
            IdentityNumber = user.IdentityNumber,
            Iban = user.Iban,
            // Balance = user.Balance,
            Password = string.Empty,
            IsActive = user.IsActive == 1,
            CrDate = user.CrDate
        };

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid)
        {
            return Page();
        }

        var userId = HttpContext.User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        var user = await _context.Users.FindAsync(ViewModelItem.UserId);
        if (user == null)
        {
            return NotFound();
        }
        if (!string.IsNullOrEmpty(ViewModelItem.Password))
        {
            user.PasswordHash = HashHelper.getHash(ViewModelItem.Password);
        }
        // Directly assign values from the form
        user.Name = ViewModelItem.Name.Trim();
        user.Surname = ViewModelItem.Surname.Trim();
        user.PhoneNumber = ViewModelItem.PhoneNumber?.Trim() ?? string.Empty;
        user.IdentityNumber = ViewModelItem.IdentityNumber?.Trim() ?? string.Empty;

        // Clean and validate IBAN using ValidationHelper
        if (!string.IsNullOrWhiteSpace(ViewModelItem.Iban))
        {
            string cleanedIban = ValidationHelper.CleanIban(ViewModelItem.Iban);
            if (ValidationHelper.IsValidIban(cleanedIban))
            {
                user.Iban = ValidationHelper.FormatIban(cleanedIban);
            }
            else
            {
                ModelState.AddModelError("ViewModelItem.Iban", _localizer["Enter a valid IBAN"]);
                return Page();
            }
        }
        else
        {
            user.Iban = null;
        }

        // user.Balance = ViewModelItem.Balance;
        user.IsActive = ViewModelItem.IsActive ? 1 : 0;

        try
        {
            await _context.SaveChangesAsync();
            // Set success alert
            AlertMessage = new SweetAlert2Message
            {
                Title = LocalizerHelper.get(_localizer, "Success"),
                Text = _localizer["Successfully updated"],
                Icon = "success",
                RedirectUrl = Url.Page("./Index")
            };
            return Page();
        }
        catch (Exception ex)
        {
            if (!await UserExists(ViewModelItem.UserId))
            {
                return NotFound();
            }
            // Set error alert
            AlertMessage = new SweetAlert2Message
            {
                Title = "Error",
                Text = $"An error occurred while creating the device group: {ex.Message}",
                Icon = "error"
            };
            return Page();
        }
    }

    private async Task<bool> UserExists(int id)
    {
        return await _context.Users.AnyAsync(e => e.UserId == id);
    }

    // Helper methods removed as they are no longer needed
}
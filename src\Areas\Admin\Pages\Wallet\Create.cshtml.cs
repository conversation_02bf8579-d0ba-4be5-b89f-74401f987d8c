using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Market;
using RazeWinComTr.Areas.Admin.ViewModels.User;

namespace RazeWinComTr.Areas.Admin.Pages.Wallet;

public class CreateModel : PageModel
{
    private readonly IWalletService _walletService;
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        IWalletService walletService,
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _walletService = walletService;
        _context = context;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public WalletCreateViewModel ViewEntity { get; set; } = new();

    public List<UserViewModel> Users { get; set; } = new();
    public List<MarketViewModel> Coins { get; set; } = new();



    public async Task OnGetAsync()
    {
        Users = await _context.Users
            .Where(u => u.IsActive == 1)
            .Select(u => new UserViewModel
            {
                UserId = u.UserId,
                Email = u.Email,
                FullName = u.Name + " " + u.Surname,
                IsActive = u.IsActive,
                CrDate = u.CrDate
            })
            .ToListAsync();

        Coins = await _context.Markets
            .Where(m => m.IsActive == 1)
            .Select(m => new MarketViewModel
            {
                Id = m.Id,
                Name = m.Name,
                PairCode = m.PairCode
            })
            .ToListAsync();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            if (!ModelState.IsValid) return Page();

            // Check if wallet already exists for this user and coin
            var existingWallet = await _walletService.GetByUserIdAndCoinIdAsync(ViewEntity.UserId, ViewEntity.CoinId);
            if (existingWallet != null)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Warning"],
                    Text = _localizer["A wallet already exists for this user and coin"],
                    Icon = "warning"
                };
                await OnGetAsync();
                return Page();
            }

            var entity = new DbModel.Wallet
            {
                UserId = ViewEntity.UserId,
                CoinId = ViewEntity.CoinId,
                Balance = ViewEntity.Balance,
                CreatedDate = DateTime.UtcNow
            };
            await _walletService.CreateAsync(entity);

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/Admin/Wallet"
            };

            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = ex.Message,
                Icon = "error"
            };
            await OnGetAsync();
            return Page();
        }
    }
}

public class WalletCreateViewModel
{
    public int UserId { get; set; }
    public int CoinId { get; set; }
    public decimal Balance { get; set; } = 0;
}

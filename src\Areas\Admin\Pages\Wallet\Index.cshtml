@page
@using Microsoft.Extensions.Localization
@model IndexModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Wallets"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Wallets"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item active">@L["Wallets"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title float-right">@L["Count"]: @(Model.Wallets.Count)</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered table-striped datatable">
                    <thead>
                    <tr>
                        <th>@L["User"]</th>
                        <th>@L["Coin"]</th>
                        <th>@L["Balance"]</th>
                        <th>@L["Created Date"]</th>
                        <th>@L["Modified Date"]</th>
                        <th style="width: 150px">@L["Actions"]</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach (var item in Model.Wallets)
                    {
                        <tr>
                            <td>@item.UserEmail</td>
                            <td>@item.CoinName (@item.CoinCode)</td>
                            <td>@item.Balance.ToString("N8")</td>
                                <td>@item.CreatedDate.ToLocalTime().ToString("g")</td>
                            <td>@(item.ModifiedDate?.ToString("g") ?? "-")</td>
                            <td>
                                <!-- Edit and Delete buttons hidden -->
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</section>

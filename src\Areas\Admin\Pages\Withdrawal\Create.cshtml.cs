using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Bank;
using RazeWinComTr.Areas.Admin.ViewModels.User;

namespace RazeWinComTr.Areas.Admin.Pages.Withdrawal;

public class CreateModel : PageModel
{
    private readonly WithdrawalService _withdrawalService;
    private readonly IWalletService _walletService;
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        WithdrawalService withdrawalService,
        IWalletService walletService,
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _withdrawalService = withdrawalService;
        _walletService = walletService;
        _context = context;
        _localizer = localizer;
    }

    public SweetAlert2Message? AlertMessage { get; set; }

    [BindProperty] public WithdrawalCreateViewModel ViewEntity { get; set; } = new();

    public List<UserViewModel> Users { get; set; } = new();

    public string? ErrorMessage { get; set; }

    public async Task OnGetAsync()
    {
        Users = await _context.Users
            .Where(u => u.IsActive == 1)
            .Select(u => new UserViewModel
            {
                UserId = u.UserId,
                Email = u.Email,
                FullName = u.Name + " " + u.Surname,
                IsActive = u.IsActive,
                CrDate = u.CrDate
            })
            .ToListAsync();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        try
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue) return Unauthorized();

            if (!ModelState.IsValid) return Page();

            // Check if user has enough TRY balance
            var tryWallet = await _walletService.GetByUserIdAndCoinIdAsync(ViewEntity.UserId, 1); // Assuming TRY has ID 1
            if (tryWallet == null || tryWallet.Balance < ViewEntity.Amount)
            {
                ModelState.AddModelError("", _localizer["User does not have enough TRY balance"]);
                await OnGetAsync();
                return Page();
            }

            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == ViewEntity.UserId);
            var entity = new DbModel.Withdrawal
            {
                UserId = ViewEntity.UserId,
                FullName = user?.Name + " " + user?.Surname ?? _localizer["Unknown"].Value,
                Email = user?.Email ?? _localizer["<EMAIL>"].Value,
                Balance = tryWallet?.Balance ?? 0,
                WithdrawalAmount = ViewEntity.Amount,
                AccountHolder = ViewEntity.AccountHolder,
                Iban = ViewEntity.Iban,
                Status = (WithdrawalStatus)ViewEntity.Status,
                CreatedDate = DateTime.UtcNow
            };
            await _withdrawalService.CreateAsync(entity);

            // If withdrawal is approved, update user's TRY balance
            if ((WithdrawalStatus)ViewEntity.Status == WithdrawalStatus.Approved && tryWallet != null)
            {
                tryWallet.Balance -= ViewEntity.Amount;
                tryWallet.ModifiedDate = DateTime.UtcNow;
                await _walletService.UpdateAsync(tryWallet);
            }

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/Admin/Withdrawal"
            };

            return Page();
        }
        catch (Exception ex)
        {
            ErrorMessage = _localizer["An error occurred: {0}", ex.Message].Value;
            await OnGetAsync();
            return Page();
        }
    }
}

public class WithdrawalCreateViewModel
{
    public int UserId { get; set; }
    public decimal Amount { get; set; } = 0;
    public string AccountHolder { get; set; } = string.Empty;
    public string Iban { get; set; } = string.Empty;
    public int Status { get; set; } = 0; // 0: Pending, 1: Approved, 2: Rejected
}

@page
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@model EditModel
@inject IStringLocalizer<SharedResource> L

@{
    ViewData["Title"] = L["Edit Withdrawal"];
    Layout = "~/Areas/Admin/Pages/Shared/_LayoutAdminLte.cshtml";
}

@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (result.isConfirmed && redirectUrl !== '') {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@L["Edit Withdrawal"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/admin">@L["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/admin/withdrawal">@L["Withdrawals"]</a></li>
                    <li class="breadcrumb-item active">@L["Edit"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>
<div asp-validation-summary="ModelOnly" class="text-danger"></div>
<section class="content">
    <div class="container-fluid">
        <div class="card">
            <form method="post">
                <input type="hidden" asp-for="Entity.Id"/>
                <input type="hidden" asp-for="Entity.UserId"/>
                <input type="hidden" asp-for="Entity.PreviousStatus"/>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card card-primary">
                                <div class="card-header">
                                    <h3 class="card-title">@L["Withdrawal Information"]</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>@L["User"]</label>
                                        <input type="text" class="form-control" value="@Model.UserEmail" readonly />
                                    </div>
                                    <div class="form-group">
                                        <label>@L["User Balance"]</label>
                                        <input type="text" class="form-control" value="@Model.UserBalance.ToString("N2") @L["Currency_Symbol"]" readonly />
                                    </div>
                                    <div class="form-group">
                                        <label>@L["Created Date"]</label>
                                        <input type="text" class="form-control" value="@Model.CreatedDate.ToLocalTime().ToString("g")" readonly />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card card-success">
                                <div class="card-header">
                                    <h3 class="card-title">@L["Edit Withdrawal"]</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label asp-for="Entity.AccountHolder">@L["Account Holder"]</label>
                                        <input asp-for="Entity.AccountHolder" class="form-control" required/>
                                    </div>
                                    <div class="form-group">
                                        <label asp-for="Entity.Iban">@L["IBAN"]</label>
                                        <input asp-for="Entity.Iban" class="form-control" required/>
                                    </div>
                                    <div class="form-group">
                                        <label asp-for="Entity.WithdrawalAmount">@L["Amount (TRY)"]</label>
                                        <input asp-for="Entity.WithdrawalAmount" class="form-control" type="number" step="0.01" required/>
                                    </div>
                                    <div class="form-group">
                                        <label asp-for="Entity.Status">@L["Status"]</label>
                                        <select asp-for="Entity.Status" class="form-control" required>
                                            <option value="0">@L["Pending"]</option>
                                            <option value="1">@L["Approved"]</option>
                                            <option value="2">@L["Rejected"]</option>
                                        </select>
                                        <small class="form-text text-muted">
                                            @if (Model.Entity.Status == 0)
                                            {
                                                <span>@L["Current status"]: <span class="badge badge-warning">@L["Pending"]</span></span>
                                            }
                                            else if (Model.Entity.Status == 1)
                                            {
                                                <span>@L["Current status"]: <span class="badge badge-success">@L["Approved"]</span></span>
                                            }
                                            else if (Model.Entity.Status == 2)
                                            {
                                                <span>@L["Current status"]: <span class="badge badge-danger">@L["Rejected"]</span></span>
                                            }
                                        </small>
                                    </div>
                                    <div class="alert alert-warning">
                                        <i class="fas fa-info-circle"></i> @L["Note: When you approve a withdrawal, the amount will be deducted from the user's balance automatically."]
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i> @L["Save"]
                    </button>
                    <a href="/Admin/Withdrawal" class="btn btn-default">
                        <i class="fas fa-times mr-1"></i> @L["Cancel"]
                    </a>
                </div>
            </form>
        </div>
    </div>
</section>

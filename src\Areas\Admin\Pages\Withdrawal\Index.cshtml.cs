using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Withdrawal;

namespace RazeWinComTr.Areas.Admin.Pages.Withdrawal;

public class IndexModel : PageModel
{
    private readonly WithdrawalService _withdrawalService;

    public IndexModel(WithdrawalService withdrawalService)
    {
        _withdrawalService = withdrawalService;
    }

    public List<WithdrawalViewModel> Withdrawals { get; set; } = new();

    // We'll use direct TempData access instead of property to avoid serialization issues

    public async Task OnGetAsync()
    {
        Withdrawals = await _withdrawalService.GetListAsync();
    }
}

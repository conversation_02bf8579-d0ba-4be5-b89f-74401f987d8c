﻿using Microsoft.Extensions.Caching.Hybrid;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Bitexen;
using RazeWinComTr.Areas.Admin.ViewModels.Common;
using RazeWinComTr.Areas.Admin.ViewModels.Market;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace RazeWinComTr.Areas.Admin.Services;

public class BitexenService : ICryptoExchangeService
{
    private readonly ILogger<BitexenService> _logger;
    private readonly AppDbContext _context;
    private readonly HybridCache _cache;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IMarketService _marketService;

    public BitexenService(IMarketService marketService, IHttpClientFactory httpClientFactory, ILogger<BitexenService> logger, AppDbContext context, HybridCache cache)
    {
        _marketService = marketService;
        _httpClientFactory = httpClientFactory;
        _logger = logger;
        _cache = cache;
        _context = context;
    }

    public async Task<ITickerResponse?> GetTickerAsync(CancellationToken token = default)
    {
        var tags = new List<string> { "bitexen", "ticker" };
        var entryOptions = new HybridCacheEntryOptions
        {
            Expiration = TimeSpan.FromSeconds(10),
            LocalCacheExpiration = TimeSpan.FromMinutes(10)
        };

        return await _cache.GetOrCreateAsync(
            $"BitexenTickerResponse", // Unique key to the cache entry
            async cancel => await GetDataFromTheSourceAsync(cancel),
            entryOptions,
            tags,
            cancellationToken: token
        );
    }

    private static BitexenTickerApiResponse? LatestTickerApiResponse = null;
    private static readonly JsonSerializerOptions jsonSerializerOptions = new JsonSerializerOptions
    {
        NumberHandling = JsonNumberHandling.AllowReadingFromString | JsonNumberHandling.AllowNamedFloatingPointLiterals
    };
    private static readonly string url = "https://www.bitexen.com/api/v1/ticker/";

    private async Task<BitexenTickerApiResponse?> GetDataFromTheSourceAsync(CancellationToken cancel)
    {
        try
        {
            var client = _httpClientFactory.CreateClient();
            var response = await client.GetStringAsync(url, cancel);
            var data = JsonSerializer.Deserialize<BitexenTickerApiResponse?>(response, jsonSerializerOptions);
            if (data?.Status == "success")
            {
                LatestTickerApiResponse = data;
                await UpdateBuySellPricesAsync(data);
                return data;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching data from Bitexen API");
        }
        return LatestTickerApiResponse;
    }

    public async Task<IEnumerable<MarketViewModel>> GetMarketListWithApiCombinedAsync()
    {
        var markets = await _marketService.GetListAsync(isActive: 1);
        var tickerResponse = await GetTickerAsync();
        if (tickerResponse != null)
        {
            foreach (var market in markets.Where(p => p.IsApi == 1 && p.ApiServiceName == ApiServiceNames.Bitexen))
            {
                var tickerKey = market.PairCode;//BTCTRY
                if (string.IsNullOrEmpty(tickerKey)) continue;

                var ticker = tickerResponse.GetTickerViewModel(tickerKey);

                if (ticker != null)
                {
                    market.BuyPrice = ticker.BidPrice;
                    market.SellPrice = ticker.AskPrice;
                    market.Change24h = ticker.DailyChangePercent;

                    market.BuyPrice += market.GeneralIncrease;
                    market.SellPrice += market.GeneralIncrease;

                    // Format the prices without rounding
                    market.BuyPriceFormatted = Helpers.NumberFormatHelper.FormatDecimal(market.BuyPrice, market.DecimalPlaces);
                    market.SellPriceFormatted = Helpers.NumberFormatHelper.FormatDecimal(market.SellPrice, market.DecimalPlaces);
                }
            }
        }
        return markets;
    }

    public async Task UpdateBuySellPricesAsync(ITickerResponse data)
    {
        var entities = _context.Markets.Where(p => p.IsApi == 1 && p.ApiServiceName == ApiServiceNames.Bitexen).ToList();
        foreach (var item in entities)
        {
            var key = item.PairCode;//BTCTRY
            if (string.IsNullOrEmpty(key)) continue;

            var ticker = data.GetTickerViewModel(key);

            if (ticker != null)
            {
                item.BuyPrice = ticker.BidPrice;
                item.SellPrice = ticker.AskPrice;
                item.Change24h = ticker.DailyChangePercent;
                item.LastPriceUpdate = DateTime.UtcNow;
                await _marketService.UpdateAsync(item);
            }
        }
    }

    /// <summary>
    /// Gets a list of available trading pairs from the Bitexen API
    /// </summary>
    /// <param name="token">Cancellation token</param>
    /// <returns>A list of available trading pairs</returns>
    public async Task<List<PairViewModel>> GetAvailablePairsAsync(CancellationToken token = default)
    {
        // Get the ticker data which contains all available pairs
        var tickerResponse = await GetTickerAsync(token);
        if (tickerResponse == null)
        {
            return new List<PairViewModel>();
        }

        var result = new List<PairViewModel>();

        // Convert the ticker data to a list of pair view models
        foreach (var pair in ((BitexenTickerApiResponse)tickerResponse).Data.Ticker)
        {
            var pairCode = pair.Key;
            var tickerDetail = pair.Value;

            // Get base and quote currencies from the market info if available
            string baseCurrency = string.Empty;
            string quoteCurrency = string.Empty;

            if (tickerDetail.Market != null)
            {
                baseCurrency = tickerDetail.Market.BaseCurrencyCode;
                quoteCurrency = tickerDetail.Market.CounterCurrencyCode;
            }
            else if (!string.IsNullOrEmpty(pairCode) && pairCode.Length >= 3)
            {
                // Try to extract base and quote currencies from the pair code
                // Common formats: BTCTRY, BTC_TRY, etc.
                // This is a simple heuristic and may not work for all pairs
                if (pairCode.EndsWith("TRY"))
                {
                    baseCurrency = pairCode.Substring(0, pairCode.Length - 3);
                    quoteCurrency = "TRY";
                }
                else if (pairCode.EndsWith("USDT"))
                {
                    baseCurrency = pairCode.Substring(0, pairCode.Length - 4);
                    quoteCurrency = "USDT";
                }
                else if (pairCode.Contains("_"))
                {
                    var parts = pairCode.Split('_');
                    if (parts.Length == 2)
                    {
                        baseCurrency = parts[0];
                        quoteCurrency = parts[1];
                    }
                }
            }

            // Create a display name for the pair
            string displayName = !string.IsNullOrEmpty(baseCurrency) && !string.IsNullOrEmpty(quoteCurrency)
                ? $"{baseCurrency} - {quoteCurrency}"
                : pairCode;

            result.Add(new PairViewModel
            {
                PairCode = pairCode,
                DisplayName = displayName,
                BaseCurrency = baseCurrency,
                QuoteCurrency = quoteCurrency
            });
        }

        // Sort the pairs by display name
        return result.OrderBy(p => p.DisplayName).ToList();
    }
}

﻿﻿using RazeWinComTr.Areas.Admin.ViewModels.Common;
using RazeWinComTr.Areas.Admin.ViewModels.Market;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces
{
    /// <summary>
    /// Interface for cryptocurrency exchange API services
    /// </summary>
    public interface ICryptoExchangeService
    {
        /// <summary>
        /// Gets the latest ticker data from the exchange API
        /// </summary>
        /// <param name="token">Cancellation token</param>
        /// <returns>The ticker data from the exchange</returns>
        Task<ITickerResponse?> GetTickerAsync(CancellationToken token = default);

        /// <summary>
        /// Gets a list of markets with updated price information from the exchange API
        /// </summary>
        /// <returns>A list of markets with updated price information</returns>
        Task<IEnumerable<MarketViewModel>> GetMarketListWithApiCombinedAsync();

        /// <summary>
        /// Updates the buy/sell prices in the database based on the ticker data
        /// </summary>
        /// <param name="data">The ticker data from the exchange</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task UpdateBuySellPricesAsync(ITickerResponse data);

        /// <summary>
        /// Gets a list of available trading pairs from the exchange API
        /// </summary>
        /// <param name="token">Cancellation token</param>
        /// <returns>A list of available trading pairs</returns>
        Task<List<PairViewModel>> GetAvailablePairsAsync(CancellationToken token = default);
    }
}

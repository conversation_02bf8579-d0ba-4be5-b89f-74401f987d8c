using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Bitexen;
using RazeWinComTr.Areas.Admin.ViewModels.Market;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces;

public interface IMarketService
{
    Task<Market?> GetByIdAsync(int id);
    Task<List<MarketViewModel>> GetListAsync(int? isActive, CancellationToken cancellationToken = default);
    Task<Market?> GetByCoinAsync(string coin);
    Task<Market?> GetByPairCodeAsync(string pairCode);
    Task<Market> CreateAsync(Market entity);
    Task UpdateAsync(Market entity);
    Task DeleteAsync(int id);
    Task UpdateBuySellPrices(BitexenTickerApiResponse data);
}

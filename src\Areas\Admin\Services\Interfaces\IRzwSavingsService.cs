using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.RzwSavings;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces
{
    /// <summary>
    /// Interface for RZW Savings Service operations
    /// </summary>
    public interface IRzwSavingsService
    {
        /// <summary>
        /// Creates a new RZW savings account for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="planId">The savings plan ID</param>
        /// <param name="rzwAmount">The amount of RZW to save</param>
        /// <param name="autoRenew">Whether to auto-renew the account at maturity</param>
        /// <returns>Success status, message, and created account</returns>
        Task<(bool Success, string Message, RzwSavingsAccount? Account)> CreateSavingsAccountAsync(
            int userId, int planId, decimal rzwAmount, bool autoRenew = false);

        /// <summary>
        /// Gets all active savings accounts for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of active savings accounts</returns>
        Task<List<RzwSavingsAccount>> GetUserActiveSavingsAsync(int userId);

        /// <summary>
        /// Gets a specific savings account by ID and user ID
        /// </summary>
        /// <param name="accountId">The account ID</param>
        /// <param name="userId">The user ID</param>
        /// <returns>The savings account if found and belongs to user</returns>
        Task<RzwSavingsAccount?> GetSavingsAccountAsync(int accountId, int userId);

        /// <summary>
        /// Processes early withdrawal from a savings account
        /// </summary>
        /// <param name="accountId">The account ID</param>
        /// <param name="userId">The user ID</param>
        /// <returns>Success status, message, and withdrawn amount</returns>
        Task<(bool Success, string Message, decimal WithdrawnAmount)> EarlyWithdrawAsync(
            int accountId, int userId);

        /// <summary>
        /// Processes maturity of a savings account
        /// </summary>
        /// <param name="accountId">The account ID</param>
        /// <returns>Success status and message</returns>
        Task<(bool Success, string Message)> ProcessMaturityAsync(int accountId);

        /// <summary>
        /// Gets all savings accounts that have reached maturity
        /// </summary>
        /// <returns>List of matured accounts</returns>
        Task<List<RzwSavingsAccount>> GetMaturedAccountsAsync();

        /// <summary>
        /// Gets all savings accounts that need interest payment
        /// </summary>
        /// <returns>List of accounts needing interest payment</returns>
        Task<List<RzwSavingsAccount>> GetAccountsForInterestPaymentAsync();

        /// <summary>
        /// Gets all savings accounts for a user (including inactive ones)
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of all savings accounts</returns>
        Task<List<RzwSavingsAccount>> GetUserSavingsAsync(int userId);

        /// <summary>
        /// Gets savings account by ID without user validation (for admin operations)
        /// </summary>
        /// <param name="accountId">The account ID</param>
        /// <returns>The savings account if found</returns>
        Task<RzwSavingsAccount?> GetSavingsAccountByIdAsync(int accountId);

        /// <summary>
        /// Gets all user's savings accounts (active and inactive)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>List of all savings accounts</returns>
        Task<List<RzwSavingsAccount>> GetUserAllSavingsAsync(int userId);

        /// <summary>
        /// Processes maturity for a savings account with interest payment and auto-renew
        /// </summary>
        /// <param name="accountId">Account ID</param>
        /// <returns>Success status and message</returns>
        Task<(bool Success, string Message)> ProcessMaturityWithInterestAndAutoRenewAsync(int accountId);

        /// <summary>
        /// Gets accounts for admin panel with filtering and pagination
        /// </summary>
        Task<(List<RzwSavingsAccountViewModel> Accounts, int TotalCount)> GetAccountsForAdminAsync(
            string? searchEmail = null,
            string? status = null,
            int? planId = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int pageNumber = 1,
            int pageSize = 50);

        /// <summary>
        /// Gets account details for admin panel
        /// </summary>
        Task<RzwSavingsAccountViewModel?> GetAccountDetailsForAdminAsync(int accountId);

        /// <summary>
        /// Updates the AutoRenew property of a savings account
        /// </summary>
        /// <param name="accountId">Account ID</param>
        /// <param name="autoRenew">New AutoRenew value</param>
        /// <returns>Success status and message</returns>
        Task<(bool Success, string Message)> UpdateAutoRenewAsync(int accountId, bool autoRenew);
    }
}

using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Models;

namespace RazeWinComTr.Areas.Admin.Services.Interfaces
{
    /// <summary>
    /// Interface for RZW Balance Management Service operations
    /// </summary>
    public interface IRzwWalletBalanceManagementService
    {
        /// <summary>
        /// Gets RZW token information
        /// </summary>
        /// <returns>RZW token information</returns>
        Task<RzwTokenInfo> GetRzwTokenInfoAsync();

        /// <summary>
        /// Checks if user has sufficient available RZW balance
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="amount">The amount to check</param>
        /// <returns>True if sufficient balance, false otherwise</returns>
        Task<bool> HasSufficientAvailableRzwAsync(int userId, decimal amount);

        /// <summary>
        /// Gets comprehensive RZW balance information for a user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>RZW balance information</returns>
        Task<RzwBalanceInfo> GetRzwBalanceInfoAsync(int userId);

        /// <summary>
        /// Locks RZW tokens for savings purposes
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="amount">The amount to lock</param>
        /// <param name="description">Description for the audit trail</param>
        /// <param name="existingContext">Existing transaction context</param>
        /// <param name="rzwSavingsAccountId">RZW Savings Account ID for tracking</param>
        /// <returns>True if successful, false if insufficient balance</returns>
        Task<bool> LockRzwForSavingsAsync(int userId, decimal amount, string description,
            AppDbContext? existingContext = null, int? rzwSavingsAccountId = null);

        /// <summary>
        /// Unlocks RZW tokens from savings
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="amount">The amount to unlock</param>
        /// <param name="description">Description for the audit trail</param>
        /// <param name="tradeType">Type of trade for audit trail</param>
        /// <param name="existingContext">Existing transaction context</param>
        /// <param name="rzwSavingsAccountId">RZW Savings Account ID for tracking</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UnlockRzwFromSavingsAsync(int userId, decimal amount, string description,
            TradeType tradeType, AppDbContext? existingContext = null, int? rzwSavingsAccountId = null);

        /// <summary>
        /// Adds RZW tokens as interest payment to user's available balance
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="amount">The interest amount to add</param>
        /// <param name="description">Description for the audit trail</param>
        /// <param name="existingContext">Existing transaction context</param>
        /// <param name="rzwSavingsAccountId">RZW Savings Account ID for tracking</param>
        /// <returns>The updated wallet</returns>
        Task<Wallet> AddRzwInterestAsync(int rzwSavingsAccountId, int userId, decimal amount, string description,
            AppDbContext? existingContext = null);

        /// <summary>
        /// Deducts RZW tokens from user's available balance
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="amount">The amount to deduct</param>
        /// <returns>True if successful, false if insufficient balance</returns>
        Task<bool> DeductAvailableRzwAsync(int userId, decimal amount);

        /// <summary>
        /// Deducts RZW tokens from user's available balance using existing transaction
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="amount">The amount to deduct</param>
        /// <param name="existingContext">Existing transaction context</param>
        /// <returns>True if successful, false if insufficient balance</returns>
        Task<bool> DeductAvailableRzwAsync(int userId, decimal amount, AppDbContext? existingContext = null);
    }
}

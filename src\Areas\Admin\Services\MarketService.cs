using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Bitexen;
using RazeWinComTr.Areas.Admin.ViewModels.Market;

namespace RazeWinComTr.Areas.Admin.Services;

public class MarketService : IMarketService
{
    private readonly AppDbContext _context;

    public MarketService(AppDbContext context)
    {
        _context = context;
    }

    public async Task<Market?> GetByIdAsync(int id)
    {
        return await _context.Markets
            .FirstOrDefaultAsync(r => r.Id == id);
    }

    public async Task<List<MarketViewModel>> GetListAsync(int? isActive, CancellationToken cancellationToken = default)
    {
        var query = _context.Markets.AsQueryable();
        if (isActive.HasValue)
        {
            query = query.Where(r => r.IsActive == isActive);
        }
        var markets = await query.Select(r => new MarketViewModel
        {
            Id = r.Id,
            Coin = r.Coin,
            Name = r.Name,
            BuyPrice = r.BuyPrice,
            SellPrice = r.SellPrice,
            PairCode = r.PairCode,
            Change24h = r.Change24h,
            DecimalPlaces = r.DecimalPlaces,
            GeneralIncrease = r.GeneralIncrease,
            IconUrl = r.IconUrl,
            IsApi = r.IsApi,
            ApiServiceName = r.ApiServiceName,
            IsActive = r.IsActive,
            MaximumBuy = r.MaximumBuy,
            MaximumSell = r.MaximumSell,
            MinimumBuy = r.MinimumBuy,
            MinimumSell = r.MinimumSell,
            Order = r.Order,
            // CrDate = r.CrDate,
            // ModDate = r.ModDate,
            LastPriceUpdate = r.LastPriceUpdate
        })
            .OrderBy(p => p.Order)
            .ToListAsync();

        // Format the prices without rounding
        foreach (var market in markets)
        {
            market.BuyPriceFormatted = Helpers.NumberFormatHelper.FormatDecimal(market.BuyPrice, market.DecimalPlaces);
            market.SellPriceFormatted = Helpers.NumberFormatHelper.FormatDecimal(market.SellPrice, market.DecimalPlaces);
        }

        return markets;
    }


    public async Task DeleteAsync(int id)
    {
        var entity = await _context.Markets.FindAsync(id);
        if (entity != null)
        {
            _context.Markets.Remove(entity);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<Market> CreateAsync(Market entity)
    {
        _context.Markets.Add(entity);
        await _context.SaveChangesAsync();
        return entity;
    }

    public async Task UpdateAsync(Market entity)
    {
        _context.Entry(entity).State = EntityState.Modified;
        await _context.SaveChangesAsync();
    }

    public async Task<Market?> GetByCoinAsync(string coin)
    {
        return await _context.Markets
            .FirstOrDefaultAsync(r => r.Coin == coin);
    }

    public async Task<Market?> GetByPairCodeAsync(string pairCode)
    {
        return await _context.Markets
            .FirstOrDefaultAsync(r => r.PairCode == pairCode);
    }

    public async Task UpdateBuySellPrices(BitexenTickerApiResponse data)
    {
        var entities = await _context
            .Markets
            .Where(p => p.IsApi == 1 && p.ApiServiceName == ApiServiceNames.Bitexen)
            .ToListAsync();
        foreach (var item in entities)
        {
            var key = item.PairCode;//BTCTRY
            if (!string.IsNullOrEmpty(key) && data.Data.Ticker.ContainsKey(key))
            {
                var ticker = data.Data.Ticker[key];
                if (ticker != null)
                {
                    item.BuyPrice = ticker.Bid;
                    item.SellPrice = ticker.Ask;
                    item.Change24h = ticker.Change24h;
                    await UpdateAsync(item);
                }
            }
        }
    }
}
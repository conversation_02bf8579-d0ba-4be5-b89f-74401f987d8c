using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Package;

namespace RazeWinComTr.Areas.Admin.Services;

public class PackageRewardPercentageService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PackageRewardPercentageService(
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _context = context;
        _localizer = localizer;
    }

    public async Task<PackageRewardPercentage?> GetByIdAsync(int id)
    {
        return await _context.PackageRewardPercentages
            .Include(p => p.Package)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<PackageRewardPercentage?> GetByPackageAndLevelAsync(int packageId, int level)
    {
        return await _context.PackageRewardPercentages
            .FirstOrDefaultAsync(p => p.PackageId == packageId && p.Level == level);
    }

    public async Task<List<PackageRewardPercentage>> GetByPackageIdAsync(int packageId)
    {
        return await _context.PackageRewardPercentages
            .Where(p => p.PackageId == packageId)
            .OrderBy(p => p.Level)
            .ToListAsync();
    }

    public async Task<List<PackageRewardPercentageViewModel>> GetListAsync()
    {
        return await _context.PackageRewardPercentages
            .Include(p => p.Package)
            .Select(p => new PackageRewardPercentageViewModel
            {
                Id = p.Id,
                PackageId = p.PackageId,
                PackageName = p.Package.Name,
                Level = p.Level,
                TlPercentage = p.TlPercentage,
                RzwPercentage = p.RzwPercentage,
                CreatedDate = p.CreatedDate,
                ModifiedDate = p.ModifiedDate
            })
            .OrderBy(p => p.PackageName)
            .ThenBy(p => p.Level)
            .ToListAsync();
    }

    public async Task<decimal?> GetPercentageAsync(int packageId, int level)
    {
        // Try to get package-specific percentage
        var packagePercentage = await _context.PackageRewardPercentages
            .FirstOrDefaultAsync(p => p.PackageId == packageId && p.Level == level);

        return packagePercentage?.RzwPercentage;
    }

    /// <summary>
    /// Validates that the percentage for a level is not greater than the percentage for any lower level
    /// </summary>
    /// <param name="packageId">The package ID</param>
    /// <param name="level">The level to validate</param>
    /// <param name="percentage">The percentage value to validate</param>
    /// <returns>Tuple with validation result and error message if validation fails</returns>
    public async Task<(bool IsValid, string? ErrorMessage)> ValidatePercentageHierarchyAsync(int packageId, int level, decimal percentage)
    {
        // Get all lower level percentages for this package
        var lowerLevelPercentages = await _context.PackageRewardPercentages
            .Where(p => p.PackageId == packageId && p.Level < level)
            .OrderBy(p => p.Level)
            .ToListAsync();

        // If there are no lower levels, validation passes
        if (lowerLevelPercentages.Count == 0)
        {
            return (true, null);
        }

        // Check if the percentage is greater than any lower level percentage
        foreach (var lowerLevel in lowerLevelPercentages)
        {
            if (percentage > lowerLevel.RzwPercentage)
            {
                return (false, _localizer["Level {0} percentage ({1}%) cannot be greater than Level {2} percentage ({3}%)",
                    level, percentage, lowerLevel.Level, lowerLevel.RzwPercentage].Value);
            }
        }

        return (true, null);
    }

    public async Task<PackageRewardPercentage> CreateAsync(PackageRewardPercentage percentage)
    {
        // Check if there's already a percentage for this package and level
        var existing = await _context.PackageRewardPercentages
            .FirstOrDefaultAsync(p => p.PackageId == percentage.PackageId && p.Level == percentage.Level);

        if (existing != null)
        {
            // Update existing instead of creating new
            existing.TlPercentage = percentage.TlPercentage;
            existing.RzwPercentage = percentage.RzwPercentage;
            existing.ModifiedDate = DateTime.UtcNow;
            _context.PackageRewardPercentages.Update(existing);
            await _context.SaveChangesAsync();
            return existing;
        }

        // Validate percentage hierarchy
        var (isValid, errorMessage) = await ValidatePercentageHierarchyAsync(
            percentage.PackageId, percentage.Level, percentage.RzwPercentage);

        if (!isValid)
        {
            throw new InvalidOperationException(errorMessage);
        }

        _context.PackageRewardPercentages.Add(percentage);
        await _context.SaveChangesAsync();
        return percentage;
    }

    public async Task<PackageRewardPercentage> UpdateAsync(PackageRewardPercentage percentage)
    {
        // Validate percentage hierarchy
        var (isValid, errorMessage) = await ValidatePercentageHierarchyAsync(
             percentage.PackageId, percentage.Level, percentage.RzwPercentage);

        if (!isValid)
        {
            throw new InvalidOperationException(errorMessage);
        }

        percentage.ModifiedDate = DateTime.UtcNow;
        _context.PackageRewardPercentages.Update(percentage);
        await _context.SaveChangesAsync();
        return percentage;
    }

    public async Task DeleteAsync(int id)
    {
        var percentage = await _context.PackageRewardPercentages.FindAsync(id);
        if (percentage != null)
        {
            _context.PackageRewardPercentages.Remove(percentage);
            await _context.SaveChangesAsync();
        }
    }
}

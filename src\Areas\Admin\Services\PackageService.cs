using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Package;

namespace RazeWinComTr.Areas.Admin.Services;

public class PackageService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PackageService(
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer)
    {
        _context = context;
        _localizer = localizer;
    }

    public async Task<Package?> GetByIdAsync(int id)
    {
        return await _context.Packages
            .Include(p => p.RewardPercentages)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<List<Package>> GetActivePackagesAsync()
    {
        return await _context.Packages
            .Where(p => p.IsActive)
            .OrderBy(p => p.Order)
            .ToListAsync();
    }

    public async Task<List<PackageViewModel>> GetListAsync()
    {
        return await _context.Packages
            .Select(p => new PackageViewModel
            {
                Id = p.Id,
                Name = p.Name,
                Price = p.Price,
                Description = p.Description,
                Benefits = p.Benefits,
                InviteLimit = p.InviteLimit,
                EarningsCap = p.EarningsCap,
                IsActive = p.IsActive,
                Order = p.Order,
                CreatedDate = p.CreatedDate,
                ModifiedDate = p.ModifiedDate
            })
            .OrderBy(p => p.Order)
            .ToListAsync();
    }

    public async Task<List<PackageWithRewardsViewModel>> GetPackagesWithRewardsAsync(bool activeOnly = true)
    {
        var query = _context.Packages
            .Include(p => p.RewardPercentages)
            .AsQueryable();

        if (activeOnly)
        {
            query = query.Where(p => p.IsActive);
        }

        var packages = await query
            .OrderBy(p => p.Order)
            .Select(p => new PackageWithRewardsViewModel
            {
                Package = new PackageViewModel
                {
                    Id = p.Id,
                    Name = p.Name,
                    Price = p.Price,
                    Description = p.Description,
                    Benefits = p.Benefits,
                    InviteLimit = p.InviteLimit,
                    EarningsCap = p.EarningsCap,
                    IsActive = p.IsActive,
                    Order = p.Order,
                    CreatedDate = p.CreatedDate,
                    ModifiedDate = p.ModifiedDate
                },
                RewardPercentages = p.RewardPercentages
                    .OrderBy(rp => rp.Level)
                    .ToList()
            })
            .ToListAsync();

        return packages;
    }

    public async Task<Package> CreateAsync(Package package)
    {
        _context.Packages.Add(package);
        await _context.SaveChangesAsync();
        return package;
    }

    public async Task<Package> UpdateAsync(Package package)
    {
        _context.Packages.Update(package);
        await _context.SaveChangesAsync();
        return package;
    }

    public async Task DeleteAsync(int id)
    {
        var package = await _context.Packages.FindAsync(id);
        if (package != null)
        {
            // Check if there are any user packages using this package
            var hasUserPackages = await _context.UserPackages.AnyAsync(up => up.PackageId == id);
            if (hasUserPackages)
            {
                throw new InvalidOperationException(_localizer["Cannot delete package because it is assigned to users. Please remove user assignments first."].Value);
            }

            // Check if there are any reward percentages for this package
            var hasRewardPercentages = await _context.PackageRewardPercentages.AnyAsync(prp => prp.PackageId == id);
            if (hasRewardPercentages)
            {
                throw new InvalidOperationException(_localizer["Cannot delete package because it has reward percentages. Please remove reward percentages first."].Value);
            }

            _context.Packages.Remove(package);
            await _context.SaveChangesAsync();
        }
    }
}

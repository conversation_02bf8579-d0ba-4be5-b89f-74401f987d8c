using Microsoft.EntityFrameworkCore;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Referral;
using System.Security.Cryptography;
using System.Text;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Service for managing user referral codes
/// </summary>
public class ReferralService
{
    private readonly AppDbContext _dbContext;
    private readonly ILogger<ReferralService> _logger;
    private static readonly char[] _allowedChars = "ABCDEFGHJKLMNPQRSTUVWXYZ23456789".ToCharArray();

    public ReferralService(
        AppDbContext dbContext,
        ILogger<ReferralService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    /// <summary>
    /// Counts the number of users referred by a specific user
    /// </summary>
    /// <param name="userId">The ID of the referring user</param>
    /// <returns>The number of users referred by the specified user</returns>
    public async Task<int> CountReferredUsersAsync(int userId)
    {
        return await _dbContext.Users
            .CountAsync(u => u.ReferrerId == userId);
    }

    /// <summary>
    /// Checks if a user has reached their invite limit based on their active package
    /// </summary>
    /// <param name="userId">The ID of the user to check</param>
    /// <returns>True if the user has reached their invite limit, false otherwise</returns>
    public async Task<bool> HasReachedInviteLimitAsync(int userId)
    {
        // Get the user's active package
        var activePackage = await _dbContext.UserPackages
            .Include(up => up.Package)
            .Where(up => up.UserId == userId && up.Status == UserPackageStatus.Active)
            .OrderByDescending(up => up.PackageId) // Get the highest package if multiple
            .FirstOrDefaultAsync();

        // If the user has no active package, they can still invite (unlimited)
        if (activePackage == null)
        {
            _logger.LogInformation("User {UserId} has no active package, but can invite (unlimited)", userId);
            return false;
        }

        // If the package has no invite limit (null), the user can invite unlimited users
        if (!activePackage.Package.InviteLimit.HasValue)
        {
            _logger.LogInformation("User {UserId} has package {PackageId} with unlimited invites", userId, activePackage.PackageId);
            return false;
        }

        // Count the user's current referrals
        int currentReferrals = await CountReferredUsersAsync(userId);

        // Check if the user has reached their invite limit
        bool hasReachedLimit = currentReferrals >= activePackage.Package.InviteLimit.Value;

        _logger.LogInformation("User {UserId} has {CurrentReferrals}/{InviteLimit} referrals, has reached limit: {HasReachedLimit}",
            userId, currentReferrals, activePackage.Package.InviteLimit.Value, hasReachedLimit);

        return hasReachedLimit;
    }

    /// <summary>
    /// Gets the remaining invites for a user based on their active package
    /// </summary>
    /// <param name="userId">The ID of the user</param>
    /// <returns>The number of remaining invites, or null if unlimited</returns>
    public async Task<int?> GetRemainingInvitesAsync(int userId)
    {
        // Get the user's active package
        var activePackage = await _dbContext.UserPackages
            .Include(up => up.Package)
            .Where(up => up.UserId == userId && up.Status == UserPackageStatus.Active)
            .OrderByDescending(up => up.PackageId) // Get the highest package if multiple
            .FirstOrDefaultAsync();

        // If the user has no active package, they can invite unlimited users
        if (activePackage == null)
        {
            return null; // Unlimited
        }

        // If the package has no invite limit (null), the user can invite unlimited users
        if (!activePackage.Package.InviteLimit.HasValue)
        {
            return null; // Unlimited
        }

        // Count the user's current referrals
        int currentReferrals = await CountReferredUsersAsync(userId);

        // Calculate remaining invites
        int remainingInvites = Math.Max(0, activePackage.Package.InviteLimit.Value - currentReferrals);

        return remainingInvites;
    }

    /// <summary>
    /// Gets the invite limit for a user based on their active package
    /// </summary>
    /// <param name="userId">The ID of the user</param>
    /// <returns>The invite limit, or null if unlimited</returns>
    public async Task<int?> GetInviteLimitAsync(int userId)
    {
        // Get the user's active package
        var activePackage = await _dbContext.UserPackages
            .Include(up => up.Package)
            .Where(up => up.UserId == userId && up.Status == UserPackageStatus.Active)
            .OrderByDescending(up => up.PackageId) // Get the highest package if multiple
            .FirstOrDefaultAsync();

        // If the user has no active package, they have unlimited invites
        if (activePackage == null)
        {
            return null; // Unlimited
        }

        return activePackage.Package.InviteLimit;
    }

    /// <summary>
    /// Generates a unique referral code for a user
    /// </summary>
    /// <returns>A unique referral code</returns>
    public async Task<string> GenerateUniqueReferralCodeAsync()
    {
        string referralCode;
        bool isUnique = false;

        // Keep generating codes until we find a unique one
        do
        {
            referralCode = GenerateReferralCode();
            isUnique = !await _dbContext.Users.AnyAsync(u => u.ReferralCode == referralCode);
        } while (!isUnique);

        return referralCode;
    }

    /// <summary>
    /// Checks if a referral code is valid (exists in the system)
    /// </summary>
    /// <param name="referralCode">The referral code to check</param>
    /// <returns>True if the code is valid, false otherwise</returns>
    public async Task<bool> IsReferralCodeValidAsync(string referralCode)
    {
        if (string.IsNullOrWhiteSpace(referralCode))
        {
            return false;
        }

        return await _dbContext.Users.AnyAsync(u => u.ReferralCode == referralCode && u.IsActive == 1);
    }

    /// <summary>
    /// Gets a user by their referral code
    /// </summary>
    /// <param name="referralCode">The referral code to look up</param>
    /// <returns>The user with the given referral code, or null if not found</returns>
    public async Task<User?> GetUserByReferralCodeAsync(string referralCode)
    {
        if (string.IsNullOrWhiteSpace(referralCode))
        {
            return null;
        }

        return await _dbContext.Users
            .FirstOrDefaultAsync(u => u.ReferralCode == referralCode && u.IsActive == 1);
    }

    /// <summary>
    /// Gets a list of users referred by a specific user
    /// </summary>
    /// <param name="userId">The ID of the referring user</param>
    /// <returns>A list of users referred by the specified user</returns>
    public async Task<List<User>> GetReferredUsersAsync(int userId)
    {
        return await _dbContext.Users
            .Where(u => u.ReferrerId == userId)
            .OrderByDescending(u => u.CrDate)
            .ToListAsync();
    }

    /// <summary>
    /// Gets a hierarchical list of users in the referral tree
    /// </summary>
    /// <param name="userId">The ID of the referring user</param>
    /// <returns>A hierarchical list of users in the referral tree</returns>
    public async Task<List<ReferralHierarchyItem>> GetReferralHierarchyAsync(int userId)
    {
        // Get all users in the system to avoid multiple database queries
        var allUsers = await _dbContext.Users.Select(p => new ReferralHierarchyItemUser
        {
            Email = p.Email,
            Name = p.Name,
            Surname = p.Surname,
            UserId = p.UserId,
            ReferrerId = p.ReferrerId
        }).ToListAsync();

        // Get all active user packages to avoid multiple database queries
        var allUserPackages = await _dbContext.UserPackages
            .Include(up => up.Package)
            .Where(up => up.Status == UserPackageStatus.Active)
            .ToListAsync();

        // Get the referrer's first package purchase date for statistics calculation
        var referrerFirstPackageDate = await _dbContext.UserPackages
            .Where(up => up.UserId == userId)
            .OrderBy(up => up.PurchaseDate)
            .Select(up => up.PurchaseDate)
            .FirstOrDefaultAsync();

        // Initialize deposits and rewards collections
        List<Deposit> allDeposits = [];
        List<ReferralReward> allReferralRewards = [];

        // Only fetch deposits and rewards if the user has purchased a package
        // This maintains the performance optimization for statistics calculation
        if (referrerFirstPackageDate != default)
        {
            // Get all payments after referrer's first package purchase to avoid multiple database queries
            allDeposits = await _dbContext.Deposits
                .Where(p => p.Status == DepositStatus.Approved && p.CreatedDate >= referrerFirstPackageDate)
                .ToListAsync();

            // Get all referral rewards after referrer's first package purchase to avoid multiple database queries
            allReferralRewards = await _dbContext.ReferralRewards
                .Where(r => r.Status == ReferralRewardStatus.Paid && r.CreatedDate >= referrerFirstPackageDate)
                .ToListAsync();
        }

        // Build the hierarchy
        var result = new List<ReferralHierarchyItem>();

        // Get direct referrals
        var directReferrals = allUsers.Where(u => u.ReferrerId == userId).ToList();

        foreach (var directReferral in directReferrals)
        {
            // Get the active package for this user (if any)
            var activePackage = allUserPackages
                .Where(up => up.UserId == directReferral.UserId)
                .OrderByDescending(up => up.PackageId) // Get the highest package if multiple
                .Select(up => up.Package)
                .FirstOrDefault();

            // Calculate total deposits and RZW earnings
            // If the user has never purchased a package, these will be 0
            var (totalDeposits, totalRzwEarnings) = referrerFirstPackageDate != default
                ? CalculateUserStatsAsync(
                    directReferral.UserId,
                    userId,
                    allDeposits,
                    allReferralRewards)
                : (0, 0);

            var item = new ReferralHierarchyItem
            {
                User = directReferral,
                Level = 1,
                ActivePackage = activePackage,
                TotalDeposits = totalDeposits,
                TotalRzwEarnings = totalRzwEarnings,
                Children = await GetChildReferralsAsync(
                    directReferral.UserId,
                    allUsers,
                    allUserPackages,
                    allDeposits,
                    allReferralRewards,
                    2,
                    referrerFirstPackageDate != default)
            };

            result.Add(item);
        }

        return result;
    }

    /// <summary>
    /// Helper method to recursively get child referrals
    /// </summary>
    private static async Task<List<ReferralHierarchyItem>> GetChildReferralsAsync(
        int userId,
        List<ReferralHierarchyItemUser> allUsers,
        List<UserPackage> allUserPackages,
        List<Deposit> allDeposits,
        List<ReferralReward> allReferralRewards,
        int level,
        bool calculateStats = true)
    {
        var result = new List<ReferralHierarchyItem>();

        // Get direct referrals for this user
        var directReferrals = allUsers.Where(u => u.ReferrerId == userId).ToList();

        foreach (var directReferral in directReferrals)
        {
            // Get the active package for this user (if any)
            var activePackage = allUserPackages
                .Where(up => up.UserId == directReferral.UserId)
                .OrderByDescending(up => up.PackageId) // Get the highest package if multiple
                .Select(up => up.Package)
                .FirstOrDefault();

            // Calculate total deposits and RZW earnings only if calculateStats is true
            var (totalDeposits, totalRzwEarnings) = calculateStats
                ? CalculateUserStatsAsync(
                    directReferral.UserId,
                    userId,
                    allDeposits,
                    allReferralRewards)
                : (0, 0);

            var item = new ReferralHierarchyItem
            {
                User = directReferral,
                Level = level,
                ActivePackage = activePackage,
                TotalDeposits = totalDeposits,
                TotalRzwEarnings = totalRzwEarnings,
                Children = await GetChildReferralsAsync(
                    directReferral.UserId,
                    allUsers,
                    allUserPackages,
                    allDeposits,
                    allReferralRewards,
                    level + 1,
                    calculateStats)
            };

            result.Add(item);
        }

        return result;
    }

    /// <summary>
    /// Calculates total deposits and RZW earnings for a user
    /// </summary>
    private static (decimal TotalDeposits, decimal TotalRzwEarnings) CalculateUserStatsAsync(
        int userId,
        int referrerId,
        List<Deposit> allDeposits,
        List<ReferralReward> allReferralRewards)
    {
        // Calculate total deposits
        // Note: allDeposits is already filtered by date in the GetReferralHierarchyAsync method
        var totalDeposits = allDeposits
            .Where(p => p.UserId == userId && p.Status == DepositStatus.Approved)
            .Sum(p => p.Amount);

        // Calculate total RZW earnings from this user's deposits
        var totalRzwEarnings = allReferralRewards
            .Where(r => r.UserId == referrerId && r.ReferredUserId == userId)
            .Sum(r => r.RzwAmount);

        return (totalDeposits, totalRzwEarnings);
    }

    /// <summary>
    /// Generates a random referral code
    /// </summary>
    /// <returns>A random referral code</returns>
    private static string GenerateReferralCode()
    {
        // Generate a 8-character referral code
        const int codeLength = 8;
        var result = new StringBuilder(codeLength);

        // Use cryptographically secure random number generator
        using (var rng = RandomNumberGenerator.Create())
        {
            byte[] randomBytes = new byte[codeLength];
            rng.GetBytes(randomBytes);

            for (int i = 0; i < codeLength; i++)
            {
                // Map the random byte to an index in the allowed characters array
                int index = randomBytes[i] % _allowedChars.Length;
                result.Append(_allowedChars[index]);
            }
        }

        return result.ToString();
    }
}

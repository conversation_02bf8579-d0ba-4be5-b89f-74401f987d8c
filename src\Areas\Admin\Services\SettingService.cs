using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.Setting;

namespace RazeWinComTr.Areas.Admin.Services;

public class SettingService
{
    private readonly AppDbContext _context;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public SettingService(IStringLocalizer<SharedResource> localizer, AppDbContext context)
    {
        _localizer = localizer;
        _context = context;
    }

    public async Task<Setting?> GetByIdAsync(int id)
    {
        return await _context.Settings.FirstOrDefaultAsync(s => s.Id == id);
    }

    public async Task<Setting?> GetByKeyAsync(string key)
    {
        return await _context.Settings.FirstOrDefaultAsync(s => s.Key == key);
    }

    public async Task<List<SettingViewModel>> GetListAsync()
    {
        return await _context.Settings
            .Select(s => new SettingViewModel
            {
                Id = s.Id,
                Key = s.Key,
                Value = s.Value,
                Description = s.Description,
                Group = s.Group,
                Order = s.Order,
                CrDate = s.CrDate
            })
            .OrderBy(s => s.Key)
            .ToListAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var entity = await _context.Settings.FindAsync(id);
        if (entity != null)
        {
            _context.Settings.Remove(entity);
            await _context.SaveChangesAsync();
        }
    }

    public async Task<Setting> CreateAsync(Setting setting)
    {
        _context.Settings.Add(setting);
        await _context.SaveChangesAsync();
        return setting;
    }

    public async Task UpdateAsync(Setting setting)
    {
        _context.Entry(setting).State = EntityState.Modified;
        await _context.SaveChangesAsync();
    }

    public async Task<string?> GetValueAsync(string key)
    {
        var setting = await _context.Settings.FirstOrDefaultAsync(s => s.Key == key);
        return setting?.Value;
    }

    public async Task<string> GetSettingAsync(string key, string defaultValue = "")
    {
        var value = await GetValueAsync(key);
        return value ?? defaultValue;
    }

    public async Task<bool> GetBoolSettingAsync(string key, bool defaultValue = false)
    {
        var value = await GetValueAsync(key);
        if (string.IsNullOrEmpty(value))
            return defaultValue;

        if (value == "1" || value.ToLower() == "true")
            return true;

        if (value == "0" || value.ToLower() == "false")
            return false;

        return bool.TryParse(value, out bool result) ? result : defaultValue;
    }

    public async Task<int> GetIntSettingAsync(string key, int defaultValue = 0)
    {
        var value = await GetValueAsync(key);
        return !string.IsNullOrEmpty(value) && int.TryParse(value, out int result) ? result : defaultValue;
    }

    public async Task<decimal> GetDecimalSettingAsync(string key, decimal defaultValue = 0)
    {
        var value = await GetValueAsync(key);
        return !string.IsNullOrEmpty(value) && decimal.TryParse(value, out decimal result) ? result : defaultValue;
    }

    public async Task<bool> SetValueAsync(string key, string value)
    {
        var setting = await _context.Settings.FirstOrDefaultAsync(s => s.Key == key);
        if (setting == null) return false;

        setting.Value = value;
        await _context.SaveChangesAsync();
        return true;
    }
}

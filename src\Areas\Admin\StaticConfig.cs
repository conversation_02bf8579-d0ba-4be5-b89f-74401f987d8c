﻿using System.Globalization;

namespace RazeWinComTr.Areas.Admin;

public static class StaticConfig
{
    public static readonly IList<CultureInfo> supportedCultures =
    [
        new("tr-TR"),
        new("en-US")
    ];

    public static string AdminPolicyName = "AdminPolicy";
    public static string JwtPolicyName = "JwtPolicy";
    public static string FormsPolicyName = "FormsPolicy";
    public static int DemoAccountLicenseExpirationDays = 10;
    public static int DemoAccountMaxDeviceCount = 5;

    public static TimeSpan BackgroundServiceDailyUsageRenewingTimeToAddToTodayForNextInterval =
        TimeSpan.FromDays(1).Add(TimeSpan.FromHours(1));
}
namespace RazeWinComTr.Areas.Admin.ViewModels;

/// <summary>
/// View model for background service status
/// </summary>
public class BackgroundServiceStatusViewModel
{
    /// <summary>
    /// The name of the service
    /// </summary>
    public string ServiceName { get; set; } = string.Empty;

    /// <summary>
    /// The status of the service (Running, Failed, Not Started)
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// The time of the last successful execution
    /// </summary>
    public DateTime? LastSuccessfulRunTime { get; set; }

    /// <summary>
    /// The time of the last failure
    /// </summary>
    public DateTime? LastFailureTime { get; set; }

    /// <summary>
    /// The number of successful executions
    /// </summary>
    public long SuccessCount { get; set; }

    /// <summary>
    /// The number of failed executions
    /// </summary>
    public long FailureCount { get; set; }

    /// <summary>
    /// The last error message
    /// </summary>
    public string? LastErrorMessage { get; set; }

    /// <summary>
    /// The time elapsed since the last successful run
    /// </summary>
    public TimeSpan? TimeSinceLastSuccess { get; set; }

    /// <summary>
    /// Gets the CSS class for the status badge
    /// </summary>
    public string StatusBadgeClass
    {
        get
        {
            return Status switch
            {
                "Running" => "badge-success",
                "Failed" => "badge-danger",
                "Not Started" => "badge-warning",
                _ => "badge-secondary"
            };
        }
    }
}

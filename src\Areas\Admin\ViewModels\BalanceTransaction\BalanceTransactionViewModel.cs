using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.ViewModels.ReferralReward;

namespace RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction
{
    public class BalanceTransactionViewModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string UserFullName { get; set; } = string.Empty;
        public string TransactionType { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public decimal PreviousBalance { get; set; }
        public decimal NewBalance { get; set; }
        public int? ReferenceId { get; set; }
        public string? ReferenceType { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public bool IsActive { get; set; }

        // Referral reward details
        public ReferralRewardDetailsViewModel? ReferralReward { get; set; }

        // Helper property to get formatted referral reward description
        public string? GetReferralRewardDescription() => ReferralReward?.GetReferralRewardDescription();
    }
}

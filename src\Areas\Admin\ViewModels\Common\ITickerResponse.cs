﻿﻿using System.Collections.Generic;

namespace RazeWinComTr.Areas.Admin.ViewModels.Common
{
    /// <summary>
    /// Common interface for all cryptocurrency exchange API ticker responses
    /// </summary>
    public interface ITickerResponse
    {
        /// <summary>
        /// Indicates whether the API request was successful
        /// </summary>
        bool IsSuccess { get; }

        /// <summary>
        /// Gets the error message if the request was not successful
        /// </summary>
        string? GetErrorMessage();

        /// <summary>
        /// Gets the timestamp of when the data was retrieved
        /// </summary>
        DateTime GetTimestamp();

        /// <summary>
        /// Gets the buy price (bid) for a specific market pair
        /// </summary>
        /// <param name="marketPair">The market pair code (e.g., "BTCTRY")</param>
        /// <returns>The buy price or null if not found</returns>
        decimal? GetBuyPrice(string marketPair);

        /// <summary>
        /// Gets the sell price (ask) for a specific market pair
        /// </summary>
        /// <param name="marketPair">The market pair code (e.g., "BTCTRY")</param>
        /// <returns>The sell price or null if not found</returns>
        decimal? GetSellPrice(string marketPair);

        /// <summary>
        /// Gets the 24-hour price change percentage for a specific market pair
        /// </summary>
        /// <param name="marketPair">The market pair code (e.g., "BTCTRY")</param>
        /// <returns>The 24-hour price change percentage or null if not found</returns>
        decimal? GetPriceChangePercentage(string marketPair);

        /// <summary>
        /// Gets a standardized ticker view model for a specific market pair
        /// </summary>
        /// <param name="marketPair">The market pair code (e.g., "BTCTRY")</param>
        /// <returns>A standardized ticker view model or null if not found</returns>
        TickerViewModel? GetTickerViewModel(string marketPair);

        /// <summary>
        /// Gets all available tickers as standardized view models
        /// </summary>
        /// <returns>A dictionary of market pairs to standardized ticker view models</returns>
        Dictionary<string, TickerViewModel> GetAllTickers();
    }
}

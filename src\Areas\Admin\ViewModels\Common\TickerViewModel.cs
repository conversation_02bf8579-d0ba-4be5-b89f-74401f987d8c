﻿﻿using System;

namespace RazeWinComTr.Areas.Admin.ViewModels.Common
{
    /// <summary>
    /// Represents the common ticker information extracted from different API sources.
    /// </summary>
    public class TickerViewModel
    {
        /// <summary>
        /// The trading pair identifier (e.g., "BTCTRY", "ETHUSDT").
        /// Corresponds to 'pair' in JSON1 or 'market.market_code' in JSON2.
        /// </summary>
        public string PairIdentifier { get; set; } = string.Empty;

        /// <summary>
        /// The timestamp when the data was generated or received, typically in Unix milliseconds.
        /// Corresponds to 'timestamp' in JSON1 (ms) or needs conversion from JSON2's 'timestamp' (s, string).
        /// </summary>
        public long TimestampMs { get; set; }

        // Alternative: Store as DateTimeOffset if immediate conversion is desired
        // public DateTimeOffset Timestamp { get; set; }

        /// <summary>
        /// The last traded price for the pair.
        /// Corresponds to 'last' in JSON1 or 'last_price' in JSON2.
        /// </summary>
        public decimal LastPrice { get; set; }

        /// <summary>
        /// The highest price for the pair in the last 24 hours.
        /// Corresponds to 'high' in JSON1 or 'high_24h' in JSON2.
        /// </summary>
        public decimal High24h { get; set; }

        /// <summary>
        /// The lowest price for the pair in the last 24 hours.
        /// Corresponds to 'low' in JSON1 or 'low_24h' in JSON2.
        /// </summary>
        public decimal Low24h { get; set; }

        /// <summary>
        /// The current highest buy order price (bid).
        /// Corresponds to 'bid' in both JSON1 and JSON2.
        /// </summary>
        public decimal BidPrice { get; set; }

        /// <summary>
        /// The current lowest sell order price (ask).
        /// Corresponds to 'ask' in both JSON1 and JSON2.
        /// </summary>
        public decimal AskPrice { get; set; }

        /// <summary>
        /// The trading volume in the base currency over the last 24 hours.
        /// Corresponds to 'volume' in JSON1 or 'volume_24h' in JSON2.
        /// </summary>
        public decimal Volume24h { get; set; }

        /// <summary>
        /// The average price over the last 24 hours.
        /// Corresponds to 'average' in JSON1 or 'avg_24h' in JSON2.
        /// </summary>
        public decimal AveragePrice24h { get; set; }

        /// <summary>
        /// The percentage change in price over the last 24 hours.
        /// Corresponds to 'dailyPercent' in JSON1 or 'change_24h' in JSON2.
        /// NOTE: JSON1 provides it as a decimal (e.g., 0.09 for 0.09%),
        /// while JSON2 provides it as a string (e.g., "0.11" for 0.11%).
        /// Store consistently, e.g., as the actual percentage value (0.11).
        /// </summary>
        public decimal DailyChangePercent { get; set; }

        // -- Optional derived properties --

        /// <summary>
        /// Gets the numerator/base symbol (e.g., "BTC").
        /// Can be derived from PairIdentifier if needed.
        /// </summary>
        public string? NumeratorSymbol => !string.IsNullOrEmpty(PairIdentifier) ? PairIdentifier.Split('_', '/')[0] : null; // Basic split logic

        /// <summary>
        /// Gets the denominator/quote symbol (e.g., "TRY").
        /// Can be derived from PairIdentifier if needed.
        /// </summary>
        public string? DenominatorSymbol
        {
            get
            {
                if (string.IsNullOrEmpty(PairIdentifier))
                {
                    return null;
                }

                var parts = PairIdentifier.Split('_', '/');
                return (parts.Length > 1) ? parts[1] : null; // Basic split logic
            }
        }

        /// <summary>
        /// Gets the Timestamp as a DateTimeOffset object.
        /// </summary>
        public DateTimeOffset Timestamp => DateTimeOffset.FromUnixTimeMilliseconds(TimestampMs);

        // Constructor (optional, but can be useful for initialization)
        public TickerViewModel() { }
    }
}

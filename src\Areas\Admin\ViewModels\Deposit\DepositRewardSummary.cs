using RazeWinComTr.Areas.Admin.Enums;
using System.Collections.Generic;

namespace RazeWinComTr.Areas.Admin.ViewModels.Deposit
{
    public class DepositRewardSummary
    {
        public int DepositId { get; set; }
        public decimal DepositAmount { get; set; }
        public DateTime DepositDate { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string UserFullName { get; set; } = string.Empty;
        public DepositRewardStatus RewardStatus { get; set; }
        public DateTime? ProcessDate { get; set; }
        public int RewardedUsersCount { get; set; }
        public decimal TotalRzwDistributed { get; set; }
        public decimal TotalTlDistributed { get; set; }
        public decimal RzwBuyPrice { get; set; }
        public List<RewardDetailViewModel> Rewards { get; set; } = [];

        // TRY cinsinden toplam değer
        public decimal TotalTryValue => TotalRzwDistributed * RzwBuyPrice;
    }

    public class RewardDetailViewModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string UserFullName { get; set; } = string.Empty;
        public int ReferredUserId { get; set; }
        public string ReferredUserEmail { get; set; } = string.Empty;
        public string ReferredUserFullName { get; set; } = string.Empty;
        public int Level { get; set; }
        public decimal RzwAmount { get; set; }
        public decimal TlAmount { get; set; }
        public decimal RzwPercentage { get; set; }
        public decimal TlPercentage { get; set; }
        public decimal DepositAmount { get; set; }
        public decimal RzwBuyPrice { get; set; }
        public string RewardType { get; set; } = string.Empty;
        public DateTime DepositDate { get; set; }
        public int? PackageId { get; set; }
        public string PackageName { get; set; } = string.Empty;

        // TRY cinsinden değer
        public decimal TryValue => RzwAmount * RzwBuyPrice;
    }
}

﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RazeWinComTr.Areas.Admin.ViewModels.Market
{
    public class MarketViewModel
    {
        public int Id { get; set; }

        [Required]
        [StringLength(50)]
        public string Coin { get; set; } = string.Empty;

        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        public decimal BuyPrice { get; set; }

        /// <summary>
        /// The buy price of the coin formatted as a string without rounding
        /// </summary>
        public string BuyPriceFormatted { get; set; } = string.Empty;

        public decimal SellPrice { get; set; }

        /// <summary>
        /// The sell price of the coin formatted as a string without rounding
        /// </summary>
        public string SellPriceFormatted { get; set; } = string.Empty;

        public decimal? Change24h { get; set; }

        public decimal GeneralIncrease { get; set; }

        public int DecimalPlaces { get; set; }

        public decimal MinimumBuy { get; set; }

        public decimal MaximumBuy { get; set; }

        public decimal MinimumSell { get; set; }

        public decimal MaximumSell { get; set; }

        [StringLength(255)]
        public string? IconUrl { get; set; }

        public int IsApi { get; set; }

        [StringLength(50)]
        public string? ApiServiceName { get; set; }

        public int IsActive { get; set; }

        public int Order { get; set; }

        [StringLength(50)]
        public string PairCode { get; set; } = string.Empty;

        public DateTime CrDate { get; set; }

        public DateTime? ModDate { get; set; }

        public DateTime? LastPriceUpdate { get; set; }
    }
}

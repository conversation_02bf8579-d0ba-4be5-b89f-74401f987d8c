using RazeWinComTr.Areas.Admin.DbModel;

namespace RazeWinComTr.Areas.Admin.ViewModels.Referral
{
    public class ReferralRewardViewModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string UserFullName { get; set; } = string.Empty;
        public int ReferredUserId { get; set; }
        public string ReferredUserEmail { get; set; } = string.Empty;
        public string ReferredUserFullName { get; set; } = string.Empty;
        public int PackageId { get; set; }
        public string PackageName { get; set; } = string.Empty;
        public int Level { get; set; }
        public decimal RzwAmount { get; set; }
        public decimal TlAmount { get; set; }
        public decimal RzwPercentage { get; set; }
        public decimal TlPercentage { get; set; }
        public decimal OriginalAmount { get; set; }
        public ReferralRewardStatus Status { get; set; }
        public DateTime? DepositDate { get; set; }
        public DateTime CreatedDate { get; set; }
    }
}

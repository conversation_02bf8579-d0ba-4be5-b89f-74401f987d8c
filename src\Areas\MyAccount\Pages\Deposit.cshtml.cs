using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;

namespace RazeWinComTr.Areas.MyAccount.Pages
{
    public class DepositModel : PageModel
    {
        private readonly SettingService _settingService;
        private readonly IStringLocalizer<SharedResource> _localizer;
        private readonly DepositService _depositService;
        private readonly AppDbContext _context;

        public DepositModel(SettingService settingService, IStringLocalizer<SharedResource> localizer, DepositService depositService, AppDbContext context)
        {
            _settingService = settingService;
            _localizer = localizer;
            _depositService = depositService;
            _context = context;
            Deposits = new List<DepositViewModel>();
        }

        public List<DepositViewModel> Deposits { get; set; }
        public string UserFullName { get; set; } = string.Empty;
        public string UserEmail { get; set; } = string.Empty;
        public string UserPhone { get; set; } = string.Empty;
        public DateTime UserCreatedDate { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            // Get user information
            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
            if (user == null)
            {
                return NotFound();
            }

            UserFullName = $"{user.Name} {user.Surname}";
            UserEmail = user.Email;
            UserPhone = user.PhoneNumber;
            UserCreatedDate = user.CrDate;

            // Get user's deposit history
            Deposits = await _depositService.GetByUserIdAsync(userId.Value);

            return Page();
        }
    }
}

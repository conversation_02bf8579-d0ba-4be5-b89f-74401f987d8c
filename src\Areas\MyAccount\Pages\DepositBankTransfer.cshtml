@page
@model RazeWinComTr.Areas.MyAccount.Pages.DepositBankTransferModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Services
@inject SettingService _settingService
@inject IStringLocalizer<SharedResource> Localizer
@{
    var bankTransferNote = await _settingService.GetSettingAsync("bankatransfer_note", "Lütfen açıklama kısmına kullanıcı ID'nizi yazınız");
}

<style>
    .deposit-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px 15px;
    }

    .deposit-card {
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        background-color: #fff;
        margin-bottom: 30px;
    }

    .deposit-card .card-header {
        background: linear-gradient(135deg, #3a7bd5, #00d2ff);
        color: white;
        padding: 16px 20px;
        font-size: 1.2rem;
        border-bottom: none;
    }

    .deposit-card .card-header a {
        color: white !important;
        font-weight: bold;
        text-decoration: none;
        margin-right: 10px;
        transition: all 0.3s ease;
    }

    .deposit-card .card-header a:hover {
        transform: translateX(-3px);
    }

    .deposit-card .card-body {
        padding: 25px;
    }

    .deposit-card .card-footer {
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
        padding: 15px 25px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        font-weight: 600;
        margin-bottom: 8px;
        display: block;
    }

    .form-control {
        border-radius: 6px;
        border: 1px solid #ddd;
        padding: 10px 15px;
        height: auto;
        font-size: 15px;
    }

    .form-control:focus {
        border-color: #3a7bd5;
        box-shadow: 0 0 0 0.2rem rgba(58, 123, 213, 0.25);
    }

    .is-invalid {
        border-color: #dc3545 !important;
        padding-right: calc(1.5em + 0.75rem) !important;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e") !important;
        background-repeat: no-repeat !important;
        background-position: right calc(0.375em + 0.1875rem) center !important;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem) !important;
    }

    .validation-summary-errors {
        color: #dc3545;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 0.25rem;
        padding: 0.75rem 1.25rem;
        margin-bottom: 1rem;
    }

    .validation-summary-errors ul {
        margin-bottom: 0;
        padding-left: 1.5rem;
    }

    .bank-info {
        margin-bottom: 20px;
    }

    .list-group-item-info {
        background-color: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
    }

    .list-group-item {
        position: relative;
        display: block;
        padding: 0.75rem 1.25rem;
        margin-bottom: -1px;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.125);
    }

    .deposit-limits {
        background-color: #fff8e1;
        border-left: 4px solid #ffc107;
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 0 8px 8px 0;
    }

    .deposit-limits h4 {
        color: #ff9800;
        margin-bottom: 10px;
        font-size: 16px;
    }

    .security-info {
        text-align: center;
        color: #6c757d;
        font-size: 14px;
        margin-top: 20px;
    }

    .security-info i {
        margin-right: 5px;
        color: #28a745;
    }

    .security-badge {
        background-color: #28a745;
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        margin-left: 5px;
    }

    .back-link {
        display: inline-flex;
        align-items: center;
    }

    .back-link i {
        margin-right: 5px;
    }

    .btn {
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .alert {
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeeba;
        color: #856404;
    }

    .bank-selection {
        margin-bottom: 20px;
    }

    .bank-option {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .bank-option:hover {
        border-color: #3a7bd5;
        background-color: #f8f9fa;
    }

    .bank-option.selected {
        border-color: #28a745;
        background-color: #f0fff4;
    }

    .bank-option .bank-name {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 5px;
    }

    .bank-option .account-holder {
        color: #6c757d;
        margin-bottom: 5px;
    }

    .bank-option .iban {
        font-family: monospace;
        letter-spacing: 1px;
    }
</style>

<div class="deposit-container">
    <div class="deposit-card">
        <div class="card-header">
            <a href="/MyAccount/Deposit" class="back-link">
                <i class="fas fa-chevron-left"></i>
            </a>
            <span>@Localizer["Bank Transfer"]</span><span class="font-weight-normal ml-1">@Localizer["Make Deposit With"]</span>
        </div>

        <form id="form" method="post">
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <strong class="d-block mb-2">@Localizer["Important"]</strong>
                    <p class="mb-0">@Localizer["Make Deposit Via Bank Transfer"]</p>
                </div>

                @if (!string.IsNullOrEmpty(Model.AlertMessage))
                {
                    <div class="alert <EMAIL>" role="alert">
                        @Model.AlertMessage
                    </div>
                }

                <div class="deposit-limits">
                    <span class="text-danger font-weight-bold">@bankTransferNote</span>
                    <p class="text-secondary mt-2 mb-0">@Localizer["Please Do Not Click The Completed Deposit Button Before Making The Deposit"]</p>
                </div>

                <div class="form-group">
                    <label asp-for="DepositInfo.FullName">@Localizer["Sender Full Name"]</label>
                    <input asp-for="DepositInfo.FullName" class="form-control" placeholder="@Localizer["Enter Your Full Name"]" />
                    <span asp-validation-for="DepositInfo.FullName" class="text-danger"></span>
                </div>

                <div class="form-group">
                    <label asp-for="DepositInfo.Phone">@Localizer["Phone Number"]</label>
                    <input asp-for="DepositInfo.Phone" class="form-control" />
                    <span asp-validation-for="DepositInfo.Phone" class="text-danger"></span>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="DepositInfo.BankId">@Localizer["Select Bank"]</label>
                            <select asp-for="DepositInfo.BankId" class="form-control bankselect">
                                <option value="">@Localizer["Please Select Bank"]</option>
                                @foreach (var bank in Model.Banks)
                                {
                                    <option value="@bank.Id"
                                            data-banka="@bank.BankName"
                                            data-adsoyad="@bank.AccountHolder"
                                            data-iban="@bank.Iban">
                                        @bank.BankName
                                    </option>
                                }
                            </select>
                            <span asp-validation-for="DepositInfo.BankId" class="text-danger"></span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div id="bankDetails" class="bank-info" style="display: none;">
                            <ul class="list-group mb-4" id="bank-info">
                                <li class="list-group-item list-group-item-info px-3">
                                    <span>@Localizer["Bank Information"]</span>
                                </li>
                                <li class="list-group-item list-group-item-info px-3">
                                    <strong>@Localizer["Account Holder"]</strong>
                                    <p class="mb-0 mt-1" id="accountHolder"></p>
                                </li>
                                <li class="list-group-item list-group-item-info px-3">
                                    <strong>IBAN</strong>
                                    <p class="mb-0 mt-1" id="iban"></p>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>



                <div class="form-group">
                    <label asp-for="DepositInfo.TransferTime">@Localizer["Transfer Time"]</label>
                    <input asp-for="DepositInfo.TransferTime" class="form-control" placeholder="@Localizer["Example Time Format"]" />
                    <span asp-validation-for="DepositInfo.TransferTime" class="text-danger"></span>
                    <small class="form-text text-muted">@Localizer["Enter Bank Transfer Time"]</small>
                </div>

                <div class="form-group">
                    <label asp-for="DepositInfo.CustomerNote">@Localizer["Description"]</label>
                    <textarea asp-for="DepositInfo.CustomerNote" class="form-control" rows="3" id="descriptionField" readonly></textarea>
                    <span asp-validation-for="DepositInfo.CustomerNote" class="text-danger"></span>
                    <small class="form-text text-muted">@Localizer["Auto Generated Description For Tracking"]</small>
                </div>

                <div class="form-group">
                    <label>@Localizer["Deposit Amount"] (₺)</label>
                    <div class="input-group money-input-group">
                        <input asp-for="DepositInfo.AmountInteger" class="form-control integer-part" placeholder="0" type="number" min="0" step="1" inputmode="numeric" oninput="updateDescription()">
                        <div class="input-group-append input-group-prepend decimal-separator">
                            <span class="input-group-text">.</span>
                        </div>
                        <input asp-for="DepositInfo.AmountFraction" class="form-control fraction-part" placeholder="00" type="text" pattern="[0-9]*" maxlength="2" inputmode="numeric" oninput="validateFraction(); updateDescription()">
                        <div class="input-group-append">
                            <span class="input-group-text">₺</span>
                        </div>
                    </div>
                    <span asp-validation-for="DepositInfo.AmountInteger" class="text-danger"></span>
                    <span asp-validation-for="DepositInfo.AmountFraction" class="text-danger"></span>
                </div>
                <style>
                    /* Para girişi için özel stiller */
                    .money-input-group {
                        display: flex;
                    }
                    .money-input-group .integer-part {
                        width: 120px;
                        text-align: right;
                        border-right: none;
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                        color: #000;
                    }
                    .money-input-group .decimal-separator {
                        margin: 0;
                        padding: 0;
                    }
                    .money-input-group .decimal-separator .input-group-text {
                        border-radius: 0;
                        border-left: none;
                        border-right: none;
                        padding: 0;
                        width: 10px;
                        min-width: 10px;
                        text-align: center;
                        background-color: white;
                        font-weight: bold;
                        color: #000;
                    }
                    .money-input-group .fraction-part {
                        width: 60px;
                        border-left: none;
                        border-radius: 0;
                        color: #000;
                    }
                    .money-input-group.focused {
                        box-shadow: 0 0 0 0.2rem rgba(58, 123, 213, 0.15);
                    }
                </style>

                <div asp-validation-summary="All" class="text-danger"></div>
            </div>

            <div class="card-footer">
                <div class="d-flex flex-column flex-sm-row-reverse">
                    <button type="submit" id="submitButton" class="btn btn-success mb-2 mb-sm-0 ml-sm-2">
                        <i class="fas fa-check mr-2"></i>@Localizer["I Made The Deposit"]
                    </button>
                    <a href="/MyAccount/Deposit" class="btn btn-danger">
                        <i class="fas fa-times mr-2"></i>@Localizer["Cancel"]
                    </a>
                </div>
            </div>
        </form>
    </div>

    <div class="security-info">
        <i class="fas fa-shield-alt"></i>
        <span>@Localizer["Secure 256-bit TLS-encryption"] <span class="security-badge">SSL</span></span>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        function validateFraction() {
            // Handle decimal part input
            var fractionInput = document.getElementById('DepositInfo_AmountFraction');
            if (fractionInput) {
                // Remove any non-digit characters
                var value = fractionInput.value.replace(/[^0-9]/g, '');
                fractionInput.value = value;

                // Limit to 2 digits for decimal part
                if (value.length > 2) {
                    fractionInput.value = value.substring(0, 2);
                }
            }
        }

        // Add custom validation method for combined amount
        $.validator.addMethod("combinedAmountGreaterThanZero", function(value, element, param) {
            var integerPart = parseInt($("#DepositInfo_AmountInteger").val()) || 0;
            var fractionPart = $("#DepositInfo_AmountFraction").val() || '0';

            // If both parts are 0, validation fails
            if (integerPart === 0 && (fractionPart === '0' || fractionPart === '' || parseInt(fractionPart) === 0)) {
                return false;
            }
            return true;
        }, '@Localizer["Total Amount Must Be Greater Than Zero"]');

        // Function to show SweetAlert for successful form submission
        function showSuccessAlert(message) {
            Swal.fire({
                title: window.t["Success"] || 'Başarılı!',
                text: message,
                icon: 'success',
                confirmButtonText: window.t["OK"] || 'Tamam',
                confirmButtonColor: '#28a745',
                allowOutsideClick: false,
                customClass: {
                    confirmButton: 'btn btn-success btn-lg',
                    popup: 'animated fadeInDown'
                },
                padding: '2em',
                showConfirmButton: true
            }).then(function () {
                window.location.href = '/MyAccount/Deposit';
            });
        }

        $(document).ready(function () {
            // Check for success message in Model or TempData and show SweetAlert
            @if (!string.IsNullOrEmpty(Model.AlertMessage) && Model.AlertType == "success")
            {
                <text>
                showSuccessAlert('@Html.Raw(Model.AlertMessage)');
                </text>
            }
            @if (TempData["AlertMessage"] != null && TempData["AlertType"]?.ToString() == "success")
            {
                <text>
                showSuccessAlert('@Html.Raw(TempData["AlertMessage"])');
                </text>
            }

            // Initialize description field
            updateDescription();
            // Bank selection handling
            $('.bankselect').change(function() {
                var selectedOption = this.selectedOptions[0];
                if (selectedOption && selectedOption.value) {
                    var banka = selectedOption.getAttribute('data-banka');
                    var adsoyad = selectedOption.getAttribute('data-adsoyad');
                    var iban = selectedOption.getAttribute('data-iban');

                    $('#accountHolder').text(adsoyad);
                    $('#iban').text(iban);
                    $('#bankDetails').show();

                    // Update description when bank changes
                    updateDescription();
                } else {
                    $('#bankDetails').hide();
                }
            });

            // Trigger change event if a bank is already selected
            if ($('#DepositInfo_BankId').val()) {
                $('#DepositInfo_BankId').trigger('change');
            }

            // Transfer time validation
            $('#DepositInfo_TransferTime').on('input', function() {
                var input = $(this);
                var value = input.val();

                // Remove non-numeric characters except colon
                value = value.replace(/[^0-9:]/g, '');

                // Ensure only one colon
                var parts = value.split(':');
                if (parts.length > 2) {
                    value = parts[0] + ':' + parts.slice(1).join('');
                }

                // Format as HH:MM
                if (parts.length === 1 && value.length > 2) {
                    value = value.substring(0, 2) + ':' + value.substring(2);
                }

                // Limit hours to 0-23
                if (parts.length > 0 && parts[0].length > 0) {
                    var hours = parseInt(parts[0]);
                    if (hours > 23) {
                        parts[0] = '23';
                        value = parts.join(':');
                    }
                }

                // Limit minutes to 0-59
                if (parts.length > 1 && parts[1].length > 0) {
                    var minutes = parseInt(parts[1]);
                    if (minutes > 59) {
                        parts[1] = '59';
                        value = parts.join(':');
                    }
                }

                input.val(value);

                // Update description field
                updateDescription();
            });

            // Update description when amount changes
            $('#DepositInfo_AmountInteger, #DepositInfo_AmountFraction').on('input', function() {
                updateDescription();
            });

            // Handle comma or period key press in integer field to move cursor to fraction field
            $("#DepositInfo_AmountInteger").on('keydown', function(e) {
                // Check if the key pressed is comma (188), period (190), or numpad decimal (110)
                if (e.which === 188 || e.which === 190 || e.which === 110 || e.key === ',' || e.key === '.') {
                    e.preventDefault(); // Prevent the character from being entered
                    $("#DepositInfo_AmountFraction").focus(); // Move focus to fraction field
                }
            });

            // Also handle input event for mobile devices where keydown might not catch all cases
            $("#DepositInfo_AmountInteger").on('input', function(e) {
                var value = $(this).val();
                // If the value contains a comma or period
                if (value.includes(',') || value.includes('.')) {
                    // Remove the comma or period
                    $(this).val(value.replace(/[,\.]/g, ''));
                    // Move focus to fraction field
                    $("#DepositInfo_AmountFraction").focus();
                }
            });

            // Select all text in fraction field when it receives focus
            $("#DepositInfo_AmountFraction").on('focus', function() {
                // Use setTimeout to ensure this happens after the browser's default focus behavior
                setTimeout(() => {
                    this.select();
                }, 0);
            });

            // Focus handling for better UX
            $("#DepositInfo_AmountInteger, #DepositInfo_AmountFraction").on('focus', function() {
                $(this).closest('.money-input-group').addClass('focused');
            }).on('blur', function() {
                $(this).closest('.money-input-group').removeClass('focused');
            });

            // Function to decode HTML entities
            function decodeHtmlEntities(text) {
                var textArea = document.createElement('textarea');
                textArea.innerHTML = text;
                return textArea.value;
            }

            // Function to update description field
            function updateDescription() {
                var userId = '@Model.CurrentUserId';
                var userName = '@Model.UserFullName';
                var integerPart = $('#DepositInfo_AmountInteger').val() || '0';
                var fractionPart = $('#DepositInfo_AmountFraction').val() || '00';
                var transferTime = $('#DepositInfo_TransferTime').val() || '';
                var bankName = '';

                // Format the amount
                var amount = integerPart;
                if (fractionPart) {
                    amount += ',' + fractionPart;
                }

                // Get selected bank information
                var selectedOption = document.getElementById('DepositInfo_BankId').selectedOptions[0];
                var iban = '';
                if (selectedOption && selectedOption.value) {
                    bankName = selectedOption.getAttribute('data-banka');
                    iban = selectedOption.getAttribute('data-iban');
                }

                var today = new Date();
                var dateStr = today.getDate() + '.' + (today.getMonth() + 1) + '.' + today.getFullYear();

                // Decode HTML entities in localized strings
                var depositTransactionText = decodeHtmlEntities('@Localizer["Deposit Transaction - User ID:"]');
                var userNameText = decodeHtmlEntities('@Localizer["User Name:"]');
                var amountText = decodeHtmlEntities('@Localizer["Amount:"]');
                var currencySymbol = decodeHtmlEntities('@Localizer["Currency_Symbol"]');
                var transferTimeText = decodeHtmlEntities('@Localizer["Transfer Time:"]');
                var bankText = decodeHtmlEntities('@Localizer["Bank:"]');
                var transactionDateText = decodeHtmlEntities('@Localizer["Transaction Date:"]');

                var description = depositTransactionText + ' ' + userId;
                description += ', ' + userNameText + ' ' + userName;

                if (parseInt(integerPart) > 0 || (fractionPart && parseInt(fractionPart) > 0)) {
                    description += ', ' + amountText + ' ' + amount + ' ' + currencySymbol;
                }

                if (transferTime) {
                    description += ', ' + transferTimeText + ' ' + transferTime;
                }

                if (bankName) {
                    description += ', ' + bankText + ' ' + bankName;

                    if (iban) {
                        description += ', IBAN: ' + iban;
                    }
                }

                description += '. ' + transactionDateText + ' ' + dateStr;

                $('#descriptionField').val(description);
            }

            // Form validation
            $('#form').on('submit', function(e) {
                var isValid = true;
                var errorMessages = [];

                // Validate bank selection
                if (!$('#DepositInfo_BankId').val()) {
                    isValid = false;
                    errorMessages.push('@Localizer["Please Select Bank"]');
                    $('#DepositInfo_BankId').addClass('is-invalid');
                } else {
                    $('#DepositInfo_BankId').removeClass('is-invalid');
                }

                // Validate full name
                if (!$('#DepositInfo_FullName').val().trim()) {
                    isValid = false;
                    errorMessages.push('@Localizer["Sender Full Name Required"]');
                    $('#DepositInfo_FullName').addClass('is-invalid');
                } else {
                    $('#DepositInfo_FullName').removeClass('is-invalid');
                }

                // Validate phone
                if (!$('#DepositInfo_Phone').val().trim()) {
                    isValid = false;
                    errorMessages.push('@Localizer["Phone Required"]');
                    $('#DepositInfo_Phone').addClass('is-invalid');
                } else {
                    $('#DepositInfo_Phone').removeClass('is-invalid');
                }

                // Validate transfer time
                if (!$('#DepositInfo_TransferTime').val().trim()) {
                    isValid = false;
                    errorMessages.push('@Localizer["Transfer Time Required"]');
                    $('#DepositInfo_TransferTime').addClass('is-invalid');
                } else {
                    $('#DepositInfo_TransferTime').removeClass('is-invalid');
                }

                // Validate amount
                var integerPart = $('#DepositInfo_AmountInteger').val() || '0';
                var fractionPart = $('#DepositInfo_AmountFraction').val() || '00';
                var intValue = parseInt(integerPart);
                var fracValue = parseInt(fractionPart);

                // Check if the combined amount is greater than zero
                if (intValue <= 0 && (fracValue <= 0 || fractionPart === '0' || fractionPart === '')) {
                    isValid = false;
                    errorMessages.push('@Localizer["Total Amount Must Be Greater Than Zero"]');
                    $('#DepositInfo_AmountInteger').addClass('is-invalid');
                    $('#DepositInfo_AmountFraction').addClass('is-invalid');
                } else {
                    $('#DepositInfo_AmountInteger').removeClass('is-invalid');
                    $('#DepositInfo_AmountFraction').removeClass('is-invalid');
                }

                // If validation fails, show error messages and prevent form submission
                if (!isValid) {
                    e.preventDefault();

                    // Display error messages
                    var validationSummary = $('.validation-summary-errors ul');
                    if (validationSummary.length === 0) {
                        // Create validation summary if it doesn't exist
                        $('.validation-summary-valid').removeClass('validation-summary-valid').addClass('validation-summary-errors');
                        validationSummary = $('<ul></ul>');
                        $('.validation-summary-errors').append(validationSummary);
                    } else {
                        validationSummary.empty();
                    }

                    // Add error messages to validation summary
                    $.each(errorMessages, function(index, message) {
                        validationSummary.append('<li>' + message + '</li>');
                    });

                    // Scroll to the top of the form to show the error messages
                    $('html, body').animate({
                        scrollTop: $('.validation-summary-errors').offset().top - 100
                    }, 500);
                }
            });
        });
    </script>
}

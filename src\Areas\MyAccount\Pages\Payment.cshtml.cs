using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.ViewModels.Bank;
using RazeWinComTr.Areas.Admin.ViewModels.Deposit;
using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Areas.MyAccount.Pages;

public class PaymentModel : PageModel
{
    private readonly AppDbContext _context;
    private readonly BankService _bankService;
    private readonly DepositService _depositService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PaymentModel(AppDbContext context, BankService bankService, DepositService depositService, IStringLocalizer<SharedResource> localizer)
    {
        _context = context;
        _bankService = bankService;
        _depositService = depositService;
        _localizer = localizer;
        Banks = new List<BankViewModel>();
        Deposits = new List<DepositViewModel>();
        PaymentInput = new PaymentInputModel();
    }

    public List<BankViewModel> Banks { get; set; }
    public List<DepositViewModel> Deposits { get; set; }

    public string UserFullName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string UserPhone { get; set; } = string.Empty;
    public DateTime UserCreatedDate { get; set; }

    [BindProperty]
    public PaymentInputModel PaymentInput { get; set; }

    [TempData]
    public string SuccessMessage { get; set; } = string.Empty;

    [TempData]
    public string ErrorMessage { get; set; } = string.Empty;

    public async Task<IActionResult> OnGetAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Get user information
        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null)
        {
            return NotFound();
        }

        UserFullName = $"{user.Name} {user.Surname}";
        UserEmail = user.Email;
        UserPhone = user.PhoneNumber;
        UserCreatedDate = user.CrDate;

        // Get active banks
        Banks = await _bankService.GetListAsync();
        Banks = Banks.Where(b => b.IsActive).ToList();

        // Get user's deposit history
        Deposits = await _depositService.GetByUserIdAsync(userId.Value);

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            await OnGetAsync();
            return Page();
        }

        try
        {
            var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
            if (user == null)
            {
                return NotFound();
            }

            var bank = await _bankService.GetByIdAsync(PaymentInput.BankId);
            if (bank == null)
            {
                ModelState.AddModelError("PaymentInput.BankId", _localizer["Selected bank not found."]);
                await OnGetAsync();
                return Page();
            }

            // Create deposit record
            var deposit = new Deposit
            {
                UserId = userId.Value,
                DepositType = _localizer["Bank Transfer"].Value,
                Amount = PaymentInput.Amount,
                FullName = $"{user.Name} {user.Surname}",
                ExtraData = _localizer["Bank: {0}, Reference: {1}", bank.BankName, PaymentInput.TransactionReference].Value,
                IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? _localizer["Unknown"].Value,
                CreatedDate = DateTime.UtcNow,
                ProcessStatus = _localizer["Submitted"].Value,
                Status = DepositStatus.Pending
            };

            await _depositService.CreateAsync(deposit);

            SuccessMessage = _localizer["Payment request submitted successfully. Your account will be credited once the payment is verified."].Value;
            return RedirectToPage();
        }
        catch (Exception ex)
        {
            ErrorMessage = _localizer["Error submitting payment: {0}", ex.Message].Value;
            await OnGetAsync();
            return Page();
        }
    }

    public class PaymentInputModel
    {
        [Required]
        [Range(1, 1000000)]
        public decimal Amount { get; set; }

        [Required]
        public int BankId { get; set; }

        [Required]
        [StringLength(100)]
        public string TransactionReference { get; set; } = string.Empty;
    }
}

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Models;
using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Areas.MyAccount.Pages.RzwSavings;

public class CreateModel : PageModel
{
    private readonly IRzwSavingsService _rzwSavingsService;
    private readonly IRzwWalletBalanceManagementService _rzwBalanceService;
    private readonly IRzwSavingsPlanService _rzwPlanService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CreateModel(
        IRzwSavingsService rzwSavingsService,
        IRzwWalletBalanceManagementService rzwBalanceService,
        IRzwSavingsPlanService rzwPlanService,
        IStringLocalizer<SharedResource> localizer)
    {
        _rzwSavingsService = rzwSavingsService;
        _rzwBalanceService = rzwBalanceService;
        _rzwPlanService = rzwPlanService;
        _localizer = localizer;
    }

    [BindProperty]
    public CreateSavingsAccountViewModel Input { get; set; } = new();
    
    public List<RzwSavingsPlan> AvailablePlans { get; set; } = new();
    public RzwBalanceInfo? UserRzwBalance { get; set; }
    public SelectList PlanSelectList { get; set; } = new(new List<SelectListItem>(), "Value", "Text");

    public SweetAlert2Message? AlertMessage { get; set; }

    public async Task<IActionResult> OnGetAsync(int? planId = null)
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        await LoadDataAsync(userId.Value);

        // Pre-select plan if provided
        if (planId.HasValue && AvailablePlans.Any(p => p.Id == planId.Value))
        {
            Input.PlanId = planId.Value;
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        await LoadDataAsync(userId.Value);

        if (!ModelState.IsValid)
        {
            return Page();
        }

        // Additional validation - ensure plan ID is from available active plans
        var selectedPlan = AvailablePlans.FirstOrDefault(p => p.Id == Input.PlanId);
        if (selectedPlan == null)
        {
            // Log potential manipulation attempt
            var logger = HttpContext.RequestServices.GetRequiredService<ILogger<CreateModel>>();
            logger.LogWarning("Invalid plan ID {PlanId} submitted by user {UserId}", Input.PlanId, userId.Value);

            ModelState.AddModelError(nameof(Input.PlanId), _localizer["Please select a valid plan"].Value);
            return Page();
        }

        // Validate amount against plan limits
        if (Input.RzwAmount < selectedPlan.MinRzwAmount || Input.RzwAmount > selectedPlan.MaxRzwAmount)
        {
            var minAmountStr = selectedPlan.MinRzwAmount.ToString() ?? "0";
            var maxAmountStr = selectedPlan.MaxRzwAmount.ToString() ?? "0";
            ModelState.AddModelError(nameof(Input.RzwAmount),
                _localizer["Amount must be between {0} and {1} RZW", minAmountStr, maxAmountStr].Value);
            return Page();
        }

        // Check available balance
        if (UserRzwBalance == null || Input.RzwAmount > UserRzwBalance.AvailableRzw)
        {
            ModelState.AddModelError(nameof(Input.RzwAmount), _localizer["Insufficient available RZW balance"].Value);
            return Page();
        }

        try
        {
            // Create savings account
            var result = await _rzwSavingsService.CreateSavingsAccountAsync(
                userId.Value, 
                Input.PlanId, 
                Input.RzwAmount, 
                Input.AutoRenew);

            if (result.Success)
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Success"],
                    Text = _localizer["Savings account created successfully! Your RZW tokens have been locked and will start earning interest."].Value,
                    Icon = "success",
                    RedirectUrl = $"/MyAccount/RzwSavings/Details?id={result.Account?.Id}"
                };
                return Page();
            }
            else
            {
                AlertMessage = new SweetAlert2Message
                {
                    Title = _localizer["Error"],
                    Text = result.Message,
                    Icon = "error"
                };
                return Page();
            }
        }
        catch (Exception)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = _localizer["An error occurred while creating the savings account. Please try again."].Value,
                Icon = "error"
            };
            return Page();
        }
    }

    private async Task LoadDataAsync(int userId)
    {
        try
        {
            // Get user's RZW balance
            UserRzwBalance = await _rzwBalanceService.GetRzwBalanceInfoAsync(userId);

            // Get available plans
            AvailablePlans = await _rzwPlanService.GetActivePlansAsync();

            // Create plan select list
            var planItems = AvailablePlans.Select(p => new SelectListItem
            {
                Value = p.Id.ToString(),
                Text = $"{p.Name} - {(p.InterestRate * 100):N2}% ({p.Description})"
            }).ToList();

            PlanSelectList = new SelectList(planItems, "Value", "Text", Input.PlanId);
        }
        catch (Exception)
        {
            UserRzwBalance = null;
            AvailablePlans = new List<RzwSavingsPlan>();
            PlanSelectList = new SelectList(new List<SelectListItem>(), "Value", "Text");
        }
    }

    // AJAX endpoint for plan details
    public async Task<IActionResult> OnGetPlanDetailsAsync(int planId)
    {
        try
        {
            // Ensure user is authenticated
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            var plans = await _rzwPlanService.GetActivePlansAsync();
            var plan = plans.FirstOrDefault(p => p.Id == planId);

            if (plan == null)
            {
                // Log potential manipulation attempt
                var logger = HttpContext.RequestServices.GetRequiredService<ILogger<CreateModel>>();
                logger.LogWarning("Invalid plan ID {PlanId} requested by user {UserId}", planId, userId.Value);
                return NotFound();
            }

            return new JsonResult(new
            {
                id = plan.Id,
                name = plan.Name,
                interestRate = plan.InterestRate,
                interestRatePercent = (plan.InterestRate * 100).ToString("N8").TrimEnd('0').TrimEnd('.'),
                minAmount = plan.MinRzwAmount,
                maxAmount = plan.MaxRzwAmount,
                termType = plan.TermType.ToString(),
                termDuration = plan.TermDuration,
                description = plan.Description
            });
        }
        catch (Exception)
        {
            return BadRequest();
        }
    }

    // AJAX endpoint for interest calculation
    public IActionResult OnGetCalculateInterestAsync(decimal amount, decimal interestRate, int termDuration)
    {
        try
        {
            // Ensure user is authenticated
            var userId = User.GetClaimUserId();
            if (!userId.HasValue)
            {
                return Unauthorized();
            }

            if (amount <= 0 || interestRate <= 0 || termDuration <= 0)
            {
                return BadRequest("Invalid parameters");
            }

            // Use helper for consistent compound interest calculation
            var dailyRate = interestRate;
            var finalAmount = RzwSavingsCalculationHelper.CalculateFinalAmount(amount, dailyRate, termDuration);
            var totalInterest = RzwSavingsCalculationHelper.CalculateCompoundInterest(amount, dailyRate, termDuration);

            // Daily interest is simple interest for display purposes (first day calculation)
            var dailyInterest = amount * dailyRate;

            return new JsonResult(new
            {
                initialAmount = amount.ToString("N8"),
                totalInterest = totalInterest.ToString("N8"),
                finalAmount = finalAmount.ToString("N8"),
                dailyInterest = dailyInterest.ToString("N8"),
                effectiveApy = RzwSavingsCalculationHelper.CalculateEffectiveAPY(amount, finalAmount, termDuration).ToString("N8").TrimEnd('0').TrimEnd('.')
            });
        }
        catch (Exception)
        {
            return BadRequest("Calculation error");
        }
    }
}

/// <summary>
/// ViewModel for creating a new savings account
/// </summary>
public class CreateSavingsAccountViewModel
{
    [Required(ErrorMessage = "Please select a savings plan")]
    [Display(Name = "Savings Plan")]
    public int PlanId { get; set; }

    [Required(ErrorMessage = "Please enter the RZW amount")]
    [Range(0.********, *********, ErrorMessage = "Amount must be greater than 0")]
    [Display(Name = "RZW Amount")]
    public decimal RzwAmount { get; set; }

    [Display(Name = "Auto Renew")]
    public bool AutoRenew { get; set; } = false;
}



@page
@model RazeWinComTr.Areas.MyAccount.Pages.RzwSavings.InterestHistoryModel
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["Interest History"];
}

@section Styles {
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="/plugins/datatables/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="/plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="/plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
    
    <style>
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .summary-stat {
            text-align: center;
            padding: 15px;
        }
        
        .summary-stat .value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .summary-stat .label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .filter-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .monthly-breakdown {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .history-table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .pagination-wrapper {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
    </style>
}

<!-- Content Header -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["Interest History"]</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount/Dashboard">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item"><a href="/MyAccount/RzwSavings">@Localizer["My Savings"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["Interest History"]</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Summary -->
<div class="summary-card">
    <div class="row">
        <div class="col-md-3">
            <div class="summary-stat">
                <div class="value">@Model.Summary.TotalInterestEarned.ToString("N8", new System.Globalization.CultureInfo("en-US"))</div>
                <div class="label">@Localizer["Total Interest Earned"] (RZW)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="summary-stat">
                <div class="value">@Model.Summary.TotalPayments</div>
                <div class="label">@Localizer["Total Payments"]</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="summary-stat">
                <div class="value">@Model.Summary.AverageInterestPerPayment.ToString("N8", new System.Globalization.CultureInfo("en-US"))</div>
                <div class="label">@Localizer["Average Per Payment"] (RZW)</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="summary-stat">
                <div class="value">@Model.Summary.FilteredResults</div>
                <div class="label">@Localizer["Filtered Results"]</div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="filter-card">
    <form method="get">
        <div class="row">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="FromDate">@Localizer["From Date"]</label>
                    <input asp-for="FromDate" type="date" class="form-control">
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="ToDate">@Localizer["To Date"]</label>
                    <input asp-for="ToDate" type="date" class="form-control">
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="AccountId">@Localizer["Savings Account"]</label>
                    <select asp-for="AccountId" class="form-control">
                        <option value="">@Localizer["All Accounts"]</option>
                        @foreach (var account in Model.UserSavingsAccounts)
                        {
                            <option value="@account.Id">@account.Plan?.Name - @account.RzwAmount.ToString("N8").TrimEnd('0').TrimEnd('.') RZW</option>
                        }
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>&nbsp;</label>
                    <div class="d-flex">
                        <button type="submit" class="btn btn-primary mr-2">
                            <i class="fas fa-filter mr-1"></i>@Localizer["Filter"]
                        </button>
                        <a href="/MyAccount/RzwSavings/InterestHistory" class="btn btn-secondary">
                            <i class="fas fa-times mr-1"></i>@Localizer["Clear"]
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Export Buttons -->
<div class="row mb-3">
    <div class="col-md-12 text-right">
        <div class="btn-group">
            <a href="?handler=Export&format=csv&fromDate=@Model.FromDate&toDate=@Model.ToDate&accountId=@Model.AccountId" 
               class="btn btn-success">
                <i class="fas fa-file-csv mr-1"></i>@Localizer["Export CSV"]
            </a>
            <a href="?handler=Export&format=excel&fromDate=@Model.FromDate&toDate=@Model.ToDate&accountId=@Model.AccountId" 
               class="btn btn-info">
                <i class="fas fa-file-excel mr-1"></i>@Localizer["Export Excel"]
            </a>
        </div>
    </div>
</div>

<!-- Monthly Breakdown -->
@if (Model.Summary.MonthlyBreakdown.Any())
{
    <div class="monthly-breakdown">
        <h5 class="mb-3">
            <i class="fas fa-chart-bar mr-2"></i>@Localizer["Monthly Breakdown"]
        </h5>
        <div class="row">
            @foreach (var month in Model.Summary.MonthlyBreakdown.Take(6))
            {
                <div class="col-md-2 mb-3">
                    <div class="text-center p-3 bg-light rounded">
                        <h6 class="mb-1">@month.MonthName</h6>
                        <div class="text-success font-weight-bold">@month.TotalInterest.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW</div>
                        <small class="text-muted">@month.PaymentCount @Localizer["Payments"]</small>
                    </div>
                </div>
            }
        </div>
    </div>
}

<!-- Interest History Table -->
<div class="history-table">
    <div class="card-header">
        <h5 class="m-0">
            <i class="fas fa-history mr-2"></i>@Localizer["Interest Payment History"]
        </h5>
    </div>
    <div class="card-body">
        @if (Model.InterestHistory.Any())
        {
            <div class="table-responsive">
                <table id="interestTable" class="table table-striped table-bordered">
                    <thead>
                        <tr>
                            <th>@Localizer["Date"]</th>
                            <th>@Localizer["Savings Account"]</th>
                            <th>@Localizer["Interest Amount"]</th>
                            <th>@Localizer["Balance After Interest"]</th>
                            <th>@Localizer["Days Elapsed"]</th>
                            <th>@Localizer["Status"]</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var payment in Model.InterestHistory)
                        {
                            <tr>
                                <td>@payment.PaymentDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")</td>
                                <td>
                                    <a href="/MyAccount/RzwSavings/Details/@payment.RzwSavingsAccountId" class="text-decoration-none">
                                        @payment.RzwSavingsAccount?.Plan?.Name
                                    </a>
                                </td>
                                <td class="text-success font-weight-bold">+@payment.RzwAmount.ToString("N8", new System.Globalization.CultureInfo("en-US")) RZW</td>
                                <td>@(((payment.PrincipalAmount ?? 0) + (payment.AccumulatedInterest ?? 0)).ToString("N8", new System.Globalization.CultureInfo("en-US"))) RZW</td>
                                <td>@((DateTime.UtcNow - (payment.RzwSavingsAccount?.StartDate ?? DateTime.UtcNow)).Days)</td>
                                <td>
                                    <span class="badge badge-success">@Localizer["Paid"]</span>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center text-muted py-5">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h5>@Localizer["No interest payments found"]</h5>
                <p>@Localizer["Try adjusting your filter criteria or check back later."]</p>
            </div>
        }
    </div>
</div>

<!-- Pagination -->
@if (Model.TotalPages > 1)
{
    <div class="pagination-wrapper">
        <nav aria-label="Interest history pagination">
            <ul class="pagination justify-content-center">
                <li class="page-item @(Model.HasPreviousPage ? "" : "disabled")">
                    <a class="page-link" href="?pageNumber=@(Model.PageNumber - 1)&fromDate=@Model.FromDate&toDate=@Model.ToDate&accountId=@Model.AccountId">
                        @Localizer["Previous"]
                    </a>
                </li>
                
                @for (int i = Math.Max(1, Model.PageNumber - 2); i <= Math.Min(Model.TotalPages, Model.PageNumber + 2); i++)
                {
                    <li class="page-item @(i == Model.PageNumber ? "active" : "")">
                        <a class="page-link" href="?pageNumber=@i&fromDate=@Model.FromDate&toDate=@Model.ToDate&accountId=@Model.AccountId">@i</a>
                    </li>
                }
                
                <li class="page-item @(Model.HasNextPage ? "" : "disabled")">
                    <a class="page-link" href="?pageNumber=@(Model.PageNumber + 1)&fromDate=@Model.FromDate&toDate=@Model.ToDate&accountId=@Model.AccountId">
                        @Localizer["Next"]
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="text-center text-muted">
            @Localizer["Page"] @Model.PageNumber @Localizer["of"] @Model.TotalPages 
            (@Model.Summary.FilteredResults @Localizer["total results"])
        </div>
    </div>
}

@section Scripts {
    <!-- DataTables & Plugins -->
    <script src="/plugins/datatables/js/jquery.dataTables.min.js"></script>
    <script src="/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
    <script src="/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
    <script src="/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
    <script src="/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
    <script src="/plugins/jszip/jszip.min.js"></script>
    <script src="/plugins/pdfmake/pdfmake.min.js"></script>
    <script src="/plugins/pdfmake/vfs_fonts.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.print.min.js"></script>
    <script src="/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#interestTable').DataTable({
                "responsive": true,
                "lengthChange": false,
                "autoWidth": false,
                "paging": false, // We handle pagination manually
                "info": false,
                "searching": false, // We have custom filters
                "order": [[0, "desc"]], // Sort by date descending
                "columnDefs": [
                    {
                        "targets": 0,
                        "type": "date-eu"
                    },
                    {
                        "targets": [2, 3],
                        "className": "text-right"
                    }
                ],
                "language": {
                    "emptyTable": "@Localizer["No interest payments found"]",
                    "zeroRecords": "@Localizer["No matching records found"]"
                }
            });

            // Set default date values if not set
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

            if (!$('#FromDate').val()) {
                $('#FromDate').val(thirtyDaysAgo.toISOString().split('T')[0]);
            }

            if (!$('#ToDate').val()) {
                $('#ToDate').val(today.toISOString().split('T')[0]);
            }
        });
    </script>
}

using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.MyAccount.Pages.RzwSavings;

public class InterestHistoryModel : PageModel
{
    private readonly IRzwSavingsInterestService _rzwInterestService;
    private readonly IRzwSavingsService _rzwSavingsService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public InterestHistoryModel(
        IRzwSavingsInterestService rzwInterestService,
        IRzwSavingsService rzwSavingsService,
        IStringLocalizer<SharedResource> localizer)
    {
        _rzwInterestService = rzwInterestService;
        _rzwSavingsService = rzwSavingsService;
        _localizer = localizer;
    }

    public List<RzwSavingsInterestPayment> InterestHistory { get; set; } = new();
    public List<RzwSavingsAccount> UserSavingsAccounts { get; set; } = new();
    public InterestHistorySummary Summary { get; set; } = new();

    // Filter properties
    [BindProperty(SupportsGet = true)]
    public DateTime? FromDate { get; set; }

    [BindProperty(SupportsGet = true)]
    public DateTime? ToDate { get; set; }

    [BindProperty(SupportsGet = true)]
    public int? AccountId { get; set; }

    [BindProperty(SupportsGet = true)]
    public int PageNumber { get; set; } = 1;

    public int PageSize { get; } = 50;
    public int TotalPages { get; set; }
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;

    public async Task<IActionResult> OnGetAsync()
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        try
        {
            // Get user's savings accounts for filter dropdown
            UserSavingsAccounts = await _rzwSavingsService.GetUserAllSavingsAsync(userId.Value);

            // Validate AccountId parameter if provided
            if (AccountId.HasValue)
            {
                var accountExists = UserSavingsAccounts.Any(a => a.Id == AccountId.Value);
                if (!accountExists)
                {
                    // Log unauthorized access attempt
                    var logger = HttpContext.RequestServices.GetRequiredService<ILogger<InterestHistoryModel>>();
                    logger.LogWarning("Unauthorized access attempt to account {AccountId} interest history by user {UserId}", AccountId.Value, userId.Value);

                    // Reset AccountId to prevent unauthorized data access
                    AccountId = null;
                }
            }

            // Get filtered interest history
            InterestHistory = await GetFilteredInterestHistoryAsync(userId.Value);

            // Calculate pagination
            var totalCount = await GetTotalInterestHistoryCountAsync(userId.Value);
            TotalPages = (int)Math.Ceiling(totalCount / (double)PageSize);

            // Calculate summary
            await CalculateSummaryAsync(userId.Value);
        }
        catch (Exception)
        {
            InterestHistory = new List<RzwSavingsInterestPayment>();
            UserSavingsAccounts = new List<RzwSavingsAccount>();
            Summary = new InterestHistorySummary();
        }

        return Page();
    }

    public async Task<IActionResult> OnGetExportAsync(string format = "csv")
    {
        var userId = User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        try
        {
            // Validate AccountId parameter if provided
            if (AccountId.HasValue)
            {
                var userAccounts = await _rzwSavingsService.GetUserAllSavingsAsync(userId.Value);
                var accountExists = userAccounts.Any(a => a.Id == AccountId.Value);
                if (!accountExists)
                {
                    // Log unauthorized access attempt
                    var logger = HttpContext.RequestServices.GetRequiredService<ILogger<InterestHistoryModel>>();
                    logger.LogWarning("Unauthorized export attempt for account {AccountId} by user {UserId}", AccountId.Value, userId.Value);

                    return Forbid("You don't have permission to export data for this account.");
                }
            }

            // Get all interest history (without pagination for export)
            var allHistory = await _rzwInterestService.GetInterestHistoryAsync(userId.Value, AccountId, FromDate, ToDate);

            switch (format.ToLower())
            {
                case "csv":
                    return await ExportToCsvAsync(allHistory);
                case "excel":
                    return await ExportToExcelAsync(allHistory);
                default:
                    return BadRequest("Unsupported export format");
            }
        }
        catch (Exception)
        {
            return BadRequest("Export failed");
        }
    }

    private async Task<List<RzwSavingsInterestPayment>> GetFilteredInterestHistoryAsync(int userId)
    {
        return await _rzwInterestService.GetInterestHistoryAsync(userId, AccountId, FromDate, ToDate, PageNumber, PageSize);
    }

    private async Task<int> GetTotalInterestHistoryCountAsync(int userId)
    {
        return await _rzwInterestService.GetInterestHistoryCountAsync(userId, AccountId, FromDate, ToDate);
    }

    private async Task CalculateSummaryAsync(int userId)
    {
        // Get all interest history for summary (not paginated)
        var allHistory = await _rzwInterestService.GetInterestHistoryAsync(userId, AccountId, FromDate, ToDate);

        Summary = new InterestHistorySummary
        {
            TotalInterestEarned = allHistory.Sum(h => h.RzwAmount),
            TotalPayments = allHistory.Count,
            AverageInterestPerPayment = allHistory.Any() ? allHistory.Average(h => h.RzwAmount) : 0,
            FirstPaymentDate = allHistory.OrderBy(h => h.PaymentDate).FirstOrDefault()?.PaymentDate,
            LastPaymentDate = allHistory.OrderByDescending(h => h.PaymentDate).FirstOrDefault()?.PaymentDate,
            FilteredResults = allHistory.Count
        };

        // Calculate monthly breakdown
        Summary.MonthlyBreakdown = allHistory
            .GroupBy(h => new { h.PaymentDate.Year, h.PaymentDate.Month })
            .Select(g => new MonthlyInterestSummary
            {
                Year = g.Key.Year,
                Month = g.Key.Month,
                TotalInterest = g.Sum(h => h.RzwAmount),
                PaymentCount = g.Count()
            })
            .OrderByDescending(m => m.Year)
            .ThenByDescending(m => m.Month)
            .ToList();
    }

    private Task<IActionResult> ExportToCsvAsync(List<RzwSavingsInterestPayment> history)
    {
        var csv = new System.Text.StringBuilder();
        csv.AppendLine("Date,Account,Interest Amount,Balance After Interest,Days Elapsed");

        foreach (var payment in history.OrderByDescending(h => h.PaymentDate))
        {
            var balanceAfterInterest = (payment.PrincipalAmount ?? 0) + (payment.AccumulatedInterest ?? 0);
            var daysElapsed = (DateTime.UtcNow - (payment.RzwSavingsAccount?.StartDate ?? DateTime.UtcNow)).Days;

            csv.AppendLine($"{payment.PaymentDate:yyyy-MM-dd HH:mm:ss}," +
                          $"{payment.RzwSavingsAccount?.Plan?.Name ?? "N/A"}," +
                          $"{payment.RzwAmount:N8}," +
                          $"{balanceAfterInterest:N8}," +
                          $"{daysElapsed}");
        }

        var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
        var fileName = $"RZW_Interest_History_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
        
        return Task.FromResult<IActionResult>(File(bytes, "text/csv", fileName));
    }

    private async Task<IActionResult> ExportToExcelAsync(List<RzwSavingsInterestPayment> history)
    {
        // For now, return CSV format. In a real implementation, you would use a library like EPPlus
        return await ExportToCsvAsync(history);
    }
}

/// <summary>
/// Summary information for interest history
/// </summary>
public class InterestHistorySummary
{
    public decimal TotalInterestEarned { get; set; }
    public int TotalPayments { get; set; }
    public decimal AverageInterestPerPayment { get; set; }
    public DateTime? FirstPaymentDate { get; set; }
    public DateTime? LastPaymentDate { get; set; }
    public int FilteredResults { get; set; }
    public List<MonthlyInterestSummary> MonthlyBreakdown { get; set; } = new();
}

/// <summary>
/// Monthly interest summary
/// </summary>
public class MonthlyInterestSummary
{
    public int Year { get; set; }
    public int Month { get; set; }
    public decimal TotalInterest { get; set; }
    public int PaymentCount { get; set; }
    
    public string MonthName => new DateTime(Year, Month, 1).ToString("MMMM yyyy");
}

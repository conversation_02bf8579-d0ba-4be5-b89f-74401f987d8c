@using Microsoft.Extensions.Localization
@inject IStringLocalizer<SharedResource> Localizer

@if (User.Identity != null && User.Identity.IsAuthenticated)
{
    <div class="headerTopRight">
        <ul>
            <li><a href="/MyAccount/Dashboard"><i class="flaticon-user-3 textX blueX"></i>@User.Identity.Name</a></li>
            <li><a href="/MyAccount/TradeHistory"><i class="flaticon-bars textX blueX"></i>@Localizer["My Trade History"]</a></li>
            <li><a href="/MyAccount/Wallet"><i class="flaticon-wallet textX blueX"></i>@Localizer["My Wallet"]</a></li>
            <li><a href="/MyAccount/BalanceHistory"><i class="flaticon-history textX blueX"></i>@Localizer["Balance History"]</a></li>
            <li><a href="/MyAccount/Packages"><i class="flaticon-gift textX blueX"></i>@Localizer["My Packages"]</a></li>
            <li><a href="/MyAccount/Profile"><i class="flaticon-user-3 textX blueX"></i>@Localizer["My Profile"]</a></li>
            <li><a href="/MyAccount/Logout"><i class="flaticon-close textX pinkX"></i>@Localizer["Logout"]</a></li>
        </ul>
    </div>
}
else
{
    <div class="headerTopRight">
        <ul>
            <li><a href="/login"><i class="flaticon-user-3 textX blueX"></i>GIRIŞ YAP</a></li>
            <li><a href="/register"><i class="flaticon-quit textX greenX"></i>HESAP AÇ</a></li>
        </ul>
    </div>
}

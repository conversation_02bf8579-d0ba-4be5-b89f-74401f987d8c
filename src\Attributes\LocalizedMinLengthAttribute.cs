﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Resources;

namespace RazeWinComTr.Attributes;

public class LocalizedMinLengthAttribute : ValidationAttribute
{
    public int Length { get; }

    private static readonly ResourceManager ResourceManager = new ResourceManager("RazeWinComTr.Areas.Admin.Resources.SharedResource", typeof(LocalizedMinLengthAttribute).Assembly);

    public LocalizedMinLengthAttribute(int length)
    {
        if (length < 0)
            throw new ArgumentOutOfRangeException(nameof(length), "Minimum length must be non-negative.");

        Length = length;
    }

    public override bool IsValid(object? value)
    {
        if (value == null) return true;

        var length = GetLength(value);
        return length >= Length;
    }

    public override string FormatErrorMessage(string name)
    {
        string template = ResourceManager.GetString("MinLength_Error", CultureInfo.CurrentUICulture)
                          ?? "The field {0} must be at least {1} characters long.";

        return string.Format(CultureInfo.CurrentCulture, template, name, Length);
    }

    private int GetLength(object value)
    {
        if (value is string str)
            return str.Length;

        if (value is Array arr)
            return arr.Length;

        throw new InvalidOperationException("LocalizedMinLengthAttribute can only be applied to string or array types.");
    }
}

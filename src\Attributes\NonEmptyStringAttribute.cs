using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Attributes;

/// <summary>
/// Validation attribute to ensure string values are not null, empty, or whitespace
/// </summary>
public class NonEmptyStringAttribute : ValidationAttribute
{
    public NonEmptyStringAttribute()
    {
        ErrorMessage = "Value cannot be empty or whitespace";
    }

    public override bool IsValid(object? value)
    {
        if (value == null)
            return true; // Use [Required] for null checks

        if (value is string stringValue)
        {
            return !string.IsNullOrWhiteSpace(stringValue);
        }

        return false;
    }
}

using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Attributes;

/// <summary>
/// Validation attribute to ensure decimal values are positive (greater than 0)
/// </summary>
public class PositiveDecimalAttribute : ValidationAttribute
{
    public PositiveDecimalAttribute()
    {
        ErrorMessage = "Value must be greater than 0";
    }

    public override bool IsValid(object? value)
    {
        if (value == null)
            return true; // Use [Required] for null checks

        if (value is decimal decimalValue)
        {
            return decimalValue > 0;
        }

        if (decimal.TryParse(value.ToString(), out var parsedValue))
        {
            return parsedValue > 0;
        }

        return false;
    }
}

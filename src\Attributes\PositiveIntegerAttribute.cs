using System.ComponentModel.DataAnnotations;

namespace RazeWinComTr.Attributes;

/// <summary>
/// Validation attribute to ensure integer values are positive (greater than 0)
/// </summary>
public class PositiveIntegerAttribute : ValidationAttribute
{
    public PositiveIntegerAttribute()
    {
        ErrorMessage = "Value must be greater than 0";
    }

    public override bool IsValid(object? value)
    {
        if (value == null)
            return true; // Use [Required] for null checks

        if (value is int intValue)
        {
            return intValue > 0;
        }

        if (int.TryParse(value.ToString(), out var parsedValue))
        {
            return parsedValue > 0;
        }

        return false;
    }
}

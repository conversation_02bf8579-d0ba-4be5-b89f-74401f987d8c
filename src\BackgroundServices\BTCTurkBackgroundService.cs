using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RazeWinComTr.Areas.Admin.Constants;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.BackgroundServices;

/// <summary>
/// Background service for updating market pairs from BTCTurk API
/// </summary>
public class BTCTurkBackgroundService : CryptoExchangeBackgroundService
{
    public BTCTurkBackgroundService(
        ILogger<BTCTurkBackgroundService> logger,
        IServiceScopeFactory serviceScopeFactory,
        IOptions<CryptoExchangeBackgroundServiceOptions> options)
        : base(logger, serviceScopeFactory, options, ApiServiceNames.BTCTurk)
    {
    }

    protected override async Task UpdateMarketPairsAsync(CancellationToken stoppingToken)
    {
        // _logger.LogDebug("Updating market pairs from BTCTurk API");

        try
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var btcTurkService = scope.ServiceProvider.GetRequiredService<BtcTurkService>();

            // Get ticker data from BTCTurk API
            var tickerResponse = await btcTurkService.GetTickerAsync(stoppingToken);
            if (tickerResponse == null || !tickerResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to get ticker data from BTCTurk API: {ErrorMessage}",
                    tickerResponse?.GetErrorMessage() ?? "Unknown error");
                return;
            }

            // Update market pairs in the database
            await btcTurkService.UpdateBuySellPricesAsync(tickerResponse);

            // Record successful execution
            BackgroundServiceStatus.RecordSuccess(_serviceName);

            // _logger.LogInformation("Successfully updated market pairs from BTCTurk API at {Timestamp}", DateTime.UtcNow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating market pairs from BTCTurk API");

            // Record failure
            BackgroundServiceStatus.RecordFailure(_serviceName, ex.Message);

            throw; // Rethrow to be handled by the base class
        }
    }
}

using System.Collections.Concurrent;

namespace RazeWinComTr.BackgroundServices;

/// <summary>
/// Tracks the status of background services
/// </summary>
public static class BackgroundServiceStatus
{
    private static readonly ConcurrentDictionary<string, ServiceStatus> _serviceStatuses = new();

    /// <summary>
    /// Records a successful execution of a service
    /// </summary>
    /// <param name="serviceName">The name of the service</param>
    public static void RecordSuccess(string serviceName)
    {
        var status = _serviceStatuses.GetOrAdd(serviceName, _ => new ServiceStatus());
        status.LastSuccessfulRunTime = DateTime.UtcNow;
        status.SuccessCount++;
    }

    /// <summary>
    /// Records a failed execution of a service
    /// </summary>
    /// <param name="serviceName">The name of the service</param>
    /// <param name="errorMessage">The error message</param>
    public static void RecordFailure(string serviceName, string errorMessage)
    {
        var status = _serviceStatuses.GetOrAdd(serviceName, _ => new ServiceStatus());
        status.LastFailureTime = DateTime.UtcNow;
        status.FailureCount++;
        status.LastErrorMessage = errorMessage;
    }

    /// <summary>
    /// Gets the status of all services
    /// </summary>
    /// <returns>A dictionary of service names and their statuses</returns>
    public static IReadOnlyDictionary<string, ServiceStatus> GetAllStatuses()
    {
        return _serviceStatuses;
    }

    /// <summary>
    /// Gets the status of a specific service
    /// </summary>
    /// <param name="serviceName">The name of the service</param>
    /// <returns>The status of the service, or null if not found</returns>
    public static ServiceStatus? GetStatus(string serviceName)
    {
        return _serviceStatuses.TryGetValue(serviceName, out var status) ? status : null;
    }

    /// <summary>
    /// Represents the status of a background service
    /// </summary>
    public class ServiceStatus
    {
        /// <summary>
        /// The time of the last successful execution
        /// </summary>
        public DateTime? LastSuccessfulRunTime { get; set; }

        /// <summary>
        /// The time of the last failure
        /// </summary>
        public DateTime? LastFailureTime { get; set; }

        /// <summary>
        /// The number of successful executions
        /// </summary>
        public long SuccessCount { get; set; }

        /// <summary>
        /// The number of failed executions
        /// </summary>
        public long FailureCount { get; set; }

        /// <summary>
        /// The last error message
        /// </summary>
        public string? LastErrorMessage { get; set; }

        /// <summary>
        /// Gets the status of the service
        /// </summary>
        public string Status
        {
            get
            {
                if (!LastSuccessfulRunTime.HasValue && !LastFailureTime.HasValue)
                {
                    return "Not Started";
                }

                if (!LastSuccessfulRunTime.HasValue)
                {
                    return "Failed";
                }

                if (LastFailureTime.HasValue && LastFailureTime > LastSuccessfulRunTime)
                {
                    return "Failed";
                }

                return "Running";
            }
        }

        /// <summary>
        /// Gets the time elapsed since the last successful run
        /// </summary>
        public TimeSpan? TimeSinceLastSuccess
        {
            get
            {
                return LastSuccessfulRunTime.HasValue
                    ? DateTime.UtcNow - LastSuccessfulRunTime.Value
                    : null;
            }
        }
    }
}

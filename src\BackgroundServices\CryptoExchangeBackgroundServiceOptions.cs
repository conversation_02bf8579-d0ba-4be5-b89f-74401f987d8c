namespace RazeWinComTr.BackgroundServices;

/// <summary>
/// Configuration options for cryptocurrency exchange background services
/// </summary>
public class CryptoExchangeBackgroundServiceOptions
{
    /// <summary>
    /// The interval in seconds between updates
    /// </summary>
    public int UpdateInterval { get; set; } = 10;

    /// <summary>
    /// The threshold in seconds for considering price data as fresh
    /// </summary>
    public int PriceThresholdSeconds { get; set; } = 30;

    /// <summary>
    /// The default delay in seconds to wait after a rate limit error
    /// </summary>
    public int RetryAfterDefaultDelay { get; set; } = 60;
}

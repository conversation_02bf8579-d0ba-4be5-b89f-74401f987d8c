using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.BackgroundServices;

namespace RazeWinComTr.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize(Policy = "AdminPolicy")]
public class BackgroundServiceStatusController : ControllerBase
{
    [HttpGet]
    public ActionResult<Dictionary<string, BackgroundServiceStatusViewModel>> Get()
    {
        var serviceStatuses = BackgroundServiceStatus.GetAllStatuses();
        var viewModels = new Dictionary<string, BackgroundServiceStatusViewModel>();

        foreach (var status in serviceStatuses)
        {
            viewModels[status.Key] = new BackgroundServiceStatusViewModel
            {
                ServiceName = status.Key,
                Status = status.Value.Status,
                LastSuccessfulRunTime = status.Value.LastSuccessfulRunTime,
                LastFailureTime = status.Value.LastFailureTime,
                SuccessCount = status.Value.SuccessCount,
                FailureCount = status.Value.FailureCount,
                LastErrorMessage = status.Value.LastErrorMessage,
                TimeSinceLastSuccess = status.Value.TimeSinceLastSuccess
            };
        }

        return Ok(viewModels);
    }
}

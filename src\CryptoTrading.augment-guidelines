# Cryptocurrency Trading Guidelines

This file provides specific guidelines for working with the cryptocurrency trading functionality in the RazeWin platform.

## Overview

The RazeWin platform allows users to buy and sell cryptocurrencies. The system fetches real-time cryptocurrency prices from external exchanges (Bitexen and BTCTurk) and updates them periodically using background services.

## Key Components

### Markets

- **Market Table**: `MARKET` in the database
- **Market Model**: `Market.cs` in `Areas/Admin/DbModel`
- **Market Service**: `MarketService.cs` in `Areas/Admin/Services`

### Wallets

- **Wallet Table**: `WALLET` in the database
- **Wallet Model**: `Wallet.cs` in `Areas/Admin/DbModel`
- **Wallet Service**: `WalletService.cs` in `Areas/Admin/Services`
- **Wallet Interface**: `IWalletService.cs` in `Areas/Admin/Services/Interfaces`

### Trades

- **Trade Table**: `TRADE` in the database
- **Trade Model**: `Trade.cs` in `Areas/Admin/DbModel`
- **Trade Service**: `TradeService.cs` in `Areas/Admin/Services`
- **Trade Types**: `TradeType` enum in `Trade.cs`
  - `Buy = 0`
  - `Sell = 1`
  - `PackageBonus = 2`
  - `ReferralReward = 3`

### External Exchange Services

- **Bitexen Service**: `BitexenService.cs` in `Areas/Admin/Services`
- **BTCTurk Service**: `BtcTurkService.cs` in `Areas/Admin/Services`
- **Factory**: `CryptoExchangeServiceFactory.cs` in `Areas/Admin/Services`

### Background Services

- **Bitexen Background Service**: `BitexenBackgroundService.cs` in `BackgroundServices`
- **BTCTurk Background Service**: `BTCTurkBackgroundService.cs` in `BackgroundServices`
- **Base Class**: `CryptoExchangeBackgroundService.cs` in `BackgroundServices`

## Trading Process

1. **Price Updates**:
   - Background services fetch prices from external exchanges periodically
   - Prices are updated in the `MARKET` table
   - The `LastPriceUpdate` field in the `MARKET` table records when the price was last updated

2. **Buy Process**:
   - User selects a cryptocurrency and amount to buy
   - System calculates the cost based on the current buy price
   - System checks if the user has sufficient balance
   - If yes, the system:
     - Deducts the cost from the user's TRY balance
     - Adds the cryptocurrency to the user's wallet
     - Creates a trade record with `TradeType.Buy`

3. **Sell Process**:
   - User selects a cryptocurrency and amount to sell
   - System calculates the proceeds based on the current sell price
   - System checks if the user has sufficient cryptocurrency balance
   - If yes, the system:
     - Deducts the cryptocurrency from the user's wallet
     - Adds the proceeds to the user's TRY balance
     - Creates a trade record with `TradeType.Sell`

## RZW Token

The RZW token is a special cryptocurrency in the platform:

- It's used for referral rewards and package bonuses
- Its price is managed internally rather than fetched from external exchanges
- The `TokenPriceService` provides methods to get the RZW token price
- When retrieving RZW token information, use `TokenPriceService` instead of directly querying the Markets table

## Market Configuration

- Each market (cryptocurrency) has configuration settings:
  - `MinimumBuy`: Minimum amount that can be bought
  - `MaximumBuy`: Maximum amount that can be bought (often set to `decimal.MaxValue`)
  - `MinimumSell`: Minimum amount that can be sold
  - `MaximumSell`: Maximum amount that can be sold
  - `BuyPrice`: Current price for buying
  - `SellPrice`: Current price for selling
  - `IsActive`: Whether the market is active
  - `IsApi`: Whether the price is updated from an API
  - `ApiServiceName`: Name of the API service (Bitexen or BTCTurk)

## UI Components

- **Public Market Page**: `/Pages/Market.cshtml`
- **Coin List Component**: `/Views/Shared/Components/CoinList/Default.cshtml`
- **Admin Market Management**: `/Areas/Admin/Pages/Market/Index.cshtml`
- **User Wallet Page**: `/Areas/MyAccount/Pages/Wallet.cshtml`
- **User Trade History**: `/Areas/MyAccount/Pages/TradeHistory.cshtml`

## Important Rules

1. Always use the current price when calculating trade amounts
2. Always check if the user has sufficient balance before executing a trade
3. Always create a trade record for each transaction
4. Always update wallet balances atomically with trade creation
5. When displaying prices, use the appropriate number of decimal places
6. When formatting numbers, use 8 decimal places and trim trailing zeros
7. Don't use Math.floor() for rounding in financial calculations

## Best Practices

1. Use the `IWalletService` interface instead of directly accessing the `WalletService`
2. Use the `TokenPriceService` for RZW token price information
3. When displaying buy/sell prices, clearly indicate which is which
4. Include percentage selectors (25%, 50%, 75%, 100%) for selecting portions of available balance
5. When completing transactions, show a success message and wait for user confirmation before refreshing
6. Display '-' instead of '0 TL' when values are zero
7. Use `@Localizer["Currency_Symbol"]` instead of static 'TL' text for currency symbols
8. Method names for price retrieval should specify whether they return buy or sell prices for clarity
9. Properties named 'RzwPrice' should be renamed to 'RzwBuyPrice' to better indicate they represent buying prices

## Error Handling

1. When retrieving RZW token prices, the application should fail rather than use default values if an error occurs
2. Handle API errors gracefully and log them appropriately
3. Display user-friendly error messages when trades cannot be executed
4. Implement proper validation for trade amounts

## Testing

1. Test buy and sell functionality with various amounts
2. Test edge cases like minimum and maximum buy/sell amounts
3. Test with insufficient balance scenarios
4. Test with inactive markets
5. Test with price updates during trading

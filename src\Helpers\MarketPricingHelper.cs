using RazeWinComTr.Models;
using System.Text.Json;

namespace RazeWinComTr.Helpers
{
    public static class MarketPricingHelper
    {
        public static SpecialMarketPricing? GetSpecialMarketPricing(string coinCode, string? specialMarketJson)
        {
            if (string.IsNullOrEmpty(specialMarketJson))
                return null;

            try
            {
                var settings = JsonSerializer.Deserialize<SpecialMarketSettings>(specialMarketJson);
                if (settings?.Markets == null || !settings.Markets.ContainsKey(coinCode))
                    return null;

                return settings.Markets[coinCode];
            }
            catch
            {
                return null;
            }
        }

        public static decimal CalculatePercentage(decimal value, decimal percentage)
        {
            return value * percentage / 100;
        }

        public static (decimal buyPrice, decimal sellPrice) ApplySpecialPricing(
            decimal buyPrice, 
            decimal sellPrice, 
            SpecialMarketPricing? specialPricing)
        {
            if (specialPricing == null || string.IsNullOrEmpty(specialPricing.Type))
                return (buyPrice, sellPrice);

            switch (specialPricing.Type)
            {
                case "yuzde+": // Percentage increase
                    buyPrice += CalculatePercentage(buyPrice, specialPricing.Amount);
                    sellPrice += CalculatePercentage(sellPrice, specialPricing.Amount);
                    break;
                case "yuzde-": // Percentage decrease
                    buyPrice -= CalculatePercentage(buyPrice, specialPricing.Amount);
                    sellPrice -= CalculatePercentage(sellPrice, specialPricing.Amount);
                    break;
                case "topla": // Add fixed amount
                    buyPrice += specialPricing.Amount;
                    sellPrice += specialPricing.Amount;
                    break;
                case "cikar": // Subtract fixed amount
                    buyPrice -= specialPricing.Amount;
                    sellPrice -= specialPricing.Amount;
                    break;
                case "carp": // Multiply
                    buyPrice *= specialPricing.Amount;
                    sellPrice *= specialPricing.Amount;
                    break;
            }

            return (buyPrice, sellPrice);
        }
    }
}

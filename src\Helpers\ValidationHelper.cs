using System.Numerics;
using System.Text.RegularExpressions;

namespace RazeWinComTr.Helpers;

/// <summary>
/// Generic validation helper class for common validation operations
/// </summary>
public static class ValidationHelper
{
    /// <summary>
    /// Validates an IBAN (International Bank Account Number) using the mod-97 check algorithm
    /// </summary>
    /// <param name="iban">The IBAN to validate</param>
    /// <returns>True if the IBAN is valid, false otherwise</returns>
    public static bool IsValidIban(string? iban)
    {
        // Handle null or empty input
        if (string.IsNullOrWhiteSpace(iban))
            return false;

        // Clean and normalize the IBAN
        iban = iban.Replace(" ", "").ToUpperInvariant();

        // Basic length check
        if (iban.Length < 15 || iban.Length > 34)
            return false;

        // Country-specific length checks
        if (iban.StartsWith("TR") && iban.Length != 26)
            return false;

        // Check that the first two characters are letters
        if (!Regex.IsMatch(iban, "^[A-Z]{2}"))
            return false;

        // Check that characters 3 and 4 are digits
        if (!Regex.IsMatch(iban, "^[A-Z]{2}[0-9]{2}"))
            return false;

        try
        {
            // Rearrange the IBAN: move the first 4 characters to the end
            var rearranged = iban[4..] + iban[..4];

            // Convert letters to numbers (A=10, B=11, ..., Z=35)
            var converted = string.Concat(rearranged.Select(c =>
                char.IsLetter(c) ? (c - 'A' + 10).ToString() : c.ToString()));

            // Use BigInteger for mod 97 check to handle large numbers
            if (BigInteger.TryParse(converted, out BigInteger num))
            {
                return num % 97 == 1;
            }

            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// Formats an IBAN with spaces for better readability
    /// </summary>
    /// <param name="iban">The IBAN to format</param>
    /// <returns>The formatted IBAN with spaces every 4 characters</returns>
    public static string FormatIban(string? iban)
    {
        if (string.IsNullOrWhiteSpace(iban))
            return string.Empty;

        // Remove existing spaces and convert to uppercase
        iban = iban.Replace(" ", "").ToUpperInvariant();
        
        // Format with spaces every 4 characters
        var formattedIban = string.Empty;
        for (var i = 0; i < iban.Length; i++)
        {
            if (i > 0 && i % 4 == 0)
            {
                formattedIban += ' ';
            }
            formattedIban += iban[i];
        }

        return formattedIban;
    }

    /// <summary>
    /// Cleans an IBAN by removing spaces and converting to uppercase
    /// </summary>
    /// <param name="iban">The IBAN to clean</param>
    /// <returns>The cleaned IBAN</returns>
    public static string CleanIban(string? iban)
    {
        if (string.IsNullOrWhiteSpace(iban))
            return string.Empty;

        return iban.Replace(" ", "").ToUpperInvariant();
    }
}

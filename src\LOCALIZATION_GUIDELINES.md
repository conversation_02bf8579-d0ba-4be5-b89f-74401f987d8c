# Localization Guidelines for RazeWin Project

This document outlines the guidelines for localization in the RazeWin project. Following these guidelines ensures consistency across the application and makes it easier for AI tools like Augment Code to understand and modify the codebase.

## Language Resource Keys

### Naming Convention

1. **Language**: All resource keys must be in English, regardless of the target language.
2. **Format**: Use Pascal Case with spaces between words (e.g., "Total Amount Must Be Greater Than Zero").
3. **Characters**: Avoid using non-ASCII characters in resource keys.
4. **Length**: Keep resource keys concise but descriptive.

### Examples

✅ **Correct Examples**:
- `"Full Name"`
- `"Enter Your Full Name"`
- `"Phone Number Required"`
- `"Total Amount Must Be Greater Than Zero"`
- `"Insufficient Balance For Withdrawal"`

❌ **Incorrect Examples**:
- `"FullName"` (missing spaces)
- `"fullName"` (camel case, missing spaces)
- `"Adınız Soyadınız"` (non-English key)
- `"PHONE_NUMBER"` (underscore format)
- `"This is a very long resource key that could be more concise"` (too verbose)

## Usage in Code

### In Razor Views

```csharp
@Localizer["Resource Key"]
```

### In C# Code

```csharp
_localizer["Resource Key"]
```

### In JavaScript

```javascript
window.t["Resource Key"]
```

## Resource Files

- English resources are defined in `Areas/Admin/Resources/SharedResource.resx`
- Turkish resources are defined in `Areas/Admin/Resources/SharedResource.tr.resx`

## Adding New Resources

When adding new resources:

1. Always add the resource key to both language files
2. Use the same resource key in both files
3. Add a descriptive comment for the resource
4. Group related resources together

## Validation Messages

For validation messages, use consistent phrasing:

- Required fields: `"{Field Name} Required"`
- Invalid input: `"Please Enter Valid {Field Name}"`
- Length restrictions: `"{Field Name} Too Long"` or `"{Field Name} Too Short"`

## Notes for AI Tools

AI tools like Augment Code should:

1. Always convert Turkish resource keys to English following the above conventions
2. Maintain the same meaning when translating resource keys
3. Preserve the Pascal Case with spaces format
4. Update both .resx files when adding or modifying resource keys

## Augment Guidelines for Language Management

When working with language resources in the RazeWin project, Augment Code must follow these strict guidelines:

1. **Never invent commands**: Do not create or guess commands for the Language Manager tool. Always use the documented commands.

2. **PowerShell limitations**: Remember that PowerShell does not support the `&&` operator for command chaining. Execute commands separately.

3. **Language Manager usage**: Always use the Language Manager tool for checking and adding language keys. Never manually edit .resx files directly.

4. **Working directory awareness**: Always verify the current working directory before executing commands. The Language Manager tool must be run from its directory.

5. **Command verification**: Before suggesting a command, verify that it exists and is properly formatted according to the tool's documentation.

6. **Batch operations**: When adding multiple language keys, work in small batches and verify results after each batch.

7. **Error handling**: If a command fails, analyze the error message carefully and suggest appropriate corrections.

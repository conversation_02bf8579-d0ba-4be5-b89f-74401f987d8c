using System.Text.Json.Serialization;

namespace RazeWinComTr.Models
{
    public class CoinSingleResponse
    {
        [JsonPropertyName("durum")]
        public string Status { get; set; } = "error";

        [JsonPropertyName("data")]
        public CoinData? Data { get; set; }

        [JsonPropertyName("bakiye")]
        public decimal? Balance { get; set; }

        [JsonPropertyName("coin_bakiye")]
        public decimal? CoinBalance { get; set; }
    }

    public class CoinData
    {
        [JsonPropertyName("img")]
        public string ImageUrl { get; set; } = string.Empty;

        [JsonPropertyName("coin")]
        public string CoinCode { get; set; } = string.Empty;

        [JsonPropertyName("ad")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("alis")]
        public decimal BuyPrice { get; set; }

        [JsonPropertyName("satis")]
        public decimal SellPrice { get; set; }

        [Json<PERSON>ropertyName("basamak")]
        public int DecimalPlaces { get; set; }

        [JsonPropertyName("minimumAlis")]
        public decimal MinimumBuy { get; set; }

        [JsonPropertyName("maximumAlis")]
        public decimal MaximumBuy { get; set; }

        [JsonPropertyName("minimumSatis")]
        public decimal MinimumSell { get; set; }

        [JsonPropertyName("maximumSatis")]
        public decimal MaximumSell { get; set; }
    }
}

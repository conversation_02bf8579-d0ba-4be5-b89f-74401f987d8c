using System.ComponentModel.DataAnnotations;
using System.Globalization;

namespace RazeWinComTr.Models
{
    public class CoinTradeRequest
    {
        [Required]
        public string Coin { get; set; } = string.Empty;

        // Integer part of the amount
        [Required]
        public int MiktarTam { get; set; }

        // Fractional part of the amount (as string to preserve leading zeros)
        [Required]
        public string Miktar<PERSON>esir { get; set; } = string.Empty;

        /// <summary>
        /// Gets the combined decimal amount from the integer and fractional parts
        /// </summary>
        /// <returns>The combined decimal amount in invariant culture</returns>
        public decimal GetCombinedAmount()
        {
            // Combine the integer and fractional parts
            string combinedString = $"{MiktarTam}.{MiktarKesir}";

            // Parse using invariant culture
            if (decimal.TryParse(combinedString, NumberStyles.Any, CultureInfo.InvariantCulture, out decimal result))
            {
                return result;
            }

            // Fallback to just the integer part if parsing fails
            return MiktarTam;
        }
    }
}

@page
@model RazeWinComTr.Pages.AboutModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["About RazeWin"];
}

<div class="fw pageHeaderAll cover wow fadeInUp"
     style="background-image: url(/public/front/fxyatirim/assets/images/page-bg.jpg)">
    <div class="container">
        <div class="fw pageHeader">
            <ul class="sul">
                <li class="icon"><span class="iconX"><i class="flaticon-star"></i></span></li>
                <li class="title">
                    <h1 class="titleX">@Localizer["About RazeWin"]</h1>
                </li>
                <li class="desc">@Localizer["Learn more about our company"]</li>
            </ul>
        </div>
    </div>
</div>

<div class="aboutSection">
    <div class="container">
        <div class="aboutTabs">
            <div class="aboutTabsNav">
                <ul class="nav nav-tabs" id="aboutTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab" aria-controls="company" aria-selected="true">
                            <i class="flaticon-building"></i>
                            <span>@Localizer["Our Company"]</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="mission-tab" data-bs-toggle="tab" data-bs-target="#mission" type="button" role="tab" aria-controls="mission" aria-selected="false">
                            <i class="flaticon-target"></i>
                            <span>@Localizer["Mission & Vision"]</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="team-tab" data-bs-toggle="tab" data-bs-target="#team" type="button" role="tab" aria-controls="team" aria-selected="false">
                            <i class="flaticon-user-3"></i>
                            <span>@Localizer["Our Team"]</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">
                            <i class="flaticon-headset"></i>
                            <span>@Localizer["Contact Us"]</span>
                        </button>
                    </li>
                </ul>
            </div>

            <div class="aboutTabsContent">
                <div class="tab-content" id="aboutTabsContent">
                    <div class="tab-pane fade show active" id="company" role="tabpanel" aria-labelledby="company-tab">
                        <div class="aboutTabContent">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="aboutTabText">
                                        <h2>@Localizer["About RazeWin"]</h2>
                                        <p>
                                            Razewin: Seyahatten Ticaretin Geleceğine
                                        </p>
                                        <p>
                                            Razewin, blockchain teknolojisiyle güçlendirilmiş bir ekosistem sunarak hem seyahat hem de ticaret dünyasını yeniden şekillendiriyor. Razewin.com, RZW token destekli sosyal navigasyon sistemiyle kullanıcılarına ödüllü seyahat deneyimi sunarken, Razewin.com.tr bu ekosistemi bir adım öteye taşıyarak RZW tokenlerini ticaret ve yatırım fırsatlarına dönüştürüyor.
                                        </p>
                                        <p>
                                            Seyahati yalnızca bir yolculuk olmaktan çıkarıp kazançlı ve etkileşimli bir deneyime dönüştürmek; misyonumuz ise blockchain teknolojisini herkes için erişilebilir ve faydalı hale getirmektir.
                                            Razewin ile hem yolculuk yapın hem kazanın hem de kazançlarınızı ticarete dönüştürün!
                                        </p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="aboutTabImage">
                                        <img src="/images/about-section.png" alt="About RazeWin" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="mission" role="tabpanel" aria-labelledby="mission-tab">
                        <div class="aboutTabContent">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="aboutTabText">
                                        <h2>@Localizer["Our Mission & Vision"]</h2>
                                        <div class="missionBox">
                                            <h3>@Localizer["Vision"]</h3>
                                            <p>
                                                Razewin olarak, dijital dünyada yenilikçi çözümler sunarak bireylerin ve işletmelerin geleceğe daha güçlü adımlar atmasını sağlamak istiyoruz. Teknolojiyle finansı birleştirerek güvenilir, hızlı ve sürdürülebilir bir ekosistem oluşturmayı hedefliyoruz.
                                            </p>
                                        </div>
                                        <div class="missionBox">
                                            <h3>@Localizer["Mission"]</h3>
                                            <p>
                                                Kullanıcılarımıza en iyi deneyimi sunmak için sürekli gelişen ve kendini yenileyen bir teknoloji platformu oluşturuyoruz. Razewin Trade Borsası (www.razewin.com.tr) ile güvenli ve erişilebilir bir ticaret ortamı sağlarken, diğer dijital projelerimizle inovasyonu her alanda destekliyoruz.
                                                Razewin Holding ile Geleceği Şekillendirin!
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="aboutTabImage">
                                        <img src="/images/about-section.png" alt="Mission & Vision" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="team" role="tabpanel" aria-labelledby="team-tab">
                        <div class="aboutTabContent">
                            <h2>@Localizer["Our Team"]</h2>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="teamMember">
                                        <div class="teamMemberImage">
                                            <img src="/public/front/fxyatirim/assets/images/team1.jpg" alt="Team Member" />
                                        </div>
                                        <div class="teamMemberInfo">
                                            <h3>John Doe</h3>
                                            <p>CEO & Founder</p>
                                            <div class="teamSocial">
                                                <a href="#"><i class="fab fa-linkedin"></i></a>
                                                <a href="#"><i class="fab fa-twitter"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="teamMember">
                                        <div class="teamMemberImage">
                                            <img src="/public/front/fxyatirim/assets/images/team2.jpg" alt="Team Member" />
                                        </div>
                                        <div class="teamMemberInfo">
                                            <h3>Jane Smith</h3>
                                            <p>CTO</p>
                                            <div class="teamSocial">
                                                <a href="#"><i class="fab fa-linkedin"></i></a>
                                                <a href="#"><i class="fab fa-twitter"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="teamMember">
                                        <div class="teamMemberImage">
                                            <img src="/public/front/fxyatirim/assets/images/team3.jpg" alt="Team Member" />
                                        </div>
                                        <div class="teamMemberInfo">
                                            <h3>Michael Johnson</h3>
                                            <p>Head of Operations</p>
                                            <div class="teamSocial">
                                                <a href="#"><i class="fab fa-linkedin"></i></a>
                                                <a href="#"><i class="fab fa-twitter"></i></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                        <div class="aboutTabContent">
                            <h2>@Localizer["Contact Us"]</h2>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="contactInfo">
                                        <div class="contactItem">
                                            <i class="flaticon-placeholder"></i>
                                            <div>
                                                <h3>@Localizer["Address"]</h3>
                                                <p>123 Crypto Street, Istanbul, Turkey</p>
                                            </div>
                                        </div>
                                        <div class="contactItem">
                                            <i class="flaticon-telephone"></i>
                                            <div>
                                                <h3>@Localizer["Phone"]</h3>
                                                <p>+90 ************</p>
                                            </div>
                                        </div>
                                        <div class="contactItem">
                                            <i class="flaticon-email"></i>
                                            <div>
                                                <h3>@Localizer["Email"]</h3>
                                                <p><EMAIL></p>
                                            </div>
                                        </div>
                                        <div class="contactItem">
                                            <i class="flaticon-headset"></i>
                                            <div>
                                                <h3>@Localizer["Support"]</h3>
                                                <p><EMAIL></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="contactForm">
                                        <form>
                                            <div class="mb-3">
                                                <input type="text" class="form-control" placeholder="@Localizer["Your Name"]" required>
                                            </div>
                                            <div class="mb-3">
                                                <input type="email" class="form-control" placeholder="@Localizer["Your Email"]" required>
                                            </div>
                                            <div class="mb-3">
                                                <input type="text" class="form-control" placeholder="@Localizer["Subject"]">
                                            </div>
                                            <div class="mb-3">
                                                <textarea class="form-control" rows="5" placeholder="@Localizer["Your Message"]" required></textarea>
                                            </div>
                                            <button type="submit" class="btn contactSubmitBtn">@Localizer["Send Message"]</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .aboutSection {
        padding: 60px 0;
        background-color: #191919;
        color: #fff;
    }

    .aboutTabs {
        background-color: #252525;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .aboutTabsNav {
        background-color: #1e1e1e;
        padding: 0;
    }

    .aboutTabsNav .nav-tabs {
        border: none;
        display: flex;
        justify-content: space-between;
    }

    .aboutTabsNav .nav-item {
        flex: 1;
    }

    .aboutTabsNav .nav-link {
        border: none;
        border-radius: 0;
        padding: 20px;
        color: #ccc;
        text-align: center;
        transition: all 0.3s ease;
        background-color: transparent;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .aboutTabsNav .nav-link i {
        font-size: 24px;
        margin-bottom: 10px;
    }

    .aboutTabsNav .nav-link.active {
        background-color: #f7931a;
        color: #fff;
    }

    .aboutTabsContent {
        padding: 40px;
    }

    .aboutTabContent h2 {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 25px;
        color: #fff;
    }

    .aboutTabText p {
        font-size: 16px;
        line-height: 1.6;
        color: #ddd;
        margin-bottom: 20px;
    }

    .aboutTabImage {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    .aboutTabImage img {
        max-width: 100%;
        height: auto;
        border-radius: 10px;
    }

    .missionBox {
        background-color: #1e1e1e;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .missionBox h3 {
        font-size: 22px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #f7931a;
    }

    .teamMember {
        background-color: #1e1e1e;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 30px;
        transition: all 0.3s ease;
    }

    .teamMember:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    }

    .teamMemberImage img {
        width: 100%;
        height: auto;
    }

    .teamMemberInfo {
        padding: 20px;
        text-align: center;
    }

    .teamMemberInfo h3 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #fff;
    }

    .teamMemberInfo p {
        color: #f7931a;
        margin-bottom: 15px;
    }

    .teamSocial a {
        display: inline-block;
        width: 36px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        background-color: #252525;
        color: #fff;
        border-radius: 50%;
        margin: 0 5px;
        transition: all 0.3s ease;
    }

    .teamSocial a:hover {
        background-color: #f7931a;
    }

    .contactInfo {
        margin-bottom: 30px;
    }

    .contactItem {
        display: flex;
        align-items: flex-start;
        margin-bottom: 25px;
    }

    .contactItem i {
        font-size: 24px;
        color: #f7931a;
        margin-right: 15px;
        margin-top: 5px;
    }

    .contactItem h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #fff;
    }

    .contactItem p {
        color: #ddd;
    }

    .contactForm .form-control {
        background-color: #1e1e1e;
        border: 1px solid #333;
        color: #fff;
        padding: 12px 15px;
    }

    .contactForm .form-control:focus {
        border-color: #f7931a;
        box-shadow: none;
    }

    .contactSubmitBtn {
        background-color: #f7931a;
        color: #fff;
        border: none;
        padding: 12px 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .contactSubmitBtn:hover {
        background-color: #e67e00;
    }

    @@media (max-width: 768px) {
        .aboutTabsNav .nav-link {
            padding: 15px 10px;
        }

        .aboutTabsNav .nav-link i {
            font-size: 20px;
        }

        .aboutTabsNav .nav-link span {
            font-size: 12px;
        }

        .aboutTabsContent {
            padding: 20px;
        }

        .aboutTabImage {
            margin-top: 30px;
        }
    }
</style>

﻿@page
@model RazeWinComTr.Pages.ChartModel
@{
}

<style>
    .grafikk {
        width: 100%;
        height: 1080px;
    }
</style>
<div class="grafikk">
    <script type="text/javascript" src="../s3.tradingview.com/tv.js"></script>
    <script type="text/javascript">
          new TradingView.widget(
          {
          "autosize": true,
          "symbol": "BINANCE:BTCUSDTPERP",
          "timezone": "Etc/UTC",
          "theme": "dark",
          "style": "1",
          "locale": "tr",
          "toolbar_bg": "#f1f3f6",
          "enable_publishing": true,
          "withdateranges": true,
          "range": "YTD",
          "hide_side_toolbar": false,
          "allow_symbol_change": true,
          "watchlist": [
            "BINANCE:BTCUSDTPERP"
          ],
          "details": true,
          "hotlist": true,
          "calendar": true,
          "show_popup_button": true,
          "popup_width": "1000",
          "popup_height": "650",
          "container_id": "tradingview_491c7"
        }
          );
    </script>

</div>


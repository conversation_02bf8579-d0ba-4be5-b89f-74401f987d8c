﻿using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Caching.Hybrid;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Market;

namespace RazeWinComTr.Pages
{
    public class IndexModel : PageModel
    {
        private readonly IMarketService _marketService;
        private readonly HybridCache _cache;

        public IndexModel(IMarketService marketService, HybridCache cache)
        {
            _marketService = marketService;
            _cache = cache;
        }

        public IEnumerable<MarketViewModel>? Markets { get; set; }

        public async Task OnGetAsync(CancellationToken cancellationToken)
        {
            var entryOptions = new HybridCacheEntryOptions
            {
                Expiration = TimeSpan.FromSeconds(3),
                LocalCacheExpiration = TimeSpan.FromSeconds(3)
            };

            Markets = await _cache.GetOrCreateAsync(
                $"MarketListPAgeResponse",
                async cancelToken => await _marketService.GetListAsync(isActive: 1, cancelToken),
                entryOptions,
                cancellationToken: cancellationToken
            );
        }
    }
}

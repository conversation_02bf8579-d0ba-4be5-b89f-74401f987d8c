@page
@model RazeWinComTr.Pages.PackagesModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin
@using System.Text.Json
@using RazeWinComTr.Models
@inject IStringLocalizer<SharedResource> Localizer

@{
    ViewData["Title"] = Localizer["Referral Packages"];
}

@section Styles {
    <link rel="stylesheet" href="/site/pages/shared/packages.css">
}

<div class="fw innerBanner">
    <div class="container">
        <div class="fw innerBannerTitle">
            <h1>@Localizer["Referral Packages"]</h1>
            <ul>
                <li><a href="/">@Localizer["Home"]</a></li>
                <li>@Localizer["Referral Packages"]</li>
            </ul>
        </div>
    </div>
</div>

<div class="fw innerContent">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h2 class="text-center mb-4">@Localizer["Choose Your Referral Package"]</h2>
                        <p class="text-center mb-5">@Localizer["Join our referral program and start earning rewards by inviting others to RazeWin."]</p>

                        <div class="packages-container">
                            <div class="row">
                                @foreach (var packageWithRewards in Model.PackagesWithRewards)
                                {
                                    var package = packageWithRewards.Package;
                                    var benefits = !string.IsNullOrEmpty(package.Benefits)
                                    ? JsonSerializer.Deserialize<RazeWinComTr.Models.PackageBenefits>(package.Benefits)
                                    : new RazeWinComTr.Models.PackageBenefits { Features = new List<string>() };

                                    <div class="col-lg-6 col-md-6 mb-4">
                                        @{
                                            var isCurrentPackage = Model.CurrentUserPackage != null && Model.CurrentUserPackage.PackageId == package.Id;
                                            var isLowerPackage = Model.CurrentPackageOrder.HasValue && package.Order < Model.CurrentPackageOrder.Value;
                                            var isHigherPackage = Model.CurrentPackageOrder.HasValue && package.Order > Model.CurrentPackageOrder.Value;

                                            var packageDisplayModel = new RazeWinComTr.Models.PackageDisplayViewModel
                                            {
                                                Package = package,
                                                RzwBuyPrice = Model.RzwBuyPrice,
                                                CurrentUserPackage = Model.CurrentUserPackage,
                                                CurrentPackageOrder = Model.CurrentPackageOrder,
                                                IsCurrentPackage = isCurrentPackage,
                                                IsLowerPackage = isLowerPackage,
                                                IsHigherPackage = isHigherPackage,
                                                IsPublicPackagesPage = true,
                                                RewardPercentages = packageWithRewards.RewardPercentages
                                            };
                                        }

                                        <partial name="/Pages/Shared/_PackageDisplay.cshtml" model="packageDisplayModel" />
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="/site/pages/shared/packages.js"></script>
}

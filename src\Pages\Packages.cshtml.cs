using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Caching.Hybrid;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Package;

namespace RazeWinComTr.Pages
{
    public class PackagesModel(
        PackageService packageService,
        UserPackageService userPackageService,
        HybridCache cache,
        ITokenPriceService tokenPriceService) : PageModel
    {
        private readonly PackageService _packageService = packageService;
        private readonly UserPackageService _userPackageService = userPackageService;
        private readonly HybridCache _cache = cache;
        private readonly ITokenPriceService _tokenPriceService = tokenPriceService;

        public List<PackageWithRewardsViewModel> PackagesWithRewards { get; set; } = [];
        public UserPackageViewModel? CurrentUserPackage { get; set; }
        public int? CurrentPackageOrder { get; set; }
        public decimal RzwBuyPrice { get; set; }

        public async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
        {
            var entryOptions = new HybridCacheEntryOptions
            {
                Expiration = TimeSpan.FromMinutes(5),
                LocalCacheExpiration = TimeSpan.FromMinutes(5)
            };

            // Get packages with rewards in a single query
            PackagesWithRewards = await _cache.GetOrCreateAsync(
                "ActivePackagesWithRewardsList",
                async cancelToken => await _packageService.GetPackagesWithRewardsAsync(activeOnly: true),
                entryOptions,
                cancellationToken: cancellationToken
            );

            // Check if user is logged in and get their current package
            var userId = User.GetClaimUserId();
            if (userId.HasValue)
            {
                // Get user's current package
                var userPackages = await _userPackageService.GetUserPackagesAsync(userId.Value);
                CurrentUserPackage = userPackages.FirstOrDefault(p => p.Status == RazeWinComTr.Areas.Admin.DbModel.UserPackageStatus.Active); // Bu zaten memory'de, async gerekmiyor

                // Get the order of the current package (for package hierarchy comparison)
                if (CurrentUserPackage != null)
                {
                    var currentPackageWithRewards = PackagesWithRewards.FirstOrDefault(p => p.Package.Id == CurrentUserPackage.PackageId); // Bu zaten memory'de, async gerekmiyor
                    if (currentPackageWithRewards != null)
                    {
                        CurrentPackageOrder = currentPackageWithRewards.Package.Order;
                    }
                }
            }

            // Get current RZW buy price
            var rzwTokenInfo = await _tokenPriceService.GetRzwTokenInfoAsync();
            RzwBuyPrice = rzwTokenInfo.BuyPrice;

            return Page();
        }
    }
}

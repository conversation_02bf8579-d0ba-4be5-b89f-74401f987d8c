﻿@page
@model RazeWinComTr.Pages.RegisterModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer
@{
}
<link rel="stylesheet" href="~/css/ios-date-fix.css" />

<div class="container">
    <div class="loginBox wow fadeInUp" style="margin-top: 80px;">
        <div class="fw loginBoxDiv">
            <div class="fw loginBoxForm">
                <div class="fw loginPageTitle">
                    <ul class="sul">
                        <li class="img"><img src="public/image/GbErZcuBDF.png"></li>
                        <li class="title">@Localizer["Register"]</li>
                    </ul>
                </div>
                <!--.loginPageTitle-->
                <form method="post" asp-page-handler="OnPost">
                    <div asp-validation-summary="All" class="text-danger"></div>
                    <div class="fw loginPageForm">
                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox green BoxTop10">
                                <span class="icon"><i class="flaticon-arroba"></i></span>
                                <span class="title">@Localizer["Identity Number"]:</span>
                                <input asp-for="Input.IdentityNumber" class="loginFormText" placeholder="kimlik no" required />
                                <span asp-validation-for="Input.IdentityNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox green BoxTop10">
                                <span class="icon"><i class="flaticon-user-2"></i></span>
                                <span class="title">@Localizer["First Name"]</span>
                                <input asp-for="Input.Name" class="loginFormText" placeholder="Ad" required />
                                <span asp-validation-for="Input.Name" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox green BoxTop10">
                                <span class="icon"><i class="flaticon-user-2"></i></span>
                                <span class="title">@Localizer["Last Name"]</span>
                                <input asp-for="Input.Surname" class="loginFormText" placeholder="Soyad" required />
                                <span asp-validation-for="Input.Surname" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox green BoxTop10">
                                <span class="icon"><i class="flaticon-telephone-1"></i></span>
                                <span class="title">@Localizer["Phone Number"]:</span>
                                <input asp-for="Input.PhoneNumber" class="loginFormText" placeholder="Telefon" required />
                                <span asp-validation-for="Input.PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox green BoxTop10">
                                <span class="icon"><i class="flaticon-arroba"></i></span>
                                <span class="title">@Localizer["Email Address"]:</span>
                                <input asp-for="Input.Email" class="loginFormText" placeholder="Mail" required/>
                                <span asp-validation-for="Input.Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox green BoxTop10">
                                <span class="icon"><i class="flaticon-arroba"></i></span>
                                <span class="title">@Localizer["Birth Date"]:</span>
                                <input asp-for="Input.BirthDate" type="date" class="loginFormText" value="@(Model?.Input?.BirthDate.ToString("yyyy-MM-dd"))" required />
                                <span asp-validation-for="Input.BirthDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox green BoxTop10">
                                <span class="icon"><i class="flaticon-arroba"></i></span>
                                <span class="title">@Localizer["Password"]:</span>
                                <input asp-for="Input.Password" type="password" class="loginFormText" placeholder="*****" required />
                                <span asp-validation-for="Input.Password" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormTabBox green BoxTop10">
                                <span class="icon"><i class="flaticon-link"></i></span>
                                <span class="title">@Localizer["Referral Code"]:</span>
                                <input asp-for="Input.ReferralCode" class="loginFormText" placeholder="@Localizer["Optional"]" />
                                <span asp-validation-for="Input.ReferralCode" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageCheckbox">
                                <ul>
                                    <li class="loginPageCheckTabbox BoxTop10">
                                        <span class="checkbox-icon">
                                            <input type="checkbox" name="checkboxG1" id="checkboxG3" class="css-checkbox" required />
                                            <label for="checkboxG3" class="css-label green ckboxR"></label>
                                        </span>
                                        <span class="title">
                                            <a href="javascript:void(0);" onclick="$('#privacyAgreement').modal('show');">
                                                @Localizer["Privacy Agreement Read"]
                                            </a>
                                        </span>
                                    </li>
                                    <li class="loginPageCheckTabbox BoxTop10">
                                        <span class="checkbox-icon">
                                            <input type="checkbox" name="checkboxG2" id="checkboxG4" class="css-checkbox" required />
                                            <label for="checkboxG4" class="css-label green ckboxR"></label>
                                        </span>
                                        <span class="title">
                                            <a href="javascript:void(0);" onclick="$('#mainAgreement').modal('show');">
                                                @Localizer["Main Agreement Read"]
                                            </a>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="fw loginPageFormTab">
                            <div class="fw loginPageFormSubmit">
                                <button type="submit" class="simpleButton xLargeX">
                                    @Localizer["Create Account"] <i class="flaticon-arrow-pointing-to-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="mainAgreement" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog small-modal-dialog" role="document" style="margin-top: 75px;">
        <div class="fw smallModal">
            <span class="bigModalClose" data-dismiss="modal"><i class="flaticon-cancel"></i></span>
            <div class="fw smallModalTop">
                <ul class="sul">
                    <li class="icon"><i class="flaticon-info onlyBlue"></i></li>
                    <li class="title"> @Localizer["Main Agreement"] </li>
                    <li class="desc">
                        <strong>@Localizer["User Agreement Title"]</strong>
                        <br><br>
                        1. <strong>@Localizer["Service Description Title"]:</strong> @Localizer["Service Description Text"]
                        <br><br>
                        2. <strong>@Localizer["Account Creation Title"]:</strong> @Localizer["Account Creation Text"]
                        <br><br>
                        3. <strong>@Localizer["Security Title"]:</strong> @Localizer["Security Text"]
                        <br><br>
                        4. <strong>@Localizer["Transaction Risks Title"]:</strong> @Localizer["Transaction Risks Text"]
                        <br><br>
                        5. <strong>@Localizer["Fees Title"]:</strong> @Localizer["Fees Text"]
                        <br><br>
                        6. <strong>@Localizer["Referral Program Title"]:</strong> @Localizer["Referral Program Text"]
                        <br><br>
                        7. <strong>@Localizer["Legal Compliance Title"]:</strong> @Localizer["Legal Compliance Text"]
                        <br><br>
                        8. <strong>@Localizer["Service Changes Title"]:</strong> @Localizer["Service Changes Text"]
                        <br><br>
                        @Localizer["Agreement Acceptance Text"]
                        <br><br>
                        RAZEWIN
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="privacyAgreement" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog small-modal-dialog" role="document" style="margin-top: 75px;">
        <div class="fw smallModal">
            <span class="bigModalClose" data-dismiss="modal"><i class="flaticon-cancel"></i></span>
            <div class="fw smallModalTop">
                <ul class="sul">
                    <li class="icon"><i class="flaticon-info onlyBlue"></i></li>
                    <li class="title"> @Localizer["Privacy Agreement"] </li>
                    <li class="desc">
                        <strong>@Localizer["Privacy Agreement Title"]</strong>
                        <br><br>
                        1. <strong>@Localizer["Data Collection Title"]:</strong> @Localizer["Data Collection Text"]
                        <br><br>
                        2. <strong>@Localizer["Data Usage Title"]:</strong> @Localizer["Data Usage Text"]
                        <br><br>
                        3. <strong>@Localizer["Data Security Title"]:</strong> @Localizer["Data Security Text"]
                        <br><br>
                        4. <strong>@Localizer["Data Sharing Title"]:</strong> @Localizer["Data Sharing Text"]
                        <br><br>
                        5. <strong>@Localizer["Cookies And Tracking Title"]:</strong> @Localizer["Cookies And Tracking Text"]
                        <br><br>
                        6. <strong>@Localizer["Data Retention Title"]:</strong> @Localizer["Data Retention Text"]
                        <br><br>
                        7. <strong>@Localizer["Your Rights Title"]:</strong> @Localizer["Your Rights Text"]
                        <br><br>
                        8. <strong>@Localizer["Changes Title"]:</strong> @Localizer["Changes Text"]
                        <br><br>
                        @Localizer["Privacy Acceptance Text"]
                        <br><br>
                        RAZEWIN
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
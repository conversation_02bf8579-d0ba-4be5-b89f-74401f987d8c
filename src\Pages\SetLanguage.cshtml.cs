using System.Globalization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace RazeWinComTr.Pages;

[AllowAnonymous] // Allow anonymous access
public class SetLanguageModel : PageModel
{
    public IActionResult OnGet(string lang, string? returnUrl)
    {
        if (!string.IsNullOrEmpty(lang))
        {
            var cultureInfo = new CultureInfo(lang);

            // Set the culture for the current request
            var requestCulture = new RequestCulture(cultureInfo);
            CultureInfo.CurrentCulture = cultureInfo;
            CultureInfo.CurrentUICulture = cultureInfo;

            // Create a cookie with the selected language
            var cookieOptions = new CookieOptions
            {
                Expires = DateTimeOffset.UtcNow.AddYears(1),
                IsEssential = true,
                SameSite = SameSiteMode.Lax
            };
            var cookieKey = CookieRequestCultureProvider.DefaultCookieName;
            var cookieValue = CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(lang));
            Response.Cookies.Append(cookieKey, cookieValue, cookieOptions);
        }

        if (!string.IsNullOrEmpty(returnUrl))
            return Redirect(returnUrl);

        // After setting the language, redirect back to the appropriate home page based on the current area
        if (HttpContext.Request.Path.StartsWithSegments("/Admin"))
            return RedirectToPage("/Admin/Index");

        // Default to the main index page
        return RedirectToPage("/Index");
    }
}

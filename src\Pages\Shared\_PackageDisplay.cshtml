@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin
@using System.Text.Json
@using RazeWinComTr.Models
@inject IStringLocalizer<SharedResource> Localizer

@model RazeWinComTr.Models.PackageDisplayViewModel

<div class="package-card @(Model.IsCurrentPackage ? "current-package" : "")">
    <div class="package-header">
        @if (Model.IsCurrentPackage)
        {
            <span class="current-badge">@Localizer["Current"]</span>
        }
        <h3>@Model.Package.Name</h3>
        <div class="package-price">
            <span class="currency">₺</span>
            <span class="amount">@Model.Package.Price.ToString("N0")</span>
        </div>
    </div>
    <div class="package-body">
        <div class="package-limits">
            @if (Model.Package.InviteLimit.HasValue)
            {
                <p><strong>@Localizer["Invite Limit"]:</strong> @Model.Package.InviteLimit</p>
            }
            else
            {
                <p><strong>@Localizer["Invite Limit"]:</strong> @Localizer["Unlimited"]</p>
            }

            @if (Model.Package.EarningsCap.HasValue)
            {
                <p><strong>@Localizer["Earnings Cap"]:</strong> ₺@Model.Package.EarningsCap.Value.ToString("0.##")</p>
            }
            else
            {
                <p><strong>@Localizer["Earnings Cap"]:</strong> @Localizer["Unlimited"]</p>
            }

            <p class="rzw-bonus"><strong>@Localizer["Bonus"]:</strong> @((Model.Package.Price / Model.RzwBuyPrice).ToString("0.##")) RZW</p>

            @if (Model.RewardPercentages.Any())
            {
                <div class="reward-percentages">
                    <p>@Localizer["Referral Deposit Reward Percentages"]</p>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead>
                                <tr>
                                    <th>@Localizer["Level"]</th>
                                    <th>RZW</th>
                                    <th>TL</th>
                                    <th>@Localizer["Total"]</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var reward in Model.RewardPercentages.OrderBy(r => r.Level))
                                {
                                    <tr>
                                        <td>@reward.Level</td>
                                        <td>@(reward.RzwPercentage.ToString("0.##") + "%")</td>
                                        <td>@(reward.TlPercentage.ToString("0.##") + "%")</td>
                                        <td>@((reward.RzwPercentage + reward.TlPercentage).ToString("0.##") + "%")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            }
        </div>
    </div>
    <div class="package-footer">
        @if (Model.IsPublicPackagesPage && User.Identity?.IsAuthenticated != true) // Giriş yapmış kullanıcılar için (sadece public sayfada olabilir)
        {
            <a href="/login?returnUrl=/MyAccount/Packages" class="btn btn-secondary btn-block">@Localizer["Login to Purchase"]</a>
        }
        else // Giriş yapmış kullanıcılar için
        {
            if (Model.IsCurrentPackage)
            {
                <button class="btn btn-success btn-block" disabled>@Localizer["Current Package"]</button>
            }
            else if (Model.IsLowerPackage)
            {
                <button class="btn btn-secondary btn-block" disabled>@Localizer["Not Available"]</button>
            }
            else if (!Model.IsPublicPackagesPage && !Model.CanAfford) // Sadece MyAccount sayfasında bakiye kontrolü
            {
                <button class="btn btn-warning btn-block" disabled>@Localizer["Insufficient Balance"]</button>
            }
            else if (Model.IsHigherPackage)
            {
                <a href="/MyAccount/Packages?packageId=@Model.Package.Id" class="btn btn-primary btn-block">@Localizer["Upgrade"]</a>
            }
            else
            {
                <a href="/MyAccount/Packages?packageId=@Model.Package.Id" class="btn btn-primary btn-block">@Localizer["Purchase Now"]</a>
            }
        }
    </div>
</div>

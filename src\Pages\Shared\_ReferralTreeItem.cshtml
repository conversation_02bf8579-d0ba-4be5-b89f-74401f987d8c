@model RazeWinComTr.Areas.Admin.ViewModels.Referral.ReferralHierarchyItem
@using Microsoft.Extensions.Localization
@using RazeWinComTr.Areas.Admin.Enums
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer

<li>
    <span class="tree-item" data-level="@Model.Level">
        <i class="fas fa-user"></i> @Model.User.Name @Model.User.Surname
        <small class="text-muted ml-2">(@Model.User.Email)</small>
        <span class="badge badge-primary ml-2">@Localizer["Level"] @Model.Level</span>
        @if (Model.ActivePackage != null)
        {
            <span class="badge badge-success ml-2">@Model.ActivePackage.Name</span>
        }
        @if (Model.TotalDeposits > 0)
        {
            <span class="badge badge-info ml-2">
                <i class="fas fa-money-bill-wave"></i> @Model.TotalDeposits.ToString("N2") TL
            </span>
        }
        @if (Model.TotalRzwEarnings > 0)
        {
            <span class="badge badge-warning ml-2">
                <i class="fas fa-coins"></i> @Model.TotalRzwEarnings.ToString("N2") RZW
            </span>
        }
        @if (ViewData["ShowSearchButton"] != null && ViewData["ShowSearchButton"] is bool showSearchButton && showSearchButton && User.IsInRole(Roller.Admin.ToString()))
        {
            <a href="?SelectedUserId=@Model.User.UserId" class="btn btn-xs btn-outline-info ml-2">
                <i class="fas fa-search"></i>
            </a>
        }
    </span>
    @if (Model.Children.Any())
    {
        <ul>
            @foreach (var child in Model.Children)
            {
                @await Html.PartialAsync("_ReferralTreeItem", child, ViewData)
            }
        </ul>
    }
</li>
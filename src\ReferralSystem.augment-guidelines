# Referral System Guidelines

This file provides specific guidelines for working with the RazeWin referral system.

## Overview

The RazeWin platform includes a multi-level referral system that allows users to earn rewards when their referrals (and their referrals' referrals) make deposits. The system is based on packages that users can purchase, with each package enabling rewards from different levels of referrals.

## Key Components

### Packages

- **Package Types**: Bronze, Silver, Gold, Platinum
- **Package Table**: `PACKAGE` in the database
- **Package Model**: `Package.cs` in `Areas/Admin/DbModel`
- **Package Service**: `PackageService.cs` in `Areas/Admin/Services`

### User Packages

- **User Package Table**: `USER_PACKAGE` in the database
- **User Package Model**: `UserPackage.cs` in `Areas/Admin/DbModel`
- **User Package Service**: `UserPackageService.cs` in `Areas/Admin/Services`
- **User Package Status**: `UserPackageStatus` enum in `UserPackage.cs`
  - `Active = 1`
  - `Expired = 2`
  - `Cancelled = 3`

### Reward Percentages

- **Reward Percentage Table**: `PACKAGE_REWARD_PERCENTAGE` in the database
- **Reward Percentage Model**: `PackageRewardPercentage.cs` in `Areas/Admin/DbModel`
- **Reward Percentage Service**: `PackageRewardPercentageService.cs` in `Areas/Admin/Services`

### Referral Rewards

- **Referral Reward Table**: `REFERRAL_REWARD` in the database
- **Referral Reward Model**: `ReferralReward.cs` in `Areas/Admin/DbModel`
- **Referral Reward Service**: `ReferralRewardService.cs` in `Areas/Admin/Services`

## Package Levels and Reward Percentages

Each package type enables rewards from different levels of referrals:

1. **Bronze Package**:
   - Level 1: 5%
   - Maximum referral depth: 1 level

2. **Silver Package**:
   - Level 1: 10%
   - Level 2: 5%
   - Maximum referral depth: 2 levels

3. **Gold Package**:
   - Level 1: 15%
   - Level 2: 10%
   - Level 3: 5%
   - Maximum referral depth: 3 levels

4. **Platinum Package**:
   - Level 1: 20%
   - Level 2: 15%
   - Level 3: 10%
   - Level 4: 5%
   - Maximum referral depth: 4 levels

## Referral Process

1. **User Registration**:
   - When a user registers, they can enter a referral code
   - The referral code links the new user to their referrer
   - The `ReferrerId` field in the `USER` table stores this relationship

2. **Package Purchase**:
   - Users must purchase a package to participate in the referral program
   - When a user purchases a package, they receive RZW tokens equal to the package price
   - Previous packages are deactivated when a user purchases a new package
   - Package purchases are recorded in the `USER_PACKAGE` table

3. **Deposit Rewards**:
   - When a user makes a deposit, their referrers can earn rewards
   - Rewards are calculated based on the deposit amount and the referrer's package level
   - Rewards are distributed in RZW tokens
   - Reward distribution is handled by the `ReferralRewardService`

## Invite Limits

- Packages may have invite limits that restrict how many users can be referred
- The `InviteLimit` field in the `PACKAGE` table defines this limit
- The `ReferralService.HasReachedInviteLimitAsync` method checks if a user has reached their invite limit
- The `ReferralService.GetRemainingInvitesAsync` method returns the number of remaining invites

## Reward Distribution

The reward distribution process works as follows:

1. When a user makes a deposit, the system identifies their referral chain (up to 10 levels for safety)
2. For each referrer in the chain:
   - The system checks if the referrer has an active package
   - If yes, it retrieves the reward percentage for the referrer's package and the specific level
   - If a percentage is defined for that level, the reward is calculated as: `deposit_amount * (percentage / 100)`
   - The reward amount in TL is converted to RZW tokens based on the current RZW price
   - The reward is added to the referrer's RZW wallet
   - A record is created in the `REFERRAL_REWARD` table

## Important Rules

1. Only users with active packages can receive rewards
2. If no specific percentage is found for a referral level, no reward will be given for that level
3. Rewards are calculated based on the original deposit amount
4. Rewards are distributed in RZW tokens, not in TL
5. The system supports an unlimited number of referral levels, as defined in the package reward percentages
6. When a user purchases a new package, any previous packages are deactivated
7. Referral rewards are only given for deposits, not for package purchases
8. When a user purchases a package, they receive RZW tokens calculated based on the current price

## UI Components

- **Public Package Page**: `/Pages/Packages.cshtml`
- **User Package Page**: `/Areas/MyAccount/Pages/Packages.cshtml`
- **Admin Package Management**: `/Areas/Admin/Pages/Package/Index.cshtml`
- **Admin User Package Management**: `/Areas/Admin/Pages/UserPackage/Index.cshtml`
- **Admin Reward Percentage Management**: `/Areas/Admin/Pages/PackageRewardPercentage/Index.cshtml`
- **Admin Reward Distribution**: `/Areas/Admin/Pages/ReferralReward/DistributeRewards.cshtml`
- **Admin Reward Summary**: `/Areas/Admin/Pages/ReferralReward/RewardSummary.cshtml`

## Testing

- Complex test scenarios for the referral system are available in `src_unittests/Tests/Complex/ReferralRewardComplexTests.cs`
- These tests verify the behavior of the reward distribution system in various scenarios
- When testing the referral system, use the provided test infrastructure rather than creating new patterns

## Common Tasks

### Adding a New Package

1. Use the Admin Package Management page to create a new package
2. Define the package name, price, and other details
3. Set up the reward percentages for each level using the Admin Reward Percentage Management page

### Modifying Reward Percentages

1. Use the Admin Reward Percentage Management page to modify reward percentages
2. Ensure that percentages follow the established pattern for each package type
3. Avoid duplicate records for the same package and level

### Distributing Rewards

1. Use the Admin Reward Distribution page to distribute rewards for deposits
2. The page shows a preview of which users will receive rewards and at what rates
3. After distribution, view the results on the Admin Reward Summary page

## Best Practices

1. Always check if a user has an active package before calculating rewards
2. Use the `UserPackageStatus.Active` enum value when checking for active packages
3. When distributing referral rewards for package bonuses, use `TradeType.PackageBonus`
4. Calculate bonus RZW amount as package price divided by RZW buy price
5. When retrieving packages and their related data, use joined queries for better performance
6. Organize package reward percentages to keep each package and its percentages together
7. Use descriptive labels that explain reward percentages represent passive income
8. When formatting numbers, use 8 decimal places and trim trailing zeros
9. Don't use Math.floor() for rounding in financial calculations

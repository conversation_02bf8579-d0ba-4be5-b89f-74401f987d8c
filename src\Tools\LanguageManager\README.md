# Language Manager Tool

This tool helps you manage language resource files (.resx) in a multilingual application. It can add new language keys, verify consistency across language files, and report on the status of language files.

## Features

- Add new language keys to all resource files
- Add multiple language keys at once from a JSON file
- Verify that all resource files contain the same keys
- Synchronize all resource files to ensure they contain the same keys
- Generate a report on the status of language files
- Sort all language keys alphabetically
- Check if a specific language key exists in all resource files
- Check if multiple language keys from a file exist in all resource files
- Check for duplicate keys in resource files
- Remove duplicate keys from resource files

## Usage

### From Command Line

You can use the tool from the command line using the batch file:

```
# From the src/Tools directory
LanguageManager.bat -ResourceDir "../Areas/Admin/Resources" -Command <command> [options]
```

```
# From the src/Tools/LanguageManager directory
LanguageManager.bat -ResourceDir "../../Areas/Admin/Resources" -Command <command> [options]
```

Or directly with PowerShell:

```
powershell -ExecutionPolicy Bypass -File "LanguageManager.ps1" -ResourceDir "../../Areas/Admin/Resources" -Command <command> [options]
```

### Available Commands

- `add`: Add a new language key to all resource files
- `add-batch`: Add multiple language keys at once from a JSON file
- `verify`: Verify that all resource files contain the same keys
- `sync`: Synchronize all resource files to ensure they contain the same keys
- `report`: Generate a report on the status of language files
- `sort`: Sort all language keys alphabetically
- `check-key`: Check if a specific language key exists in all resource files
- `check-keys`: Check if multiple language keys from a file exist in all resource files
- `check-duplicates`: Check for duplicate keys in resource files
- `remove-duplicates`: Remove duplicate keys from resource files

### Examples

#### Add a new key

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command add -Key 'Welcome' -DefaultValue 'Welcome' -Comment 'Welcome message'
```

#### Add a new key with specific values for each language

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command add -Key 'Welcome' -DefaultValue 'Welcome' -Comment 'Welcome message'
```

Note: Adding values for specific languages directly from the command line is not supported in the batch file. Use a JSON file instead.

#### Add multiple keys from a JSON file

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command add-batch -KeysFile 'keys.json'
```

#### Verify that all resource files contain the same keys

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command verify
```

#### Synchronize all resource files

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command sync
```

#### Generate a report on the status of language files

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command report
```

#### Sort all language keys alphabetically

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command sort
```

#### Check if a specific language key exists in all resource files

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command check-key -Key 'Welcome Message'
```

#### Check if multiple language keys from a file exist in all resource files

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command check-keys -KeysFile 'keys_to_check.txt'
```

The keys file can be either:
- A simple text file with one key per line
- A JSON file with the same format as used for batch adding

This command will report which keys are missing from the resource files but will not add them automatically. You'll need to use the `add` command to add the missing keys.

#### Check for duplicate keys in resource files

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command check-duplicates
```

#### Remove duplicate keys from resource files

```
LanguageManager.bat -ResourceDir '../../Areas/Admin/Resources' -Command remove-duplicates
```

## JSON Format for Batch Adding

The JSON file for batch adding should have the following format:

```json
[
  {
    "key": "Welcome Message",
    "defaultValue": "Welcome",
    "values": {
      "en": "Welcome",
      "tr": "Hoş Geldiniz",
      "fr": "Bienvenue"
    },
    "comment": "Welcome message on homepage"
  },
  {
    "key": "Goodbye Message",
    "defaultValue": "Goodbye",
    "values": {
      "en": "Goodbye",
      "tr": "Hoşça Kalın",
      "fr": "Au revoir"
    },
    "comment": "Goodbye message"
  }
]
```

## Notes

- The tool supports any number of languages
- The tool ensures all language files contain the same keys
- The tool can be used from any location, not just from the project directory
- The tool can be integrated into your build process or CI/CD pipeline
- The tool properly handles Turkish characters and other special characters using UTF-8 encoding
- The tool does not sort language keys by default when adding them, use the `sort` command separately when needed
- **IMPORTANT**: Always delete temporary JSON files after adding language keys to avoid cluttering the repository
- **IMPORTANT**: Always use single quotes (') instead of double quotes (") in PowerShell commands to avoid issues with special characters and parameter parsing

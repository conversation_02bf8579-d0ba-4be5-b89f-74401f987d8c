using Microsoft.AspNetCore.Mvc;
using RazeWinComTr.Areas.Admin.ViewModels.Market;

namespace RazeWinComTr.ViewComponents
{
    public class CoinListViewComponent : ViewComponent
    {
        public Task<IViewComponentResult> InvokeAsync(IEnumerable<MarketViewModel> markets)
        {
            var model = new CoinListViewModel
            {
                ViewMarkets = markets
            };

            return Task.FromResult<IViewComponentResult>(View(model));
        }
    }

    public class CoinListViewModel
    {
        public IEnumerable<MarketViewModel> ViewMarkets { get; set; } = Array.Empty<MarketViewModel>();
    }
}
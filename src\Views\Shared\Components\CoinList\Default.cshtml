@model RazeWinComTr.ViewComponents.CoinListViewModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr
@using RazeWinComTr.Areas.Admin.DbModel
@using RazeWinComTr.Areas.Admin.Services
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> L

<table class="table coinlist">
    <thead>
        <tr>
            <th style="width:10%">Logo</th>
            <th style="width:20%">Coin</th>
            <th style="width:15%" class="d-none d-lg-table-cell text-right"><PERSON><PERSON>ş</th>
            <th style="width:15%" class="d-none d-lg-table-cell text-right">Satış</th>
            <th style="width:15%" class="d-none d-lg-table-cell text-right">24s Değ<PERSON>şim</th>
            <th style="min-width:240px" class="islemBtnTh"></th>
        </tr>
    </thead>
    <tbody>

        @foreach (var coin in Model.ViewMarkets)
        {
            <tr data-coin-id="@coin.Id">
                <td>
                    <img src="@FileService.getAnonymousFileUrl(nameof(Market), coin.Id)" class="tbl-icon" alt="@coin.PairCode">
                </td>
                <td>
                    <span class="tbl-currency">@coin.Name</span>
                    <span class="tbl-coin-abbrev">@coin.PairCode</span>
                </td>
                <td class="d-none d-lg-table-cell td-number">
                    <span class="tbl-price buyPrice">
                        @{
                            // Check if the buy price is 0
                            if (coin.BuyPrice == 0)
                            {
                                <span>-</span>
                            }
                            else
                            {
                                var buyPriceFormatted = string.IsNullOrEmpty(coin.BuyPriceFormatted)
                                    ? coin.BuyPrice.ToString()
                                    : coin.BuyPriceFormatted;

                                // Split the price into integer and decimal parts
                                var buyPriceParts = buyPriceFormatted.Split('.');
                                var buyPriceIntegerPart = buyPriceParts[0];
                                var buyPriceDecimalPart = buyPriceParts.Length > 1 ? "." + buyPriceParts[1] : "";

                                // Format the integer part with thousands separators
                                if (decimal.TryParse(buyPriceIntegerPart, out var buyPriceInteger))
                                {
                                    buyPriceIntegerPart = NumberFormatHelper.FormatDecimalWithThousandSeparator(buyPriceInteger, 0);
                                }

                                <span class="integer-part">@buyPriceIntegerPart</span><span class="decimal-part">@buyPriceDecimalPart</span> @L["Currency_Symbol"]
                            }
                        }
                    </span>
                </td>
                <td class="d-none d-lg-table-cell td-number">
                    <span class="tbl-price sellPrice">
                        @{
                            // Check if the sell price is 0
                            if (coin.SellPrice == 0)
                            {
                                <span>-</span>
                            }
                            else
                            {
                                var sellPriceFormatted = string.IsNullOrEmpty(coin.SellPriceFormatted)
                                    ? coin.SellPrice.ToString()
                                    : coin.SellPriceFormatted;

                                // Split the price into integer and decimal parts
                                var sellPriceParts = sellPriceFormatted.Split('.');
                                var sellPriceIntegerPart = sellPriceParts[0];
                                var sellPriceDecimalPart = sellPriceParts.Length > 1 ? "." + sellPriceParts[1] : "";

                                // Format the integer part with thousands separators
                                if (decimal.TryParse(sellPriceIntegerPart, out var sellPriceInteger))
                                {
                                    sellPriceIntegerPart = NumberFormatHelper.FormatDecimalWithThousandSeparator(sellPriceInteger, 0);
                                }

                                <span class="integer-part">@sellPriceIntegerPart</span><span class="decimal-part">@sellPriceDecimalPart</span> @L["Currency_Symbol"]
                            }
                        }
                    </span>
                </td>
                <td class="d-none d-lg-table-cell td-number"><span class="tbl-price change24h" style="color: @(coin.Change24h >= 0 ? "green" : "red");">@(coin.Change24h >= 0 ? "+" : "")@coin.Change24h%</span></td>
                <td>
                    @if (User.Identity != null && User.Identity.IsAuthenticated)
                    {
                        <button class="btn btn-al-btn  coinAlBtn" data-pairCode="@coin.PairCode">@L["Buy"]</button>
                        <button class="btn btn-sat-btn coinSatBtn" data-pairCode="@coin.PairCode">@L["Sell"]</button>
                    }
                    else
                    {
                        <button class="btn btn-al-btn  coinAlBtn" data-pairCode="@coin.PairCode" data-toggle="modal">@L["Buy"]</button>
                        <button class="btn btn-sat-btn coinSatBtn" data-pairCode="@coin.PairCode" data-toggle="modal">@L["Sell"]</button>
                    }
                </td>
            </tr>
        }

    </tbody>
</table>

<style>
    .coinlist {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

        .coinlist thead th {
            background-color: #333;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }

        .coinlist tbody tr {
            border-bottom: 1px solid #dee2e6;
            transition: background-color 0.2s;
        }

            .coinlist tbody tr:hover {
                background-color: #f8f9fa;
            }

        .coinlist td {
            padding: 12px;
            vertical-align: middle;
        }

        .coinlist tr .td-number {
            vertical-align: middle;
            text-align: right;
        }

    .tbl-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
    }

    .tbl-currency {
        display: block;
        font-weight: 600;
        /* color: #212529; */
    }

    .tbl-coin-abbrev {
        display: block;
        font-size: 0.85em;
        color: #6c757d;
    }

    .tbl-price {
        font-weight: 500;
    }

    .tbl-price .integer-part {
        font-weight: 600;
    }

    .tbl-price .decimal-part {
        font-size: 0.95em;
    }

    .btn-al-btn {
        background-color: #28a745;
        color: white;
        border: none;
        padding: 10px 10px;
        border-radius: 6px;
        margin-right: 3px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

        .btn-al-btn:hover {
            background-color: #218838;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

    .btn-sat-btn {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 10px 10px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

        .btn-sat-btn:hover {
            background-color: rgb(133, 18, 29);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

    /* Mobil görünüm için responsive tasarım */
    @@media (max-width: 992px) {
        .d-none.d-lg-table-cell {
            display: none;
        }
    }

    @@media (max-width: 767px) {
        .btn-al-btn, .btn-sat-btn {
            padding: 8px 8px;
            font-size: 14px;
            display: inline-block;
            width: auto;
            min-width: 90px;
        }

        .coinlist td {
            padding: 10px 5px;
        }
    }
</style>




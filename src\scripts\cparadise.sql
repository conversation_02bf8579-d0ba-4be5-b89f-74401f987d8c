-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Apr 11, 2025 at 05:25 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `cparadise`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin`
--

CREATE TABLE `admin` (
  `id` int(11) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `sifre` varchar(255) DEFAULT NULL,
  `durum` int(11) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `songiris` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ayarlar`
--

CREATE TABLE `ayarlar` (
  `id` int(11) NOT NULL,
  `mkey` varchar(255) NOT NULL COMMENT 'Ayar anahtarı',
  `mval` text DEFAULT NULL COMMENT 'Ayar değeri',
  `aciklama` varchar(255) DEFAULT NULL COMMENT 'Ayar açıklaması',
  `grup` varchar(50) DEFAULT 'genel' COMMENT 'Ayar grubu',
  `sira` int(11) DEFAULT 0 COMMENT 'Sıralama',
  `tarih` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `ayarlar`
--

INSERT INTO `ayarlar` (`id`, `mkey`, `mval`, `aciklama`, `grup`, `sira`, `tarih`) VALUES
(1, 'site_title', 'CParadise', 'Site Başlığı', 'genel', 1, '2025-04-09 11:35:04'),
(2, 'site_description', 'CParadise - Kripto Para Alım Satım Platformu', 'Site Açıklaması', 'genel', 2, '2025-04-09 11:35:04'),
(3, 'site_keywords', 'kripto,bitcoin,ethereum,altcoin,trading', 'Site Anahtar Kelimeleri', 'genel', 3, '2025-04-09 11:35:04'),
(4, 'site_email', '<EMAIL>', 'Site Email Adresi', 'genel', 4, '2025-04-09 11:35:04'),
(5, 'site_telefon', '+90 ************', 'Site Telefon', 'genel', 5, '2025-04-09 11:35:04'),
(6, 'site_adres', 'İstanbul, Türkiye', 'Site Adres', 'genel', 6, '2025-04-09 11:35:04'),
(7, 'facebook_url', 'https://facebook.com/cparadise', 'Facebook URL', 'sosyal', 10, '2025-04-09 11:35:04'),
(8, 'twitter_url', 'https://twitter.com/cparadise', 'Twitter URL', 'sosyal', 11, '2025-04-09 11:35:04'),
(9, 'instagram_url', 'https://instagram.com/cparadise', 'Instagram URL', 'sosyal', 12, '2025-04-09 11:35:04'),
(10, 'telegram_url', 'https://t.me/cparadise', 'Telegram URL', 'sosyal', 13, '2025-04-09 11:35:04'),
(11, 'api_key', '*********0', 'API Anahtarı', 'api', 20, '2025-04-09 11:35:04'),
(12, 'api_secret', 'abcdef123456', 'API Gizli Anahtarı', 'api', 21, '2025-04-09 11:35:04'),
(13, 'default_currency', 'TRY', 'Varsayılan Para Birimi', 'para', 30, '2025-04-09 11:35:04'),
(14, 'min_deposit', '100', 'Minimum Yatırma Tutarı', 'para', 31, '2025-04-09 11:35:04'),
(15, 'min_withdraw', '250', 'Minimum Çekme Tutarı', 'para', 32, '2025-04-09 11:35:04'),
(16, 'withdraw_commission', '1.5', 'Çekim Komisyonu (%)', 'para', 33, '2025-04-09 11:35:04'),
(17, 'login_attempts', '5', 'Maximum Giriş Denemesi', 'guvenlik', 40, '2025-04-09 11:35:04'),
(18, 'login_timeout', '15', 'Giriş Engelleme Süresi (Dakika)', 'guvenlik', 41, '2025-04-09 11:35:04'),
(19, 'maintenance_mode', '0', 'Bakım Modu (0: Kapalı, 1: Açık)', 'guvenlik', 42, '2025-04-09 11:35:04'),
(20, 'smtp_host', 'smtp.gmail.com', 'SMTP Sunucu', 'smtp', 50, '2025-04-09 11:35:04'),
(21, 'smtp_port', '587', 'SMTP Port', 'smtp', 51, '2025-04-09 11:35:04'),
(22, 'smtp_user', '<EMAIL>', 'SMTP Kullanıcı', 'smtp', 52, '2025-04-09 11:35:04'),
(23, 'smtp_pass', 'smtp_password', 'SMTP Şifre', 'smtp', 53, '2025-04-09 11:35:04'),
(24, 'smtp_crypto', 'tls', 'SMTP Güvenlik (tls/ssl)', 'smtp', 54, '2025-04-09 11:35:04'),
(25, 'site_theme', 'default', 'Site Teması', 'tema', 60, '2025-04-09 11:35:04'),
(26, 'admin_theme', 'default', 'Admin Panel Teması', 'tema', 61, '2025-04-09 11:35:04'),
(27, 'logo_dark', 'uploads/logo-dark.png', 'Koyu Logo', 'tema', 62, '2025-04-09 11:35:04'),
(28, 'logo_light', 'uploads/logo-light.png', 'Açık Logo', 'tema', 63, '2025-04-09 11:35:04'),
(29, 'favicon', 'uploads/favicon.ico', 'Favicon', 'tema', 64, '2025-04-09 11:35:04'),
(36, 'site_baslik', 'CParadise', 'Site Başlığı', 'genel', 1, '2025-04-09 11:36:35'),
(37, 'whatsappno', '**********', 'WhatsApp Numarası', 'iletisim', 10, '2025-04-09 11:37:09'),
(49, 'email', '<EMAIL>', 'İletişim Email', 'iletisim', 11, '2025-04-09 11:37:51'),
(50, 'telefon', '+90 ************', 'İletişim Telefon', 'iletisim', 12, '2025-04-09 11:37:51'),
(51, 'adres', 'İstanbul, Türkiye', 'İletişim Adres', 'iletisim', 13, '2025-04-09 11:37:51'),
(52, 'javascript', '', 'Javascript Kodları (TAWK vb.)', 'kod', 20, '2025-04-09 11:37:51'),
(83, 'papara_limit', 'Min: 100₺ - Max: 50.000₺', 'Papara Limit Bilgisi', 'odeme', 70, '2025-04-09 11:39:03'),
(84, 'papara_ad', 'John Doe', 'Papara Hesap Adı', 'odeme', 71, '2025-04-09 11:39:03'),
(85, 'papara_no', '*********0', 'Papara Numarası', 'odeme', 72, '2025-04-09 11:39:03'),
(86, 'papara_not', 'Lütfen açıklama kısmına kullanıcı ID\'nizi yazınız', 'Papara Not', 'odeme', 73, '2025-04-09 11:39:03'),
(87, 'papara_durum', '1', 'Papara Ödeme Durumu (0: Kapalı, 1: Açık)', 'odeme', 74, '2025-04-09 11:39:03'),
(88, 'pep_limit', 'Min: 1.500₺ - Max: 300.000₺', 'ETH ERC20 Limit Bilgisi', 'odeme', 80, '2025-04-09 11:39:03'),
(89, 'pep_not', 'İşlem süresi ortalama 10-30 dakikadır', 'ETH ERC20 Not', 'odeme', 81, '2025-04-09 11:39:03'),
(90, 'pep_durum', '1', 'ETH ERC20 Ödeme Durumu (0: Kapalı, 1: Açık)', 'odeme', 82, '2025-04-09 11:39:03'),
(100, 'pep_no', '0x*********0abcdef*********0abcdef12345678', 'ETH ERC20 Cüzdan Adresi', 'odeme', 81, '2025-04-09 11:40:14'),
(101, 'btc_limit', 'Min: 3.000₺ - Max: 750.000₺', 'Bitcoin Limit Bilgisi', 'odeme', 90, '2025-04-09 11:41:53'),
(102, 'btc_cuzdan', '******************************************', 'Bitcoin Cüzdan Adresi', 'odeme', 91, '2025-04-09 11:41:53'),
(103, 'btc_not', 'İşlem süresi ortalama 10-60 dakikadır', 'Bitcoin Not', 'odeme', 92, '2025-04-09 11:41:53'),
(104, 'btc_durum', '1', 'Bitcoin Ödeme Durumu (0: Kapalı, 1: Açık)', 'odeme', 93, '2025-04-09 11:41:53'),
(105, 'bankatransfer_limit', 'Min: 500₺ - Max: 100.000₺', 'Banka Transfer Limit Bilgisi', 'odeme', 100, '2025-04-09 11:41:53'),
(106, 'bankatransfer_not', 'Mesai saatleri içinde 5-15 dakika içinde işleminiz tamamlanır', 'Banka Transfer Not', 'odeme', 101, '2025-04-09 11:41:53'),
(107, 'bankatransfer_durum', '1', 'Banka Transfer Durumu (0: Kapalı, 1: Açık)', 'odeme', 102, '2025-04-09 11:41:53'),
(108, 'kredi_karti_limit', 'Min: 100₺ - Max: 50.000₺', 'Kredi Kartı Limit Bilgisi', 'odeme', 110, '2025-04-09 11:41:53'),
(109, 'kredi_karti_not', 'Kredi kartı ödemeleri anında hesabınıza yansır', 'Kredi Kartı Not', 'odeme', 111, '2025-04-09 11:41:53'),
(110, 'kredi_karti_durum', '1', 'Kredi Kartı Durumu (0: Kapalı, 1: Açık)', 'odeme', 112, '2025-04-09 11:41:53'),
(111, 'usdt_trc20_limit', 'Min: 50$ - Max: 100.000$', 'USDT TRC20 Limit Bilgisi', 'odeme', 120, '2025-04-09 11:41:53'),
(112, 'usdt_trc20_cuzdan', 'TRxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', 'USDT TRC20 Cüzdan Adresi', 'odeme', 121, '2025-04-09 11:41:53'),
(113, 'usdt_trc20_not', 'İşlem süresi ortalama 5-10 dakikadır', 'USDT TRC20 Not', 'odeme', 122, '2025-04-09 11:41:53'),
(114, 'usdt_trc20_durum', '1', 'USDT TRC20 Durumu (0: Kapalı, 1: Açık)', 'odeme', 123, '2025-04-09 11:41:53'),
(340, 'payfix_limit', 'Min: 100 USDT - Max: 50000 USDT', 'USDT TRC20 Limit Bilgisi', 'para', 34, '2025-04-09 11:46:33'),
(341, 'payfix_no', 'TRC20WALLETADDRESS', 'USDT TRC20 Cüzdan Adresi', 'para', 35, '2025-04-09 11:46:33'),
(342, 'payfix_not', 'Lütfen gönderim yaparken ağı doğru seçiniz (TRC20)', 'USDT TRC20 Uyarı Notu', 'para', 36, '2025-04-09 11:46:33'),
(343, 'payfix_durum', '1', 'USDT TRC20 Durum (0: Kapalı, 1: Açık)', 'para', 37, '2025-04-09 11:46:33');

-- --------------------------------------------------------

--
-- Table structure for table `bankalar`
--

CREATE TABLE `bankalar` (
  `id` int(11) NOT NULL,
  `baslik` varchar(100) NOT NULL COMMENT 'Banka adı',
  `adsoyad` varchar(100) NOT NULL COMMENT 'Hesap sahibinin adı soyadı',
  `iban` varchar(50) NOT NULL COMMENT 'IBAN numarası',
  `durum` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0: Pasif, 1: Aktif',
  `sira` int(11) NOT NULL DEFAULT 0 COMMENT 'Sıralama',
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bankalar`
--

INSERT INTO `bankalar` (`id`, `baslik`, `adsoyad`, `iban`, `durum`, `sira`, `created_at`, `updated_at`) VALUES
(1, 'Ziraat Bankası', 'Ahmet Yılmaz', 'TR33 0006 1005 1978 6457 8413 26', 1, 1, '2025-04-09 11:20:03', '2025-04-09 11:20:03'),
(2, 'Garanti Bankası', 'Mehmet Demir', 'TR66 0006 2005 1978 6457 8413 27', 1, 2, '2025-04-09 11:20:03', '2025-04-09 11:20:03'),
(3, 'İş Bankası', 'Ayşe Kaya', 'TR99 0006 4005 1978 6457 8413 28', 1, 3, '2025-04-09 11:20:03', '2025-04-09 11:20:03'),
(4, 'Yapı Kredi', 'Fatma Şahin', 'TR22 0006 7005 1978 6457 8413 29', 1, 4, '2025-04-09 11:20:03', '2025-04-09 11:20:03'),
(5, 'QNB Finansbank', 'Ali Öztürk', 'TR55 0006 1115 1978 6457 8413 30', 1, 5, '2025-04-09 11:20:03', '2025-04-09 11:20:03'),
(6, 'Akbank', 'Zeynep Çelik', 'TR88 0006 1225 1978 6457 8413 31', 1, 6, '2025-04-09 11:20:03', '2025-04-09 11:20:03'),
(7, 'Halkbank', 'Can Yıldız', 'TR11 0006 1335 1978 6457 8413 32', 0, 7, '2025-04-09 11:20:03', '2025-04-09 11:20:03'),
(8, 'Vakıfbank', 'Deniz Arslan', 'TR44 0006 1445 1978 6457 8413 33', 0, 8, '2025-04-09 11:20:03', '2025-04-09 11:20:03'),
(9, 'Ziraat Bankası VIP', 'Mustafa Aydın', 'TR77 0006 1555 1978 6457 8413 34', 1, 0, '2025-04-09 11:20:03', '2025-04-09 11:20:03'),
(10, 'Garanti Bankası VIP', 'Esra Yılmaz', 'TR99 0006 1665 1978 6457 8413 35', 1, 0, '2025-04-09 11:20:03', '2025-04-09 11:20:03');

-- --------------------------------------------------------

--
-- Table structure for table `cekim`
--

CREATE TABLE `cekim` (
  `id` int(11) NOT NULL,
  `uye_id` int(11) NOT NULL,
  `isimsoyisim` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `bakiye` decimal(10,2) NOT NULL,
  `cekimtalebi` decimal(10,2) NOT NULL,
  `ibanisim` varchar(100) NOT NULL,
  `iban` varchar(100) NOT NULL,
  `durum` tinyint(1) DEFAULT 0 COMMENT '0: Beklemede, 1: Onaylandı, 2: Reddedildi',
  `tarih` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cekim`
--

INSERT INTO `cekim` (`id`, `uye_id`, `isimsoyisim`, `email`, `bakiye`, `cekimtalebi`, `ibanisim`, `iban`, `durum`, `tarih`) VALUES
(1, 1, 'Ahmet Yılmaz', '<EMAIL>', 1000.00, 500.00, 'Ahmet Yılmaz', '**************************', 0, '2023-10-20 10:00:00'),
(2, 2, 'Mehmet Demir', '<EMAIL>', 2500.00, 1000.00, 'Mehmet Demir', '**************************', 0, '2023-10-20 11:30:00'),
(3, 3, 'Ayşe Kaya', '<EMAIL>', 3000.00, 1500.00, 'Ayşe Kaya', '**************************', 1, '2023-10-19 15:45:00'),
(4, 4, 'Fatma Şahin', '<EMAIL>', 5000.00, 2000.00, 'Fatma Şahin', '**************************', 1, '2023-10-19 16:20:00'),
(5, 5, 'Ali Öztürk', '<EMAIL>', 1500.00, 1500.00, 'Ali Öztürk', '**************************', 2, '2023-10-18 09:15:00'),
(6, 6, 'Zeynep Çelik', '<EMAIL>', 800.00, 900.00, 'Zeynep Çelik', '**************************', 2, '2023-10-18 14:40:00'),
(7, 7, 'Can Yıldız', '<EMAIL>', 4000.00, 2500.00, 'Can Yıldız', '1A1zP1eP5QGefi2DMPTfTL5SLmv7DivfNa', 0, '2023-10-20 13:25:00'),
(8, 8, 'Deniz Arslan', '<EMAIL>', 6000.00, 3000.00, 'Deniz Arslan', '0x742d35Cc6634C0532925a3b844Bc454e4438f44e', 0, '2023-10-20 14:10:00');

-- --------------------------------------------------------

--
-- Table structure for table `cuzdan`
--

CREATE TABLE `cuzdan` (
  `id` int(11) NOT NULL,
  `uye_id` int(11) NOT NULL COMMENT 'User ID',
  `coin_id` int(11) NOT NULL COMMENT 'Coin ID from market table',
  `bakiye` decimal(18,8) NOT NULL DEFAULT 0.******** COMMENT 'Balance',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `market`
--

CREATE TABLE `market` (
  `id` int(11) NOT NULL,
  `sira` int(11) NOT NULL DEFAULT 0 COMMENT 'Sıralama',
  `api_sembol` varchar(20) NOT NULL COMMENT 'API sembolü (örn: BTC/USDT)',
  `kod` varchar(20) DEFAULT NULL,
  `coin` varchar(20) DEFAULT NULL,
  `api` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0: Manuel, 1: Otomatik',
  `ad` varchar(50) NOT NULL COMMENT 'Coin adı',
  `kisaad` varchar(10) NOT NULL COMMENT 'Kısa adı (BTC, ETH vs)',
  `alis_kur` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Alış kuru',
  `satis_kur` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Satış kuru',
  `min_islem_miktar` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Minimum işlem miktarı',
  `max_islem_miktar` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Maximum işlem miktarı',
  `degisim24` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '24 saatlik değişim yüzdesi',
  `hacim_24h` decimal(20,2) NOT NULL DEFAULT 0.00 COMMENT '24 saatlik işlem hacmi',
  `genel_artis` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT 'Genel artış yüzdesi',
  `icon` varchar(255) DEFAULT NULL COMMENT 'Coin ikonu',
  `img` varchar(255) DEFAULT NULL,
  `durum` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0: Pasif, 1: Aktif',
  `son_guncelleme` datetime DEFAULT NULL COMMENT 'Son fiyat güncelleme tarihi',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `alis` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Alış fiyatı',
  `satis` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Satış fiyatı',
  `basamak` int(11) NOT NULL DEFAULT 2 COMMENT 'Ondalık basamak sayısı',
  `minimumAlis` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Minimum alış miktarı',
  `maximumAlis` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Maximum alış miktarı',
  `minimumSatis` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Minimum satış miktarı',
  `maximumSatis` decimal(20,8) NOT NULL DEFAULT 0.******** COMMENT 'Maximum satış miktarı',
  `coin_to` varchar(10) DEFAULT 'TRY'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `market`
--

INSERT INTO `market` (`id`, `sira`, `api_sembol`, `kod`, `coin`, `api`, `ad`, `kisaad`, `alis_kur`, `satis_kur`, `min_islem_miktar`, `max_islem_miktar`, `degisim24`, `hacim_24h`, `genel_artis`, `icon`, `img`, `durum`, `son_guncelleme`, `created_at`, `updated_at`, `alis`, `satis`, `basamak`, `minimumAlis`, `maximumAlis`, `minimumSatis`, `maximumSatis`, `coin_to`) VALUES
(1, 1, 'BTC/TRY', NULL, NULL, 0, 'Bitcoin', 'BTC', 850000.********, 852000.********, 0.00100000, 10.********, 2.35, 15000000.00, 12.50, 'assets/coins/btc.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(2, 2, 'ETH/TRY', NULL, NULL, 0, 'Ethereum', 'ETH', 45000.********, 45500.********, 0.01000000, 100.********, 1.75, 8500000.00, 8.25, 'assets/coins/eth.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(3, 3, 'USDT/TRY', NULL, NULL, 0, 'Tether', 'USDT', 28.********, 28.********, 10.********, 1000000.********, 0.15, 25000000.00, 0.50, 'assets/coins/usdt.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(4, 4, 'BNB/TRY', NULL, NULL, 0, 'Binance Coin', 'BNB', 4500.********, 4550.********, 0.10000000, 1000.********, -1.25, 3500000.00, 15.75, 'assets/coins/bnb.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(5, 5, 'XRP/TRY', NULL, NULL, 0, 'Ripple', 'XRP', 12.********, 12.********, 50.********, 100000.********, 3.45, 2500000.00, 5.25, 'assets/coins/xrp.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(6, 6, 'ADA/TRY', NULL, NULL, 0, 'Cardano', 'ADA', 8.75000000, 8.85000000, 100.********, 200000.********, -0.85, 1800000.00, -2.50, 'assets/coins/ada.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(7, 7, 'DOGE/TRY', NULL, NULL, 0, 'Dogecoin', 'DOGE', 1.85000000, 1.87000000, 500.********, 1000000.********, 5.25, 950000.00, 25.50, 'assets/coins/doge.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(8, 8, 'SOL/TRY', NULL, NULL, 0, 'Solana', 'SOL', 750.********, 755.********, 1.********, 5000.********, 4.15, 1200000.00, 18.75, 'assets/coins/sol.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(9, 9, 'DOT/TRY', NULL, NULL, 0, 'Polkadot', 'DOT', 95.********, 96.********, 5.********, 10000.********, -2.35, 850000.00, -5.50, 'assets/coins/dot.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(10, 10, 'AVAX/TRY', NULL, NULL, 0, 'Avalanche', 'AVAX', 350.********, 353.********, 2.********, 8000.********, 1.85, 750000.00, 9.25, 'assets/coins/avax.png', NULL, 1, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(11, 11, 'SHIB/TRY', NULL, NULL, 0, 'Shiba Inu', 'SHIB', 0.00075000, 0.00076000, 1000000.********, 1********000.********, -5.25, 450000.00, -15.50, 'assets/coins/shib.png', NULL, 0, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY'),
(12, 12, 'MATIC/TRY', NULL, NULL, 0, 'Polygon', 'MATIC', 18.********, 18.********, 50.********, 50000.********, -1.75, 650000.00, -3.25, 'assets/coins/matic.png', NULL, 0, '2023-10-20 23:55:00', '2025-04-09 11:26:19', '2025-04-09 11:26:19', 0.********, 0.********, 2, 0.********, 0.********, 0.********, 0.********, 'TRY');

-- --------------------------------------------------------

--
-- Table structure for table `odemeler`
--

CREATE TABLE `odemeler` (
  `id` int(11) NOT NULL,
  `uye_id` int(11) NOT NULL,
  `tip` varchar(50) NOT NULL COMMENT 'kredi_karti,bankatransfer,btc,payfix',
  `tutar` decimal(10,2) NOT NULL,
  `adsoyad` varchar(100) NOT NULL,
  `extra` text DEFAULT NULL COMMENT 'JSON formatında ekstra bilgiler',
  `ip` varchar(50) NOT NULL,
  `tarih` datetime NOT NULL,
  `islem` varchar(50) DEFAULT NULL COMMENT 'İşlem durumu',
  `sabit` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0: Beklemede, 1: Onaylandı, 2: Reddedildi',
  `isOnlineLastDate` datetime DEFAULT NULL COMMENT 'Son işlem tarihi'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `odemeler`
--

INSERT INTO `odemeler` (`id`, `uye_id`, `tip`, `tutar`, `adsoyad`, `extra`, `ip`, `tarih`, `islem`, `sabit`, `isOnlineLastDate`) VALUES
(1, 1, 'kredi_karti', 100.00, 'John Doe', '{\"cc_name\":\"John Doe\",\"cc_number\":\"****************\",\"cc_exp\":\"12/24\",\"cc_cvv\":\"123\"}', '127.0.0.1', '2023-10-20 10:00:00', 'otp_onayla', 0, '2023-10-20 10:01:00'),
(2, 2, 'bankatransfer', 250.00, 'Jane Smith', '{\"banka\":\"Garanti\",\"saat\":\"14:30\",\"telefon\":\"**********\",\"not\":\"Öğle saatlerinde EFT yapıldı\"}', '***********', '2023-10-20 14:30:00', NULL, 1, NULL),
(3, 3, 'btc', 500.00, 'Bob Wilson', '{}', '***********', '2023-10-20 15:45:00', NULL, 0, NULL),
(4, 4, 'payfix', 150.00, 'Alice Brown', '{\"payfixno\":\"*********\",\"telefon\":\"**********\"}', '***********', '2023-10-20 16:15:00', NULL, 0, NULL),
(5, 1, 'kredi_karti', 100.00, 'John Doe', '{\"cc_name\":\"John Doe\",\"cc_number\":\"****************\",\"cc_exp\":\"12/24\",\"cc_cvv\":\"123\"}', '***********', '2024-01-20 10:15:00', 'otp_gecersiz', 2, NULL),
(6, 2, 'payfix', 250.00, 'Jane Smith', '{\"payfixno\":\"PF123456\",\"telefon\":\"**********\"}', '***********', '2024-01-20 11:30:00', '', 2, NULL),
(7, 3, 'bankatransfer', 500.00, 'Alice Johnson', '{\"banka\":\"Garanti\",\"iban\":\"TR*********\"}', '***********', '2024-01-20 12:45:00', '', 2, NULL),
(8, 4, 'kredi_karti', 750.00, 'Bob Wilson', '{\"cc_name\":\"Bob Wilson\",\"cc_number\":\"****************\",\"cc_exp\":\"03/25\",\"cc_cvv\":\"321\"}', '***********', '2024-01-20 14:20:00', 'otp_gecersiz', 1, NULL),
(9, 5, 'payfix', 300.00, 'Carol Brown', '{\"payfixno\":\"PF789012\",\"telefon\":\"**********\"}', '***********', '2024-01-20 15:40:00', '', 2, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `trade`
--

CREATE TABLE `trade` (
  `id` int(11) NOT NULL,
  `type` enum('alis','satis') NOT NULL COMMENT 'İşlem tipi',
  `uye_id` int(11) NOT NULL COMMENT 'Üye ID',
  `coin_id` int(11) NOT NULL COMMENT 'Coin ID (market tablosundan)',
  `coin_kur` decimal(20,8) NOT NULL COMMENT 'İşlem anındaki coin kuru',
  `coinmiktar` decimal(20,8) NOT NULL COMMENT 'İşlem miktarı (coin)',
  `tlmiktar` decimal(20,2) NOT NULL COMMENT 'İşlem miktarı (TL)',
  `eskicoinbakiye` decimal(20,8) NOT NULL COMMENT 'İşlem öncesi coin bakiyesi',
  `yenicoinbakiye` decimal(20,8) NOT NULL COMMENT 'İşlem sonrası coin bakiyesi',
  `eskibakiye` decimal(20,2) NOT NULL COMMENT 'İşlem öncesi TL bakiyesi',
  `yenibakiye` decimal(20,2) NOT NULL COMMENT 'İşlem sonrası TL bakiyesi',
  `tarih` datetime NOT NULL DEFAULT current_timestamp(),
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0: İptal, 1: Aktif'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `trade`
--

INSERT INTO `trade` (`id`, `type`, `uye_id`, `coin_id`, `coin_kur`, `coinmiktar`, `tlmiktar`, `eskicoinbakiye`, `yenicoinbakiye`, `eskibakiye`, `yenibakiye`, `tarih`, `status`) VALUES
(1, 'alis', 1, 1, 850000.********, 0.05000000, 42500.00, 0.********, 0.05000000, 50000.00, 7500.00, '2023-10-20 10:15:00', 1),
(2, 'alis', 2, 1, 855000.********, 0.03500000, 29925.00, 0.15000000, 0.18500000, 35000.00, 5075.00, '2023-10-20 11:30:00', 1),
(3, 'alis', 3, 1, 848000.********, 0.10000000, 84800.00, 0.25000000, 0.35000000, 100000.00, 15200.00, '2023-10-20 14:45:00', 1),
(4, 'alis', 1, 2, 45000.********, 0.********, 22500.00, 1.********, 1.********, 25000.00, 2500.00, '2023-10-20 15:20:00', 1),
(5, 'alis', 4, 2, 46000.********, 0.25000000, 11500.00, 0.********, 0.25000000, 15000.00, 3500.00, '2023-10-20 16:10:00', 1),
(6, 'satis', 2, 1, 860000.********, 0.05000000, 43000.00, 0.18500000, 0.13500000, 5075.00, 48075.00, '2023-10-20 17:05:00', 1),
(7, 'satis', 3, 1, 862000.********, 0.15000000, 129300.00, 0.35000000, 0.20000000, 15200.00, 144500.00, '2023-10-20 18:30:00', 1),
(8, 'satis', 1, 2, 47000.********, 0.********, 23500.00, 1.********, 1.********, 2500.00, 26000.00, '2023-10-20 19:15:00', 1),
(9, 'satis', 4, 2, 47500.********, 0.10000000, 4750.00, 0.25000000, 0.15000000, 3500.00, 8250.00, '2023-10-20 20:00:00', 1),
(10, 'alis', 5, 3, 28.********, 1000.********, 28500.00, 500.********, 1500.********, 30000.00, 1500.00, '2023-10-20 21:10:00', 1),
(11, 'alis', 6, 3, 28.55000000, 500.********, 14275.00, 0.********, 500.********, 15000.00, 725.00, '2023-10-20 22:20:00', 1),
(12, 'satis', 5, 3, 28.60000000, 300.********, 8580.00, 1500.********, 1200.********, 1500.00, 10080.00, '2023-10-20 23:00:00', 1),
(13, 'satis', 6, 3, 28.********, 200.********, 5730.00, 500.********, 300.********, 725.00, 6455.00, '2023-10-20 23:30:00', 1);

-- --------------------------------------------------------

--
-- Table structure for table `uyeler`
--

CREATE TABLE `uyeler` (
  `id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `sifre` varchar(255) NOT NULL,
  `bakiye` decimal(20,2) NOT NULL DEFAULT 0.00,
  `adsoyad` varchar(255) DEFAULT NULL,
  `telefon` varchar(20) DEFAULT NULL,
  `tc` varchar(11) DEFAULT NULL,
  `iban` varchar(50) DEFAULT NULL,
  `ozel_market` text DEFAULT NULL COMMENT 'JSON formatında özel market ayarları',
  `durum` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1: Aktif, 0: Pasif',
  `tarih` datetime NOT NULL DEFAULT current_timestamp(),
  `son_giris` datetime DEFAULT NULL,
  `ip` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `uyeler`
--

INSERT INTO `uyeler` (`id`, `email`, `sifre`, `bakiye`, `adsoyad`, `telefon`, `tc`, `iban`, `ozel_market`, `durum`, `tarih`, `son_giris`, `ip`) VALUES
(1, '<EMAIL>', '123456', 1000.00, 'Test Kullanıcı', '**********', '*********01', '**************************', NULL, 1, '2025-04-09 11:30:12', NULL, '127.0.0.1'),
(3, '<EMAIL>', 'aa', 11.00, '', '', '', '', NULL, 2, '2025-04-09 16:43:32', NULL, NULL),
(4, '<EMAIL>', 'cc', 0.00, '', '', '', '', NULL, 1, '2025-04-09 17:02:11', NULL, NULL),
(5, '<EMAIL>', 'cc', 0.00, '', '', '', '', NULL, 1, '2025-04-09 17:02:25', NULL, NULL),
(6, '<EMAIL>', 'ccc', 0.00, '', '', '', '', NULL, 1, '2025-04-09 17:03:28', NULL, NULL),
(7, '<EMAIL>', 'ccc', 0.00, '', '', '', '', NULL, 1, '2025-04-09 17:04:35', NULL, NULL),
(8, '<EMAIL>', 'cccc', 3000.00, '', '', '', '', NULL, 1, '2025-04-09 17:08:06', NULL, NULL),
(9, '<EMAIL>', 'cccc', 0.00, '', '', '', '', NULL, 1, '2025-04-09 17:10:55', NULL, NULL),
(10, '<EMAIL>', '123123', 0.00, '22 22', '22222222222', '*********02', '01.101.1988', NULL, 1, '2025-04-11 13:07:50', NULL, '::1'),
(11, '<EMAIL>', '123123', 0.00, '22222 22222', '905554443322', '*********02', '01.101.1988', NULL, 1, '2025-04-11 13:10:52', NULL, '::1'),
(12, '<EMAIL>', '123123', 0.00, 'Seyit Onbaioşı', '************', '*********04', '********', NULL, 1, '2025-04-11 16:54:30', NULL, '::1'),
(13, '<EMAIL>', '123123', 0.00, 'kemal tekyollu', '************', '*********04', '01.10.1900', NULL, 1, '2025-04-11 17:48:40', NULL, '::1');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `ayarlar`
--
ALTER TABLE `ayarlar`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `mkey` (`mkey`),
  ADD KEY `grup` (`grup`),
  ADD KEY `sira` (`sira`);

--
-- Indexes for table `bankalar`
--
ALTER TABLE `bankalar`
  ADD PRIMARY KEY (`id`),
  ADD KEY `durum` (`durum`),
  ADD KEY `sira` (`sira`);

--
-- Indexes for table `cekim`
--
ALTER TABLE `cekim`
  ADD PRIMARY KEY (`id`),
  ADD KEY `uye_id` (`uye_id`),
  ADD KEY `durum` (`durum`),
  ADD KEY `tarih` (`tarih`);

--
-- Indexes for table `cuzdan`
--
ALTER TABLE `cuzdan`
  ADD PRIMARY KEY (`id`),
  ADD KEY `uye_id` (`uye_id`),
  ADD KEY `coin_id` (`coin_id`);

--
-- Indexes for table `market`
--
ALTER TABLE `market`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `api_sembol` (`api_sembol`),
  ADD KEY `sira` (`sira`),
  ADD KEY `durum` (`durum`),
  ADD KEY `kisaad` (`kisaad`);

--
-- Indexes for table `odemeler`
--
ALTER TABLE `odemeler`
  ADD PRIMARY KEY (`id`),
  ADD KEY `uye_id` (`uye_id`),
  ADD KEY `tip` (`tip`),
  ADD KEY `tarih` (`tarih`);

--
-- Indexes for table `trade`
--
ALTER TABLE `trade`
  ADD PRIMARY KEY (`id`),
  ADD KEY `uye_id` (`uye_id`),
  ADD KEY `coin_id` (`coin_id`),
  ADD KEY `type` (`type`),
  ADD KEY `tarih` (`tarih`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `uyeler`
--
ALTER TABLE `uyeler`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `durum` (`durum`),
  ADD KEY `tc` (`tc`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `ayarlar`
--
ALTER TABLE `ayarlar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=344;

--
-- AUTO_INCREMENT for table `bankalar`
--
ALTER TABLE `bankalar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `cekim`
--
ALTER TABLE `cekim`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `cuzdan`
--
ALTER TABLE `cuzdan`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `market`
--
ALTER TABLE `market`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `odemeler`
--
ALTER TABLE `odemeler`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `trade`
--
ALTER TABLE `trade`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `uyeler`
--
ALTER TABLE `uyeler`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

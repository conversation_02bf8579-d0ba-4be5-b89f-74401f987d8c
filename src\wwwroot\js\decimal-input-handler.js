/**
 * Decimal Input Handler
 * 
 * This script handles decimal input fields with comma as decimal separator.
 * It provides validation and formatting for decimal inputs.
 */

(function ($) {
    // Initialize decimal input fields
    function initDecimalInputs() {
        $('.decimal-input').each(function () {
            const $input = $(this);
            const maxDecimals = $input.data('decimal-places') || 8;
            
            // Set up input mask
            $input.on('input', function (e) {
                let value = $input.val();
                
                // Replace any dots with dots (for users who might type comma)
                value = value.replace(/\,/g, '.');
                
                // Remove any characters that are not digits or dot
                value = value.replace(/[^\d.]/g, '');
                
                // Ensure only one dot
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }
                
                // Limit decimal places
                if (parts.length > 1 && parts[1].length > maxDecimals) {
                    value = parts[0] + '.' + parts[1].substring(0, maxDecimals);
                }
                
                $input.val(value);
            });
            
            // On blur, format the number properly
            $input.on('blur', function () {
                let value = $input.val();
                
                // If empty, set to default value (usually 0)
                if (!value) {
                    $input.val('0');
                    return;
                }
                
                // If it ends with a comma, add a zero
                if (value.endsWith('.')) {
                    value += '0';
                }
                
                // If it's just a comma, set to 0
                if (value === '.') {
                    value = '0';
                }
                
                $input.val(value);
            });
        });
    }
    
    // Convert comma to dot before form submission
    function setupFormSubmission() {
        $('form').on('submit', function () {
            $('.decimal-input').each(function () {
                const $input = $(this);
                const value = $input.val();
                
                // Replace comma with dot for server-side processing
                if (value) {
                    const convertedValue = value.replace(/,/g, '.');
                    $input.val(convertedValue);
                }
            });
            
            return true;
        });
    }
    
    // Add custom validation method for decimal inputs
    $.validator.addMethod('decimalComma', function (value, element) {
        // Allow empty values for optional fields
        if (this.optional(element)) {
            return true;
        }
        
        // Check if the value matches the pattern for decimal with comma
        return /^-?\d+(?:,\d+)?$/.test(value);
    }, 'Please enter a valid number with comma as decimal separator (e.g., 123,45)');
    
    // Initialize when document is ready
    $(document).ready(function () {
        initDecimalInputs();
        setupFormSubmission();
    });
    
})(jQuery);

/**
 * Required Field Indicator
 * 
 * This script automatically adds an asterisk (*) in parentheses next to form field labels
 * when the associated input field has a required attribute or data-val-required attribute.
 * 
 * It works with both HTML5 required attribute and ASP.NET Core validation attributes.
 */
(function ($) {
    'use strict';

    /**
     * Add required field indicators to all form labels with required inputs
     */
    function addRequiredFieldIndicators() {
        // Find all inputs, selects, and textareas with required attribute
        $('input[required], select[required], textarea[required]').each(function () {
            addIndicatorToLabel($(this));
        });

        // Find all inputs with data-val-required attribute (ASP.NET validation)
        $('input[data-val-required], select[data-val-required], textarea[data-val-required]').each(function () {
            addIndicatorToLabel($(this));
        });

        // Find all inputs with LocalizedRequired attribute (custom validation)
        $('[data-val="true"]').each(function() {
            // Check if any of the data attributes contain "required"
            var isRequired = false;
            $.each(this.attributes, function() {
                if (this.name.indexOf('data-val-') === 0 && 
                    this.name.indexOf('required') !== -1) {
                    isRequired = true;
                    return false; // Break the loop
                }
            });
            
            if (isRequired) {
                addIndicatorToLabel($(this));
            }
        });
    }

    /**
     * Add indicator to the label associated with the input
     * @param {jQuery} $input - The input element
     */
    function addIndicatorToLabel($input) {
        // Get the associated label
        var id = $input.attr('id');
        if (!id) return;
        
        var $label = $('label[for="' + id + '"]');
        
        // Add the required indicator if not already present
        if ($label.length && !$label.find('.required-indicator').length) {
            $label.append(' <span class="required-indicator">(*)</span>');
        }
    }

    // Initialize when document is ready
    $(document).ready(function () {
        addRequiredFieldIndicators();
        
        // Also handle dynamically loaded content
        $(document).on('DOMNodeInserted', function(e) {
            var $element = $(e.target);
            if ($element.find('input, select, textarea').length > 0) {
                setTimeout(function() {
                    addRequiredFieldIndicators();
                }, 100);
            }
        });
    });

})(jQuery);

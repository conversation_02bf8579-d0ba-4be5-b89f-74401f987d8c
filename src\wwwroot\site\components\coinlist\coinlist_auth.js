﻿$(document).on('submit', '.kriptSatForm', function (e) {
    e.preventDefault();
    var form = $(this);

    // Get the amount value and split it into integer and fractional parts
    var miktarInput = form.find('input[name="miktar"]');
    if (miktarInput.length) {
        // Remove the original miktar input from the form data
        miktarInput.prop('disabled', true);

        // Use the values calculated in Sat_kontrol()
        var integerPart = kriptSatMiktarTam || 0;
        var fractionalPart = kriptSatMiktarKesir || '0';

        // Add fields for the split values
        form.append('<input type="hidden" name="MiktarTam" value="' + integerPart + '">');
        form.append('<input type="hidden" name="MiktarKesir" value="' + fractionalPart + '">');
    }

    $.ajax({
        url: "/api/ajax/coinsat",
        method: "POST",
        data: form.serialize(),
        dataType: 'JSON',
        success: function (data) {
            console.log(data);
            if (data.durum == 'success') {
                Swal.fire({
                    title: window.t["Success"] || 'Başarılı!',
                    text: window.t["Success"] || 'Satış emri başarılı',
                    icon: 'success',
                    confirmButtonText: window.t["OK"] || 'Tamam',
                    confirmButtonColor: '#28a745',
                    allowOutsideClick: false,
                    customClass: {
                        confirmButton: 'btn btn-success btn-lg',
                        popup: 'animated fadeInDown'
                    },
                    padding: '2em',
                    timer: 3000,
                    timerProgressBar: true
                }).then(function () {
                    window.location.href = '/MyAccount/TradeHistory';
                });
            } else {
                $('.kriptSatDurum').addClass('text-danger');
                $('.kriptSatDurum').html(data.mesaj);
            }
        }
    })

});
$(document).on('submit', '.kriptAlForm', function (e) {
    e.preventDefault();
    var form = $(this);

    // Get the amount value and split it into integer and fractional parts
    var miktarInput = form.find('input[name="miktar"]');
    if (miktarInput.length) {
        // Remove the original miktar input from the form data
        miktarInput.prop('disabled', true);

        // Use the values calculated in al_kontrol()
        var integerPart = kriptAlMiktarTam || 0;
        var fractionalPart = kriptAlMiktarKesir || '0';

        // Add fields for the split values
        form.append('<input type="hidden" name="MiktarTam" value="' + integerPart + '">');
        form.append('<input type="hidden" name="MiktarKesir" value="' + fractionalPart + '">');
    }

    $.ajax({
        url: "/api/ajax/coinal",
        method: "POST",
        data: form.serialize(),
        dataType: 'JSON',
        success: function (data) {
            console.log(data);
            if (data.durum == 'success') {
                Swal.fire({
                    title: window.t["Success"] || 'Başarılı!',
                    text: window.t["Success"] || 'Alış emri başarılı',
                    icon: 'success',
                    confirmButtonText: window.t["OK"] || 'Tamam',
                    confirmButtonColor: '#28a745',
                    allowOutsideClick: false,
                    customClass: {
                        confirmButton: 'btn btn-success btn-lg',
                        popup: 'animated fadeInDown'
                    },
                    padding: '2em',
                    timer: 3000,
                    timerProgressBar: true
                }).then(function () {
                    window.location.href = '/MyAccount/TradeHistory';
                });
            } else {
                $('.kriptAlDurum').addClass('text-danger');
                $('.kriptAlDurum').html(data.mesaj);
            }
        }
    })

});

var uye_CoinBakiye = 0;
var uye_Bakiye = 0;
var para_Img = 0;
var para_Kod = '';
var para_Baslik = "";
var para_AlisKur = 0;
var para_SatisKur = 0;
var para_Basamak = 4;
var para_MinimumAlis = "150.00";
var para_MaximumAlis = "300.00";
var para_MinimumSatis = "150.00";
var para_MaximumSatis = "300.00";

// Global variables for decimal part handling
var kriptAlMiktarTam = 0;
var kriptAlMiktarKesir = '0';
var kriptSatMiktarTam = 0;
var kriptSatMiktarKesir = '0';

// Variables to track open modals and their associated coin
var activeModal = null;
var activeCoinPairCode = '';

function getCoin(dataPairCode) {
    $.ajax({
        url: "/api/ajax/coinsingle",
        method: "POST",
        data: {
            kod: dataPairCode
        },
        dataType: 'JSON',
        success: function (data) {
            if (data.durum == 'success') {
                console.log(data.data);
                let coindata = data.data;

                uye_CoinBakiye = data.coin_bakiye;
                uye_Bakiye = data.bakiye;

                para_Img = coindata.img;
                para_Kod = coindata.coin;
                para_Baslik = coindata.ad;
                para_AlisKur = coindata.alis;
                para_SatisKur = coindata.satis;
                para_Basamak = coindata.basamak;
                para_MinimumAlis = coindata.minimumAlis;
                para_MaximumAlis = coindata.maximumAlis;
                para_MinimumSatis = coindata.minimumSatis;
                para_MaximumSatis = coindata.maximumSatis;

                $('.kriptAlForm .kriptImg').attr('src', para_Img);
                $('.kriptAlForm .kriptBaslik').html(para_Baslik);

                // Format buy price with thousands separators and trim trailing zeros
                $('.kriptAlForm .kriptKur').html(number_format(para_AlisKur, 8, '.', ',', true) + ' ' + window.t["Currency_Symbol"]);

                // Format user balance with thousands separators
                $('.kriptAlForm .kriptAlBakiye').html(number_format(uye_Bakiye, 8, '.', ',', true) + ' ' + window.t["Currency_Symbol"]);
                $('.kriptAlForm .kriptCoin').val(para_Kod);

                // Update Buy label with formatted price and trim trailing zeros
                $('.kriptAlForm small').html((window.t["Buy"] || 'Alış') + ': <span class="kriptKur">' + number_format(para_AlisKur, 8, '.', ',', true) + ' ' + window.t["Currency_Symbol"] + '</span>');

                // Update Available Balance text with formatted balance
                $('.kriptAlForm p').html((window.t["Available Balance"] || 'Kullanılabilir Bakiye') + ': <span class="font-weight-600 kriptAlBakiye">' + number_format(uye_Bakiye, 8, '.', ',', true) + ' ' + window.t["Currency_Symbol"] + '</span>');

                $('.kriptSatForm .kriptImg').attr('src', para_Img);
                $('.kriptSatForm .kriptBaslik').html(para_Baslik);

                // Format sell price with thousands separators and trim trailing zeros
                $('.kriptSatForm .kriptKur').html(number_format(para_SatisKur, 8, '.', ',', true) + ' ' + window.t["Currency_Symbol"]);

                // Format coin balance with thousands separator for integer part
                $('.kriptSatForm .kriptSatBakiye').html(number_format(uye_CoinBakiye, 8, '.', '.', true) + ' ' + para_Kod);

                // Update Sell label with formatted price and trim trailing zeros
                $('.kriptSatForm small').html((window.t["Sell"] || 'Satış') + ': <span class="kriptKur">' + number_format(para_SatisKur, 8, '.', ',', true) + ' ' + window.t["Currency_Symbol"] + '</span>');

                // Update Available Balance text with thousands separator for integer part
                $('.kriptSatForm p').html((window.t["Available Balance"] || 'Kullanılabilir Bakiye') + ': <span class="font-weight-600 kriptSatBakiye">' + number_format(uye_CoinBakiye, 8, '.', '.', true) + ' ' + para_Kod + '</span>');
                $('.kriptSatForm .kriptCoin').val(para_Kod);

                $('.kriptAlDurum').removeClass('text-danger');
                $('.kriptAlDurum').html('');
                $('.kriptSatDurum').removeClass('text-danger');
                $('.kriptSatDurum').html('');

                $('.kriptAlToplam').val("");
                $(".kriptSatToplam").val("");
                $('.kriptAlMiktar').val("");
                $(".kriptSatMiktar").val("");
            }
        }
    })
}


$(document).on('click', '.coinAlBtn', function (e) {
    console.log('Clicked button info:', e.target);

    e.preventDefault();
    var btn = $(this);
    var dataPairCode = btn.attr('data-pairCode');
    getCoin(dataPairCode);
    activeModal = 'coinAlModal';
    activeCoinPairCode = dataPairCode;
    $('#coinAlModal').modal('toggle');

});

// Clear active modal tracking when modal is closed
$('#coinAlModal').on('hidden.bs.modal', function () {
    activeModal = null;
    activeCoinPairCode = '';
});

$(document).on('click', '.coinSatBtn', function (e) {
    console.log('Clicked button info:', e.target);

    e.preventDefault();
    var btn = $(this);
    var dataPairCode = btn.attr('data-pairCode');
    getCoin(dataPairCode);
    activeModal = 'coinSatModal';
    activeCoinPairCode = dataPairCode;
    $('#coinSatModal').modal('toggle');

});

// Clear active modal tracking when modal is closed
$('#coinSatModal').on('hidden.bs.modal', function () {
    activeModal = null;
    activeCoinPairCode = '';
});

var kriptAlForm = $('.kriptAlForm');
var kriptAlBtn = $('.kriptAlBtn');
var kriptAlDurum = $('.kriptAlDurum');
var kriptAlToplam = 0;
var kriptAlMiktar = 0;
var _kriptAlToplam = 0;

$(".kriptAlMiktar").keyup(function () {
    // Get the current value
    let value = $(this).val();

    // Replace any dots with commas for consistent display
    if (value.indexOf(".") !== -1) {
        value = value.replace(/\./g, ",");
        $(this).val(value);
    }

    kriptAlMiktar = value;
    al_kontrol();
});

function al_kontrol() {
    // Convert comma to dot for calculation
    var miktarForCalc = kriptAlMiktar.toString().replace(/,/g, '.');

    // Split the value into integer and fractional parts for form submission
    var parts = kriptAlMiktar.toString().split(',');
    var integerPart = parseInt(parts[0] || '0', 10);
    var fractionalPart = parts.length > 1 ? parts[1] : '0';

    // Store the parts for later use in form submission
    kriptAlMiktarTam = integerPart;
    kriptAlMiktarKesir = fractionalPart;

    _kriptAlToplam = parseFloat(miktarForCalc) / para_AlisKur;
    kriptAlToplam = number_format(_kriptAlToplam, para_Basamak, '.', ',', true);

    var error = 0;
    if (isNaN(parseFloat(miktarForCalc))) {
        kriptAlDurum.addClass('text-danger');
        kriptAlDurum.html('<small>Lütfen geçerli bir miktar giriniz.</small>');
        error = 1;
    } else if (parseFloat(miktarForCalc) > parseFloat(uye_Bakiye)) {
        kriptAlDurum.addClass('text-danger');
        kriptAlDurum.html('<small>Kullanılabilir bakiyenizi aştınız.</small>');
        error = 1;
    } else if (parseFloat(_kriptAlToplam) < parseFloat(para_MinimumAlis)) {
        kriptAlDurum.addClass('text-danger');
        kriptAlDurum.html('<small>Minimum alınabilecek tutar: ' + para_MinimumAlis + ' ' + para_Kod + ' (' + (para_MinimumAlis * para_AlisKur) + ' ' + window.t["Currency_Symbol"] + ')</small>');
        error = 1;
    } else if (parseFloat(_kriptAlToplam) > parseFloat(para_MaximumAlis)) {
        kriptAlDurum.addClass('text-danger');
        kriptAlDurum.html('<small>Maksimum alınabilecek tutar: ' + para_MaximumAlis + ' ' + para_Kod + ' (' + (para_MaximumAlis * para_AlisKur) + ' ' + window.t["Currency_Symbol"] + ')</small>');
        error = 1;
    } else {
        kriptAlDurum.removeClass('text-danger');
        kriptAlDurum.html('');
    }

    $('.kriptAlToplam').val(kriptAlToplam + ' ' + para_Kod);

    kriptAlBtn.addClass('disabled');
    if (error == 0) {
        kriptAlBtn.removeClass('disabled');
        return true;
    }
    return false;
}

/************************************************************ */

var kriptSatForm = $('.kriptSatForm');
var kriptSatBtn = $('.kriptSatBtn');
var kriptSatDurum = $('.kriptSatDurum');
var kriptSatToplam = 0;
var kriptSatMiktar = 0;
var _kriptSatToplam = 0;


$(".kriptSatMiktar").keyup(function () {
    // Get the current value
    let value = $(this).val();

    // Replace any dots with commas
    if (value.indexOf(".") !== -1) {
        value = value.replace(/\./g, ",");
        $(this).val(value);
    }

    kriptSatMiktar = value;
    Sat_kontrol();
});

function Sat_kontrol() {
    kriptSatDurum.html('');

    // Convert comma to dot for calculation
    var miktarForCalc = kriptSatMiktar.toString().replace(/,/g, '.');

    // Split the value into integer and fractional parts for form submission
    var parts = kriptSatMiktar.toString().split(',');
    var integerPart = parseInt(parts[0] || '0', 10);
    var fractionalPart = parts.length > 1 ? parts[1] : '0';

    // Store the parts for later use in form submission
    kriptSatMiktarTam = integerPart;
    kriptSatMiktarKesir = fractionalPart;

    _kriptSatToplam = parseFloat(miktarForCalc) * para_SatisKur;
    kriptSatToplam = number_format(_kriptSatToplam, para_Basamak, '.', ',', true);

    var error = 0;
    if (isNaN(parseFloat(miktarForCalc))) {
        kriptSatDurum.addClass('text-danger');
        kriptSatDurum.html('<small>Lütfen geçerli bir miktar giriniz.</small>');
        error = 1;
    } else if (parseFloat(miktarForCalc) > parseFloat(uye_CoinBakiye)) {
        kriptSatDurum.addClass('text-danger');
        kriptSatDurum.html('<small>Kullanılabilir bakiyenizi aştınız.</small>');
        error = 1;
    } else if (parseFloat(miktarForCalc) < parseFloat(para_MinimumSatis)) {
        kriptSatDurum.addClass('text-danger');
        kriptSatDurum.html('<small>Minimum Satılabilecek tutar: ' + para_MinimumSatis + ' ' + para_Kod + ' (' + (para_MinimumSatis * para_SatisKur) + ' ' + window.t["Currency_Symbol"] + ')</small>');
        error = 1;
    } else if (parseFloat(miktarForCalc) > parseFloat(para_MaximumSatis)) {
        kriptSatDurum.addClass('text-danger');
        kriptSatDurum.html('<small>Maksimum Satılabilecek tutar: ' + para_MaximumSatis + ' ' + para_Kod + ' (' + (para_MaximumSatis * para_SatisKur) + ' ' + window.t["Currency_Symbol"] + ')</small>');
        error = 1;
    } else {
        kriptSatDurum.removeClass('text-danger');
        kriptSatDurum.html('');
    }

    $('.kriptSatToplam').val(kriptSatToplam + ' ' + window.t["Currency_Symbol"]);

    kriptSatBtn.addClass('disabled');
    if (error == 0) {
        kriptSatBtn.removeClass('disabled');
    }
}

function number_format(number, decimals, dec_point, thousands_sep, trimZeros = false) {
    // Strip all characters but numerical ones.
    number = (number + '').replace(/[^0-9+\-Ee.]/g, '');
    var n = !isFinite(+number) ? 0 : +number,
        prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
        sep = (typeof thousands_sep === 'undefined') ? ',' : thousands_sep,
        dec = (typeof dec_point === 'undefined') ? '.' : dec_point,
        s = '',
        toFixedFix = function (n, prec) {
            var k = Math.pow(10, prec);
            return '' + Math.round(n * k) / k;
        };
    // Fix for IE parseFloat(0.55).toFixed(0) = 0;
    s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.');

    // Always apply thousands separator to the integer part
    if (s[0].length > 3) {
        s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep);
    }

    if (trimZeros) {
        // Sondaki sıfırları kaldır
        if (s[1]) {
            s[1] = s[1].replace(/0+$/, '');
            if (s[1].length === 0) {
                return s[0]; // Eğer decimal kısım sadece 0'lardan oluşuyorsa, sadece tam sayı kısmını döndür
            }
        }
    } else {
        // Orijinal davranış: decimal kısmı belirtilen uzunluğa tamamla
        if ((s[1] || '').length < prec) {
            s[1] = s[1] || '';
            s[1] += new Array(prec - s[1].length + 1).join('0');
        }
    }
    return s.join(dec);
}

// Function to update modal prices when new data is available
function updateModalPrices(coinData) {
    if (!activeModal || !activeCoinPairCode) return;

    // Check if the coin in the modal matches the updated coin
    if (coinData.PairCode === activeCoinPairCode) {
        if (activeModal === 'coinAlModal') {
            // Update buy price in the modal
            var currentPrice = $('.kriptAlForm .kriptKur').text();
            // Format the price with thousands separators and trim trailing zeros
            var newPrice = number_format(coinData.BuyPrice, 8, '.', ',', true) + ' ' + window.t["Currency_Symbol"];

            if (currentPrice !== newPrice) {
                $('.kriptAlForm .kriptKur').text(newPrice);
                highlightElement($('.kriptAlForm .kriptKur'));

                // Update the global variable for calculations
                para_AlisKur = coinData.BuyPrice;

                // Recalculate if there's an amount entered
                if (kriptAlMiktar) {
                    al_kontrol();
                }
            }
        } else if (activeModal === 'coinSatModal') {
            // Update sell price in the modal
            var currentPrice = $('.kriptSatForm .kriptKur').text();
            // Format the price with thousands separators and trim trailing zeros
            var newPrice = number_format(coinData.SellPrice, 8, '.', ',', true) + ' ' + window.t["Currency_Symbol"];

            if (currentPrice !== newPrice) {
                $('.kriptSatForm .kriptKur').text(newPrice);
                highlightElement($('.kriptSatForm .kriptKur'));

                // Update the global variable for calculations
                para_SatisKur = coinData.SellPrice;

                // Recalculate if there's an amount entered
                if (kriptSatMiktar) {
                    Sat_kontrol();
                }
            }
        }
    }
}

// Function to highlight an element with animation
function highlightElement(element) {
    element.addClass('highlight');
    setTimeout(function () {
        element.removeClass('highlight');
    }, 500);
}

// Hook into the global coinlist_veriler function to update modal prices
if (typeof window.originalCoinlistVeriler === 'undefined') {
    window.originalCoinlistVeriler = window.coinlist_veriler;

    window.coinlist_veriler = function () {
        // Call the original function
        window.originalCoinlistVeriler();

        // Add our custom logic to update modal prices
        if (activeModal && activeCoinPairCode) {
            $.ajax({
                url: '/api/ajax/coinlist',
                type: 'GET',
                success: function (data) {
                    // Find the coin that matches our active coin
                    var activeCoin = data.find(function (coin) {
                        return coin.PairCode === activeCoinPairCode;
                    });

                    if (activeCoin) {
                        updateModalPrices(activeCoin);
                    }
                }
            });
        }
    };
}

// Quick Amount Selector for Buy Modal
$(document).on('click', '.kriptAlForm .quick-amount-btn', function() {
    // Remove active class from all buttons
    $('.kriptAlForm .quick-amount-btn').removeClass('active');

    // Add active class to clicked button
    $(this).addClass('active');

    // Get the percentage value
    var percentage = parseInt($(this).data('percentage'));

    // Calculate the amount based on the percentage of available balance
    var availableBalance = parseFloat(uye_Bakiye.toString().replace(/,/g, '.'));
    var amount = (availableBalance * percentage / 100);

    // Format the amount with proper formatting (no rounding)
    var formattedAmount = number_format(amount, 2, ',', '', true);

    // Update the input field
    $('.kriptAlMiktar').val(formattedAmount);

    // Update the global variable
    kriptAlMiktar = formattedAmount;

    // Trigger validation and calculation
    al_kontrol();
});

// Quick Amount Selector for Sell Modal
$(document).on('click', '.kriptSatForm .quick-amount-btn', function() {
    // Remove active class from all buttons
    $('.kriptSatForm .quick-amount-btn').removeClass('active');

    // Add active class to clicked button
    $(this).addClass('active');

    // Get the percentage value
    var percentage = parseInt($(this).data('percentage'));

    // Calculate the amount based on the percentage of available balance
    var availableBalance = parseFloat(uye_CoinBakiye.toString().replace(/,/g, '.'));
    var amount = (availableBalance * percentage / 100);

    // Format the amount with 8 decimal places for crypto
    var formattedAmount = number_format(amount, 8, ',', '', true);

    // Update the input field
    $('.kriptSatMiktar').val(formattedAmount);

    // Update the global variable
    kriptSatMiktar = formattedAmount;

    // Trigger validation and calculation
    Sat_kontrol();
});

// Clear active state when user manually edits the input
$(".kriptAlMiktar").on('input', function() {
    $('.kriptAlForm .quick-amount-btn').removeClass('active');
});

$(".kriptSatMiktar").on('input', function() {
    $('.kriptSatForm .quick-amount-btn').removeClass('active');
});

// Reset buttons when modals are opened
$('#coinAlModal').on('shown.bs.modal', function() {
    $('.kriptAlForm .quick-amount-btn').removeClass('active');
});

$('#coinSatModal').on('shown.bs.modal', function() {
    $('.kriptSatForm .quick-amount-btn').removeClass('active');
});

// Update form labels and buttons with translations when document is ready
$(document).ready(function () {
    // Update form labels
    $('.kriptAlForm .input-group-addon:first').text(window.t["Amount to spend"] + ' ' + window.t["Currency_Symbol"]);
    $('.kriptSatForm .input-group-addon:first').text(window.t["Amount to sell"] || 'Satılacak Miktar');

    // Update input placeholders
    $('.kriptAlMiktar').attr('placeholder', window.t["Enter amount to buy"] || 'Satın almak istediğiniz miktar');
    $('.kriptSatMiktar').attr('placeholder', window.t["Enter amount to sell"] || 'Satmak istediğiniz miktar');

    // Update "Amount to Buy/Sell" labels
    $('.kriptAlForm .input-group-addon:eq(1)').text(window.t["Amount to Buy"] || 'Alınacak Miktar');
    $('.kriptSatForm .input-group-addon:eq(1)').text(window.t["Amount to Receive"] + ' ' + window.t["Currency_Symbol"]);

    // Update available balance text
    $('.kriptAlForm p').html((window.t["Available Balance"] || 'Kullanılabilir Bakiye') + ': <span class="font-weight-600 kriptAlBakiye"></span>');
    $('.kriptSatForm p').html((window.t["Available Balance"] || 'Kullanılabilir Bakiye') + ': <span class="font-weight-600 kriptSatBakiye"></span>');

    // Update button texts
    $('.kriptAlBtn span').text(window.t["Submit Buy Order"] || 'ALIŞ EMRİ GİR');
    $('.kriptSatBtn span').text(window.t["Submit Sell Order"] || 'SATIŞ EMRİ GİR');

    // Update Buy/Sell labels
    $('.kriptAlForm small').html((window.t["Buy"] || 'Alış') + ': <span class="kriptKur"></span>');
    $('.kriptSatForm small').html((window.t["Sell"] || 'Satış') + ': <span class="kriptKur"></span>');
});
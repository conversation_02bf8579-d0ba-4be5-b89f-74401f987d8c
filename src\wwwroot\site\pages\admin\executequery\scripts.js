// Initialize CodeMirror for SQL syntax highlighting
document.addEventListener('DOMContentLoaded', function() {
    // Load additional CodeMirror addons for comment/uncomment functionality
    loadScript('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/addon/comment/comment.min.js');
    loadScript('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/addon/edit/matchbrackets.min.js');
    loadScript('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/addon/edit/closebrackets.min.js');
    loadScript('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/addon/selection/active-line.min.js');
    loadScript('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.15/addon/display/placeholder.min.js');

    // Function to load scripts dynamically
    function loadScript(url) {
        var script = document.createElement('script');
        script.src = url;
        document.head.appendChild(script);
    }

    var sqlEditor = CodeMirror.fromTextArea(document.getElementById('sqlEditor'), {
        mode: 'text/x-sql',
        theme: 'dracula',
        lineNumbers: true,
        indentWithTabs: true,
        smartIndent: true,
        lineWrapping: true,
        matchBrackets: true,
        autoCloseBrackets: true,
        styleActiveLine: true,
        autofocus: true,
        undoDepth: 100,
        historyEventDelay: 200,
        placeholder: "Enter your SQL query here...",
        extraKeys: {
            "Ctrl-Enter": function(cm) {
                executeQuery(false);
            },
            "Cmd-Enter": function(cm) {
                executeQuery(false);
            },
            "Ctrl-E": function(cm) {
                executeSelectedText();
            },
            "Cmd-E": function(cm) {
                executeSelectedText();
            },
            "Ctrl-/": "toggleComment",
            "Cmd-/": "toggleComment",
            "Ctrl-Z": function(cm) { cm.undo(); },
            "Cmd-Z": function(cm) { cm.undo(); },
            "Ctrl-Y": function(cm) { cm.redo(); },
            "Cmd-Y": function(cm) { cm.redo(); },
            "Cmd-Shift-Z": function(cm) { cm.redo(); },
            "Tab": function(cm) {
                if (cm.somethingSelected()) {
                    cm.indentSelection("add");
                } else {
                    cm.replaceSelection("    ", "end");
                }
            }
        }
    });

    var selectedTextInput = document.getElementById('selectedText');
    var executeSelectedBtn = document.getElementById('executeSelectedBtn');
    var executeAllBtn = document.getElementById('executeAllBtn');
    var form = document.querySelector('form');

    // Track selection changes
    sqlEditor.on('cursorActivity', function() {
        var selection = sqlEditor.getSelection();
        if (selection && selection.trim().length > 0) {
            executeSelectedBtn.disabled = false;
            executeSelectedBtn.title = 'Execute selected SQL (Ctrl+E / Cmd+E)';
        } else {
            executeSelectedBtn.disabled = true;
            executeSelectedBtn.title = 'Select text to execute';
        }
    });

    // Execute selected text
    function executeSelectedText() {
        var selection = sqlEditor.getSelection();
        if (selection && selection.trim().length > 0) {
            selectedTextInput.value = selection;
            form.submit();
        }
    }

    // Execute query (all or selected)
    function executeQuery(selectedOnly) {
        if (selectedOnly) {
            executeSelectedText();
        } else {
            selectedTextInput.value = ''; // Clear selected text to execute full query
            form.submit();
        }
    }

    // Clear button functionality
    document.getElementById('clearBtn').addEventListener('click', function() {
        sqlEditor.setValue('');
        sqlEditor.focus();
    });

    // Execute selected button
    executeSelectedBtn.addEventListener('click', function() {
        executeQuery(true);
    });

    // Execute all button
    executeAllBtn.addEventListener('click', function() {
        executeQuery(false);
    });

    // Add keyboard shortcut hints
    executeAllBtn.setAttribute('title', 'Execute all SQL (Ctrl+Enter / Cmd+Enter)');

    // Comment/Uncomment button
    document.getElementById('commentBtn').addEventListener('click', function() {
        var cm = sqlEditor;
        cm.toggleComment();
    });

    // Format SQL button
    document.getElementById('formatBtn').addEventListener('click', function() {
        formatSql();
    });

    // Undo button
    document.getElementById('undoBtn').addEventListener('click', function() {
        sqlEditor.undo();
    });

    // Redo button
    document.getElementById('redoBtn').addEventListener('click', function() {
        sqlEditor.redo();
    });

    // Format SQL function
    function formatSql() {
        var cm = sqlEditor;
        var selection = cm.getSelection();
        var textToFormat = selection || cm.getValue();

        try {
            // Simple SQL formatting - this is a basic implementation
            // For production, consider using a dedicated SQL formatter library
            var formatted = formatSqlBasic(textToFormat);

            if (selection) {
                cm.replaceSelection(formatted);
            } else {
                cm.setValue(formatted);
            }
        } catch (e) {
            console.error("Error formatting SQL:", e);
        }
    }

    // Basic SQL formatter
    function formatSqlBasic(sql) {
        // Replace multiple spaces with a single space
        sql = sql.replace(/\s+/g, ' ');

        // Add newlines after specific SQL keywords
        var keywords = ['SELECT', 'FROM', 'WHERE', 'GROUP BY', 'HAVING', 'ORDER BY', 'LIMIT',
                        'INSERT INTO', 'VALUES', 'UPDATE', 'SET', 'DELETE FROM', 'CREATE TABLE',
                        'ALTER TABLE', 'DROP TABLE', 'JOIN', 'LEFT JOIN', 'RIGHT JOIN', 'INNER JOIN',
                        'OUTER JOIN', 'UNION', 'UNION ALL'];

        // Case insensitive replacement for each keyword
        keywords.forEach(function(keyword) {
            var regex = new RegExp('\\b' + keyword + '\\b', 'gi');
            sql = sql.replace(regex, '\n' + keyword.toUpperCase());
        });

        // Add indentation for lines after the first line
        var lines = sql.trim().split('\n');
        for (var i = 1; i < lines.length; i++) {
            lines[i] = '    ' + lines[i];
        }

        return lines.join('\n');
    }

    // Adjust height based on content
    function adjustEditorHeight() {
        var windowHeight = window.innerHeight;
        var editorHeight = Math.max(150, Math.min(300, windowHeight * 0.3));
        sqlEditor.setSize(null, editorHeight);
    }

    adjustEditorHeight();
    window.addEventListener('resize', adjustEditorHeight);

    // Handle compact mode toggle for results table
    var compactModeSwitch = document.getElementById('compactModeSwitch');
    if (compactModeSwitch) {
        compactModeSwitch.addEventListener('change', function() {
            var resultsTable = document.getElementById('resultsTable');
            if (resultsTable) {
                if (this.checked) {
                    resultsTable.classList.add('table-compact');
                } else {
                    resultsTable.classList.remove('table-compact');
                }
            }
        });
    }

    // Keyboard shortcuts help modal
    var helpBtn = document.getElementById('helpBtn');
    if (helpBtn) {
        helpBtn.addEventListener('click', function() {
            var modal = new bootstrap.Modal(document.getElementById('keyboardShortcutsModal'));
            modal.show();
        });
    }
});

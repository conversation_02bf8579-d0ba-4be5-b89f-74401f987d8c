body {
    background-color: #1e1e2d;
    color: #e4e6ef;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding: 0;
    margin: 0;
    min-height: 100vh;
}

.navbar {
    background-color: #151521;
    border-bottom: 1px solid #2b2b40;
}

.main-container {
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px);
}

.query-container {
    margin-bottom: 20px;
    flex: 0 0 auto;
}

.results-container {
    flex: 1 1 auto;
    overflow: auto;
    background-color: #151521;
    border-radius: 8px;
    padding: 15px;
    position: relative;
}

.CodeMirror {
    height: 200px;
    border-radius: 8px;
    font-family: 'Consolas', 'Courier New', monospace;
    font-size: 14px;
}

.btn-execute {
    background-color: #0bb783;
    border-color: #0bb783;
    color: white;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 6px;
    transition: all 0.3s;
}

.btn-execute:hover {
    background-color: #09a173;
    border-color: #09a173;
}

.btn-clear {
    background-color: #3699ff;
    border-color: #3699ff;
    color: white;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 6px;
    transition: all 0.3s;
}

.btn-clear:hover {
    background-color: #187de4;
    border-color: #187de4;
}

.btn-info {
    background-color: #8950fc;
    border-color: #8950fc;
    color: white;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 6px;
    transition: all 0.3s;
}

.btn-info:hover {
    background-color: #7337ee;
    border-color: #7337ee;
    color: white;
}

.btn-info:disabled {
    background-color: #8950fc;
    border-color: #8950fc;
    opacity: 0.5;
    color: white;
}

.badge.bg-info {
    background-color: #8950fc !important;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.35em 0.65em;
}

.alert {
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.table {
    color: #e4e6ef;
    border-color: #2b2b40;
}

.table thead th {
    background-color: #1a1a27;
    border-color: #2b2b40;
    color: #3699ff;
    font-weight: 600;
    padding: 12px;
}

.table tbody td {
    border-color: #2b2b40;
    padding: 12px;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(42, 42, 60, 0.5);
}

.table-hover tbody tr:hover {
    background-color: rgba(54, 153, 255, 0.1);
}

.execution-info {
    font-size: 14px;
    color: #92929f;
    margin-bottom: 15px;
}

.no-results {
    text-align: center;
    padding: 50px;
    color: #92929f;
}

.table-responsive {
    max-height: calc(100vh - 400px);
    overflow-x: auto;
}

/* Compact table for large datasets */
.table-compact th,
.table-compact td {
    padding: 4px 8px;
    font-size: 0.85rem;
    white-space: nowrap;
}

.table-compact th {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* Editor toolbar */
.editor-toolbar .btn-outline-light {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
    background-color: transparent;
    transition: all 0.2s;
}

.editor-toolbar .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Tooltip styles */
[title] {
    position: relative;
    cursor: help;
}

/* Form switch for compact mode */
.form-check-input {
    background-color: #30304d;
    border-color: #3f4254;
}

.form-check-input:checked {
    background-color: #3699ff;
    border-color: #3699ff;
}

.form-check-label {
    color: #92929f;
    font-size: 0.85rem;
}

.text-muted {
    color: #92929f !important;
}

/* Keyboard shortcuts modal */
.modal-content.bg-dark {
    background-color: #1e1e2d !important;
}

.table-dark {
    background-color: #151521;
    color: #e4e6ef;
}

kbd {
    background-color: #3f4254;
    color: #ffffff;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 0.8rem;
    font-family: 'Consolas', 'Courier New', monospace;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.2);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1e1e2d;
}

::-webkit-scrollbar-thumb {
    background: #3f4254;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #3699ff;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .CodeMirror {
        height: 150px;
    }

    .main-container {
        padding: 10px;
    }

    .table-responsive {
        max-height: calc(100vh - 350px);
    }
}

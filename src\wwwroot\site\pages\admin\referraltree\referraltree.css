/* Referral Tree Styles */
.referral-tree {
    padding: 10px;
    overflow-x: auto;
    will-change: transform;
    transform: translateZ(0);
}

.tree {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.tree li {
    margin: 0;
    padding: 10px 5px 0 5px;
    position: relative;
    list-style-type: none;
}

.tree li::before {
    content: '';
    position: absolute;
    top: 0;
    width: 1px;
    height: 100%;
    right: auto;
    left: -20px;
    border-left: 1px solid #ccc;
    bottom: 50px;
}

.tree li::after {
    content: '';
    position: absolute;
    top: 30px;
    width: 25px;
    height: 20px;
    right: auto;
    left: -20px;
    border-top: 1px solid #ccc;
}

.tree li:last-child::before {
    height: 30px;
}

.tree > li:first-child::before {
    display: none;
}

.tree li .tree-item {
    display: inline-block;
    padding: 5px 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f8f9fa;
    position: relative;
}

.tree li .tree-item::before {
    content: 'Level ' attr(data-level);
    position: absolute;
    top: -20px;
    left: 0;
    font-size: 12px;
    color: #666;
    background-color: #f8f9fa;
    padding: 2px 5px;
    border-radius: 3px;
    border: 1px solid #ccc;
}

.tree ul {
    margin-left: 20px;
    padding-left: 0;
}

.tree ul li {
    margin: 0;
    padding: 10px 5px 0 5px;
    position: relative;
}

/* Copy tooltip animation */
.copy-tooltip {
    position: fixed;
    background-color: #4caf50;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(-20px);
    animation: fadeInOut 1.5s ease;
    z-index: 1000;
    text-align: center;
    transform: translateX(-50%);
}

@keyframes fadeInOut {
    0% { opacity: 0; }
    15% { opacity: 1; }
    85% { opacity: 1; }
    100% { opacity: 0; }
}

/* Responsive styles */
@media (max-width: 768px) {
    .tree ul {
        margin-left: 10px;
    }
    
    .tree-item {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* User details card */
.user-details .card-header.bg-primary {
    color: white;
}

.user-details dl.row dt {
    font-weight: bold;
}

/* Search form */
.search-form .select2-container {
    width: 100% !important;
}

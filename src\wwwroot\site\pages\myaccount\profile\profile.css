/* Profile page specific styles */

/* Copy field styles */
.copy-field {
    display: flex;
    align-items: center;
}

.copy-field .field-value {
    flex-grow: 1;
    margin-right: 5px;
}

.copy-field .copy-btn {
    flex-shrink: 0;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    color: #3a7bd5;
}

/* Copy tooltip animation */
.copy-tooltip {
    position: absolute;
    background-color: #4caf50;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(-20px);
    animation: fadeInOut 1.5s ease;
    z-index: 1000;
}

@keyframes fadeInOut {
    0% { opacity: 0; }
    15% { opacity: 1; }
    85% { opacity: 1; }
    100% { opacity: 0; }
}

/* Referral Tree Styles */
.referral-tree {
    padding: 10px;
}

.tree {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

.tree li {
    margin: 0;
    padding: 10px 5px 0 5px;
    position: relative;
    list-style-type: none;
}

.tree li::before {
    content: '';
    position: absolute;
    top: 0;
    width: 1px;
    height: 100%;
    right: auto;
    left: -20px;
    border-left: 1px solid #ccc;
    bottom: 50px;
}

.tree li::after {
    content: '';
    position: absolute;
    top: 30px;
    width: 25px;
    height: 20px;
    right: auto;
    left: -20px;
    border-top: 1px solid #ccc;
}

.tree li:last-child::before {
    height: 30px;
}

.tree > li:first-child::before {
    display: none;
}

.tree li .tree-item {
    display: inline-block;
    padding: 5px 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f8f9fa;
}

.tree ul {
    margin-left: 20px;
    padding-left: 0;
}

.tree ul li {
    margin: 0;
    padding: 10px 5px 0 5px;
    position: relative;
}

/* Badge styles for referral tree */
.tree-item .badge {
    font-size: 0.85em;
    padding: 0.35em 0.65em;
    margin-left: 0.5em;
    font-weight: 500;
}

.tree-item .badge i {
    margin-right: 0.25em;
}

.tree-item .badge-primary {
    background-color: #007bff;
    color: white;
}

.tree-item .badge-success {
    background-color: #28a745;
    color: white;
}

.tree-item .badge-info {
    background-color: #17a2b8;
    color: white;
}

.tree-item .badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.tree-item .badge-secondary {
    background-color: #6c757d;
    color: white;
}

/* Responsive styles for sidebar */
@media (max-width: 576px) {
    .list-group-item .field-value {
        max-width: 100%;
        margin-top: 5px;
    }

    .list-group-item .copy-btn {
        margin-top: 5px;
    }

    .tree ul {
        margin-left: 10px;
    }

    .tree-item {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .tree-item .badge {
        display: inline-block;
        margin-top: 0.25em;
        margin-left: 0;
    }
}

// Global function to copy text to clipboard
function copyToClipboard(elementId) {
    try {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error(window.t["Element not found"] + ': ' + elementId);
            return;
        }

        // Get the text to copy
        let text;
        if (element.tagName === 'INPUT') {
            text = element.value;
        } else {
            text = element.innerText;
        }

        // Use the modern clipboard API if available
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text)
                .then(() => {
                    console.log(window.t["Text copied to clipboard"]);
                    showCopySuccess(elementId);
                })
                .catch(err => {
                    console.error(window.t["Failed to copy text"] + ': ', err);
                    fallbackCopyToClipboard(text, elementId);
                });
        } else {
            fallbackCopyToClipboard(text, elementId);
        }
    } catch (error) {
        console.error(window.t["Error copying to clipboard"] + ':', error);
    }
}

// Fallback method for copying to clipboard
function fallbackCopyToClipboard(text, elementId) {
    // Create a temporary textarea element to copy from
    const textarea = document.createElement('textarea');
    textarea.value = text;
    textarea.setAttribute('readonly', '');
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    document.body.appendChild(textarea);

    try {
        // Select and copy the text
        textarea.select();
        const successful = document.execCommand('copy');

        if (successful) {
            console.log(window.t["Text copied to clipboard using fallback"]);
            showCopySuccess(elementId);
        } else {
            console.error(window.t["Fallback copy failed"]);
        }
    } catch (err) {
        console.error(window.t["Fallback copy error"] + ':', err);
    } finally {
        // Remove the temporary element
        document.body.removeChild(textarea);
    }
}

// Show success UI after copying
function showCopySuccess(elementId) {
    // Find the button based on the element ID
    let button;
    if (elementId === 'sidebar-referral-code') {
        // For sidebar button - get the button next to the element
        const element = document.getElementById(elementId);
        button = element.nextElementSibling;
    } else if (elementId === 'referralCodeInput') {
        // For referral tab button
        button = document.querySelector('.input-group-append .btn-outline-primary');
    }

    if (!button) {
        console.error(window.t["Button not found for element"] + ': ' + elementId);
        return;
    }

    // Store original button content
    const originalContent = button.innerHTML;
    const originalClass = button.className;

    // Change button appearance
    button.classList.remove('btn-outline-primary');
    button.classList.add('btn-success');

    if (elementId === 'sidebar-referral-code') {
        button.innerHTML = '<i class="fas fa-check"></i>';
    } else {
        button.innerHTML = '<i class="fas fa-check"></i> ' + window.t["Copied"];
    }

    // Reset after a short delay
    setTimeout(() => {
        button.className = originalClass;
        button.innerHTML = originalContent;
    }, 1500);

    // Success notification is shown by changing the button appearance
}

// IBAN validation and formatting
$(function () {
    // IBAN validation using the mod-97 check algorithm
    function isValidIBAN(iban) {
        // Handle empty input
        if (!iban) return false;

        // Clean and normalize the IBAN
        iban = iban.replace(/\s+/g, '').toUpperCase();

        // Basic length check
        if (iban.length < 15 || iban.length > 34) return false;

        // Country-specific length checks
        if (iban.startsWith('TR') && iban.length !== 26) {
            return false;
        }

        // Check that the first two characters are letters
        if (!/^[A-Z]{2}/.test(iban)) {
            return false;
        }

        // Check that characters 3 and 4 are digits
        if (!/^[A-Z]{2}[0-9]{2}/.test(iban)) {
            return false;
        }

        try {
            // Rearrange the IBAN: move the first 4 characters to the end
            const rearranged = iban.slice(4) + iban.slice(0, 4);

            // Convert letters to numbers (A=10, B=11, ..., Z=35)
            const converted = rearranged.replace(/[A-Z]/g, ch => (ch.charCodeAt(0) - 55).toString());

            // Process the string in chunks to avoid integer overflow
            let remainder = '';
            for (let i = 0; i < converted.length; i += 7) {
                remainder = remainder + converted.substring(i, i + 7);
                remainder = (parseInt(remainder, 10) % 97).toString();
            }

            // The IBAN is valid if the final remainder is 1
            return parseInt(remainder, 10) === 1;
        } catch (e) {
            console.error(window.t["Error validating IBAN"] + ':', e);
            return false;
        }
    }

    $.validator.addMethod("validIban", function(value, element) {
        // Skip validation if empty
        if (!value) return true;

        return isValidIBAN(value);
    }, window.t["Enter a valid IBAN"]);

    $("#ProfileInput_Iban").on("input", function() {
        // Format IBAN with spaces for better readability
        var iban = $(this).val().replace(/\s/g, '').toUpperCase();
        var formattedIban = '';

        // Convert to uppercase and format with spaces every 4 characters
        for (var i = 0; i < iban.length; i++) {
            if (i > 0 && i % 4 === 0) {
                formattedIban += ' ';
            }
            formattedIban += iban[i];
        }

        $(this).val(formattedIban);
    });

    // Format and validate the IBAN on page load
    $(document).ready(function() {
        // Format IBAN in the form
        var iban = $("#ProfileInput_Iban").val();
        if (iban) {
            // Trigger the input event to format the IBAN
            $("#ProfileInput_Iban").trigger("input");

            // Validate the IBAN
            setTimeout(validateIban, 500); // Slight delay to ensure formatting is complete
        }

        // Format IBAN in the sidebar
        var sidebarIban = $("#sidebar-iban").text();
        if (sidebarIban) {
            var formattedIban = '';
            sidebarIban = sidebarIban.replace(/\s/g, '');

            for (var i = 0; i < sidebarIban.length; i++) {
                if (i > 0 && i % 4 === 0) {
                    formattedIban += ' ';
                }
                formattedIban += sidebarIban[i];
            }

            $("#sidebar-iban").text(formattedIban);
            // Also update the title attribute for the tooltip
            $("#sidebar-iban").attr('title', formattedIban);
        }
    });

    // Add validation rule to the form
    $("form").validate({
        rules: {
            "ProfileInput.Iban": {
                validIban: true
            }
        }
    });

    // Add real-time validation for IBAN field
    function validateIban() {
        const iban = $("#ProfileInput_Iban").val();

        // Skip validation if IBAN is empty
        if (!iban || iban.trim() === '') {
            $("#ibanError").hide();
            $("#ProfileInput_Iban").removeClass("is-invalid");
            return true;
        }

        // Validate IBAN
        if (!isValidIBAN(iban)) {
            $("#ibanError").show();
            $("#ProfileInput_Iban").addClass("is-invalid");
            return false;
        } else {
            $("#ibanError").hide();
            $("#ProfileInput_Iban").removeClass("is-invalid");
            return true;
        }
    }

    // Validate on blur
    $("#ProfileInput_Iban").on("blur", validateIban);

    // Validate on form submit
    $("#profileForm").on("submit", function(e) {
        const iban = $("#ProfileInput_Iban").val();

        // Skip validation if IBAN is empty
        if (!iban || iban.trim() === '') {
            return true;
        }

        // If IBAN is provided but invalid, prevent form submission
        if (!isValidIBAN(iban)) {
            e.preventDefault();
            $("#ibanError").show();

            // Scroll to the IBAN field
            $('html, body').animate({
                scrollTop: $("#ProfileInput_Iban").offset().top - 100
            }, 200);

            // Add a red border to highlight the field
            $("#ProfileInput_Iban").addClass("is-invalid");

            // Error message is shown by the error span and invalid class

            return false;
        }

        // IBAN is valid, allow form submission
        $("#ProfileInput_Iban").removeClass("is-invalid");
        return true;
    });
});


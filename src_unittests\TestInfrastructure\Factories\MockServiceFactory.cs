using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Tests.TestInfrastructure.Factories
{
    /// <summary>
    /// Factory class for creating mock services with standardized configurations.
    /// This centralizes mock creation and ensures consistency across all tests.
    /// 
    /// Usage Example:
    /// var mockWallet = MockServiceFactory.CreateWalletService();
    /// var mockTrade = MockServiceFactory.CreateTradeService();
    /// </summary>
    public static class MockServiceFactory
    {
        /// <summary>
        /// Creates a mock WalletService with standard configuration.
        /// Includes in-memory wallet tracking and realistic behavior simulation.
        /// </summary>
        /// <param name="initialWallets">Optional initial wallets to populate</param>
        /// <returns>Configured Mock&lt;IWalletService&gt;</returns>
        public static Mock<IWalletService> CreateWalletService(Dictionary<(int UserId, int CoinId), Wallet>? initialWallets = null)
        {
            var mock = new Mock<IWalletService>(MockBehavior.Strict);
            var wallets = initialWallets ?? new Dictionary<(int UserId, int CoinId), Wallet>();
            var nextId = wallets.Values.Any() ? wallets.Values.Max(w => w.Id) + 1 : 1;

            // Setup GetByIdAsync
            mock.Setup(x => x.GetByIdAsync(It.IsAny<int>()))
                .ReturnsAsync((int id) => wallets.Values.FirstOrDefault(w => w.Id == id));

            // Setup GetByUserIdAsync
            mock.Setup(x => x.GetByUserIdAsync(It.IsAny<int>()))
                .ReturnsAsync((int userId) => wallets.Values.Where(w => w.UserId == userId).ToList());

            // Setup GetByUserIdAndCoinIdAsync
            mock.Setup(x => x.GetByUserIdAndCoinIdAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, int coinId, AppDbContext? context) =>
                    wallets.TryGetValue((userId, coinId), out var wallet) ? wallet : null);

            // Setup CreateAsync
            mock.Setup(x => x.CreateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((Wallet wallet, AppDbContext? context) =>
                {
                    wallet.Id = nextId++;
                    wallet.CreatedDate = DateTime.UtcNow;
                    wallets[(wallet.UserId, wallet.CoinId)] = wallet;
                    return wallet;
                });

            // Setup AddAvailableBalanceAsync - Returns Task<Wallet>
            mock.Setup(x => x.AddAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, RzwTokenInfo tokenInfo, decimal amount, TradeType tradeType, AppDbContext? context) =>
                {
                    var key = (userId, tokenInfo.TokenId);
                    if (wallets.TryGetValue(key, out var wallet))
                    {
                        wallet.Balance += amount;
                        wallet.ModifiedDate = DateTime.UtcNow;
                        return wallet;
                    }
                    else
                    {
                        wallet = new Wallet
                        {
                            Id = nextId++,
                            UserId = userId,
                            CoinId = tokenInfo.TokenId,
                            Balance = amount,
                            CreatedDate = DateTime.UtcNow,
                            ModifiedDate = DateTime.UtcNow
                        };
                        wallets[key] = wallet;
                        return wallet;
                    }
                });

            // Setup DeductAvailableBalanceAsync - Returns Task<bool>
            mock.Setup(x => x.DeductAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<AppDbContext>()))
                .ReturnsAsync((int userId, RzwTokenInfo tokenInfo, decimal amount, AppDbContext? context) =>
                {
                    var key = (userId, tokenInfo.TokenId);
                    if (wallets.TryGetValue(key, out var wallet) && wallet.Balance >= amount)
                    {
                        wallet.Balance -= amount;
                        wallet.ModifiedDate = DateTime.UtcNow;
                        return true;
                    }
                    return false;
                });

            return mock;
        }

        /// <summary>
        /// Creates a mock WalletService that throws exceptions for failure testing.
        /// All methods throw "Wallet service failure" exceptions.
        /// </summary>
        /// <returns>Mock&lt;IWalletService&gt; configured to throw exceptions</returns>
        public static Mock<IWalletService> CreateFailingWalletService()
        {
            var mock = new Mock<IWalletService>(MockBehavior.Strict);
            var exception = new Exception("Wallet service failure");

            mock.Setup(x => x.GetByIdAsync(It.IsAny<int>())).ThrowsAsync(exception);
            mock.Setup(x => x.GetByUserIdAsync(It.IsAny<int>())).ThrowsAsync(exception);
            mock.Setup(x => x.GetByUserIdAndCoinIdAsync(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<AppDbContext>())).ThrowsAsync(exception);
            mock.Setup(x => x.CreateAsync(It.IsAny<Wallet>(), It.IsAny<AppDbContext>())).ThrowsAsync(exception);
            mock.Setup(x => x.AddAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<TradeType>(), It.IsAny<AppDbContext>())).ThrowsAsync(exception);
            mock.Setup(x => x.DeductAvailableBalanceAsync(It.IsAny<int>(), It.IsAny<RzwTokenInfo>(), It.IsAny<decimal>(), It.IsAny<AppDbContext>())).ThrowsAsync(exception);

            return mock;
        }

        /// <summary>
        /// Creates a mock TokenPriceService with configurable prices.
        /// </summary>
        /// <param name="buyPrice">RZW buy price (default: 1.0)</param>
        /// <param name="coinId">Coin ID (default: 1)</param>
        /// <param name="sellPrice">RZW sell price (default: buyPrice * 0.95)</param>
        /// <returns>Configured Mock&lt;ITokenPriceService&gt;</returns>
        public static Mock<ITokenPriceService> CreateTokenPriceService(decimal buyPrice = 1.0m, int coinId = 1, decimal? sellPrice = null)
        {
            var sellPriceValue = sellPrice ?? buyPrice * 0.95m;
            var mock = new Mock<ITokenPriceService>(MockBehavior.Strict);

            var rzwTokenInfo = new RzwTokenInfo
            {
                TokenId = coinId,
                BuyPrice = buyPrice,
                SellPrice = sellPriceValue
            };

            mock.Setup(x => x.GetRzwTokenInfoAsync()).ReturnsAsync(rzwTokenInfo);
            mock.Setup(x => x.GetCurrentRzwBuyPriceAsync()).ReturnsAsync(buyPrice);
            mock.Setup(x => x.GetRzwTokenIdAsync()).ReturnsAsync(coinId);
            mock.Setup(x => x.GetCoinInfoAsync(It.IsAny<int>()))
                .ReturnsAsync((int id) => new RzwTokenInfo
                {
                    TokenId = id,
                    BuyPrice = buyPrice,
                    SellPrice = sellPriceValue
                });

            return mock;
        }

        /// <summary>
        /// Creates a mock TradeService with basic functionality.
        /// </summary>
        /// <returns>Configured Mock&lt;ITradeService&gt;</returns>
        public static Mock<ITradeService> CreateTradeService()
        {
            var mock = new Mock<ITradeService>(MockBehavior.Strict);
            var tradeId = 1;

            mock.Setup(x => x.CreateAsync(It.IsAny<Trade>(), It.IsAny<AppDbContext?>()))
                .ReturnsAsync((Trade trade, AppDbContext? existingContext) =>
                {
                    trade.Id = tradeId++;
                    trade.CreatedDate = DateTime.UtcNow;
                    return trade;
                });

            return mock;
        }

        /// <summary>
        /// Creates a mock Logger for any type.
        /// </summary>
        /// <typeparam name="T">The type to create logger for</typeparam>
        /// <returns>Mock&lt;ILogger&lt;T&gt;&gt;</returns>
        public static Mock<ILogger<T>> CreateLogger<T>()
        {
            return new Mock<ILogger<T>>();
        }

        /// <summary>
        /// Creates a mock StringLocalizer.
        /// </summary>
        /// <typeparam name="T">The resource type</typeparam>
        /// <returns>Mock&lt;IStringLocalizer&lt;T&gt;&gt;</returns>
        public static Mock<IStringLocalizer<T>> CreateLocalizer<T>()
        {
            var mock = new Mock<IStringLocalizer<T>>();

            // Setup to return the key as the localized string for testing
            mock.Setup(x => x[It.IsAny<string>()])
                .Returns((string key) => new LocalizedString(key, key));

            return mock;
        }
    }
}

using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;

namespace RazeWinComTr.RewardTests.Unit.Helpers
{
    /// <summary>
    /// Unit tests for RZW Savings Calculation Helper
    /// Tests compound interest calculations and mathematical formulas
    /// </summary>
    public class RzwSavingsCalculationHelperTests
    {
        #region Compound Interest Calculation Tests

        [Theory]
        [InlineData(1000, 0.0003, 1, 0.3)] // Interest only: 1000 * (1 + 0.0003)^1 - 1000
        [InlineData(1000, 0.0003, 30, 9.039)] // Interest only: 1000 * (1 + 0.0003)^30 - 1000 = 9.039
        [InlineData(1000, 0.0003, 365, 115.702)] // Interest only: 1000 * (1 + 0.0003)^365 - 1000 = 115.702
        [InlineData(5000, 0.01, 1, 50)] // Interest only: 5000 * (1 + 0.01)^1 - 5000 = 50
        [InlineData(5000, 0.01, 12, 634.125)] // Interest only: 5000 * (1 + 0.01)^12 - 5000 = 634.125
        [InlineData(10000, 0.15, 1, 1500)] // Interest only: 10000 * (1 + 0.15)^1 - 10000
        public void CalculateCompoundInterest_ShouldReturnCorrectInterestAmount(decimal principal, decimal rate, int periods, decimal expectedInterest)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert
            Assert.Equal(expectedInterest, Math.Round(result, 3, MidpointRounding.AwayFromZero));
        }

        [Theory]
        [InlineData(1000, 0.0003, 1, 0.3)] // Interest only
        [InlineData(1000, 0.0003, 30, 9.039)] // Interest only
        [InlineData(5000, 0.01, 12, 634.125)] // Interest only
        [InlineData(10000, 0.15, 1, 1500)] // Interest only
        public void CalculateCompoundInterest_ShouldReturnCorrectInterestOnly(decimal principal, decimal rate, int periods, decimal expectedInterest)
        {
            // Act
            var interestOnly = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert
            Assert.Equal(expectedInterest, Math.Round(interestOnly, 3, MidpointRounding.AwayFromZero));
        }

        #endregion

        #region Daily Rate Calculation Tests

        [Theory]
        [InlineData(0.0003, "Daily", 0.0003)] // Daily rate stays same
        [InlineData(0.01, "Monthly", 0.000333333)] // Monthly to daily: 0.01/30
        [InlineData(0.15, "Yearly", 0.000410959)] // Yearly to daily: 0.15/365
        public void CalculateDailyRate_ShouldReturnCorrectDailyRate(decimal rate, string termType, decimal expectedDailyRate)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateDailyRate(rate, termType);

            // Assert
            Assert.Equal(expectedDailyRate, Math.Round(result, 9, MidpointRounding.AwayFromZero));
        }

        #endregion

        #region Final Amount Calculation Tests

        [Theory]
        [InlineData(1000, 0.0003, 1, 1000.3)] // 1000 * (1.0003)^1 = 1000.3
        [InlineData(1000, 0.0003, 30, 1009.039)] // 1000 * (1.0003)^30 = 1009.039
        [InlineData(5000, 0.000333333, 30, 5050.242)] // 5000 * (1.000333333)^30 = 5050.242
        [InlineData(10000, 0.000410959, 365, 11617.984)] // 10000 * (1.000410959)^365 = 11617.984
        public void CalculateFinalAmount_ShouldReturnCorrectAmount(decimal principal, decimal dailyRate, int days, decimal expectedAmount)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateFinalAmount(principal, dailyRate, days);

            // Assert - Allow for small rounding differences
            Assert.True(Math.Abs(result - expectedAmount) < 1m, $"Expected {expectedAmount}, got {result}");
        }

        #endregion

        #region Edge Cases and Validation Tests

        [Theory]
        [InlineData(0, 0.01, 30)] // Zero principal
        [InlineData(1000, 0, 30)] // Zero rate
        [InlineData(1000, 0.01, 0)] // Zero periods
        public void CalculateCompoundInterest_WithZeroValues_ShouldHandleGracefully(decimal principal, decimal rate, int periods)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert - Should return 0 for zero cases (interest only)
            Assert.Equal(0, result);
        }

        [Theory]
        [InlineData(-1000, 0.01, 30)] // Negative principal
        [InlineData(1000, -0.01, 30)] // Negative rate
        [InlineData(1000, 0.01, -30)] // Negative periods
        public void CalculateCompoundInterest_WithNegativeValues_ShouldThrowOrHandleGracefully(decimal principal, decimal rate, int periods)
        {
            // Act & Assert
            if (principal < 0 || rate < 0 || periods < 0)
            {
                // Should either throw exception or return 0/principal
                var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);
                // For negative values, we expect either an exception or a safe fallback
                Assert.True(result >= 0 || result == principal);
            }
        }

        [Fact]
        public void CalculateDailyRate_WithInvalidTermType_ShouldHandleGracefully()
        {
            // Arrange
            var invalidTermType = "Invalid";

            // Act
            var result = RzwSavingsCalculationHelper.CalculateDailyRate(0.01m, invalidTermType);

            // Assert
            // Should return the original rate or 0 for invalid term type
            Assert.True(result >= 0);
        }

        #endregion

        #region Precision and Rounding Tests

        [Theory]
        [InlineData(1000.12345678, 0.00030001, 1)] // High precision inputs
        [InlineData(999999.99999999, 0.00029999, 365)] // Large numbers with precision
        public void CalculateCompoundInterest_WithHighPrecision_ShouldMaintainAccuracy(decimal principal, decimal rate, int periods)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert - result is interest only, not total amount
            Assert.True(result > 0); // Interest should be positive for positive rates
            Assert.True(result < principal); // Interest should be less than principal for reasonable rates
        }

        [Fact]
        public void CalculateCompoundInterest_ShouldRoundTo8DecimalPlaces()
        {
            // Arrange
            decimal principal = 1000m;
            decimal rate = 0.0003m;
            int periods = 1;

            // Act
            var result = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, periods);

            // Assert
            var decimalPlaces = BitConverter.GetBytes(decimal.GetBits(result)[3])[2];
            Assert.True(decimalPlaces <= 8, $"Result has {decimalPlaces} decimal places, expected <= 8");
        }

        #endregion

        #region Real-World Scenario Tests

        [Fact]
        public void DailyPlan_OneMonth_ShouldCalculateCorrectly()
        {
            // Arrange - Daily plan: 0.03% daily for 30 days
            decimal principal = 1000m;
            decimal dailyRate = 0.0003m;
            int days = 30;

            // Act
            var interest = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, dailyRate, days);

            // Assert
            Assert.True(interest > 8.9m && interest < 9.1m, $"Expected ~9 RZW interest, got {interest}");
        }

        [Fact]
        public void MonthlyPlan_OneMonth_ShouldCalculateCorrectly()
        {
            // Arrange - Monthly plan: 1% monthly for 30 days
            decimal principal = 5000m;
            decimal monthlyRate = 0.01m;
            var termType = RzwSavingsTermType.Monthly;
            int days = 30;

            // Convert monthly rate to daily rate
            var dailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(monthlyRate, termType);

            // Act
            var result = RzwSavingsCalculationHelper.CalculateFinalAmount(principal, dailyRate, days);
            var interest = result - principal;

            // Assert - Monthly rate 0.01/30 = 0.000333333, so 5000 * (1.000333333)^30 - 5000 = ~50.24
            Assert.True(interest > 49m && interest < 52m, $"Expected ~50.24 RZW interest, got {interest}");
        }

        [Fact]
        public void YearlyPlan_OneYear_ShouldCalculateCorrectly()
        {
            // Arrange - Yearly plan: 15% yearly for 365 days
            decimal principal = 10000m;
            decimal yearlyRate = 0.15m;
            var termType = RzwSavingsTermType.Yearly;
            int days = 365;

            // Convert yearly rate to daily rate
            var dailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(yearlyRate, termType);

            // Act
            var result = RzwSavingsCalculationHelper.CalculateFinalAmount(principal, dailyRate, days);
            var interest = result - principal;

            // Assert - Yearly rate 0.15/365 = 0.000410959, so 10000 * (1.000410959)^365 - 10000 = ~1617.98
            Assert.True(interest > 1610m && interest < 1625m, $"Expected ~1617.98 RZW interest, got {interest}");
        }

        #endregion

        #region APY Calculation Tests

        [Theory]
        [InlineData(0.0003, 0.115702)] // 0.03% daily = ~11.57% APY
        [InlineData(0.001, 0.440251)] // 0.1% daily = ~44.03% APY (corrected)
        [InlineData(0.000410959, 0.161798)] // 15% yearly rate = ~16.18% APY
        public void CalculateAPY_ShouldReturnCorrectAPY(decimal dailyRate, decimal expectedAPY)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateAPY(dailyRate);

            // Assert
            Assert.Equal(expectedAPY, Math.Round(result, 6, MidpointRounding.AwayFromZero));
        }

        [Theory]
        [InlineData(0.0003, 11.5702)] // 0.03% daily = ~11.57% APY
        [InlineData(0.001, 44.0251)] // 0.1% daily = ~44.03% APY (corrected)
        public void GetAPYAsPercentage_ShouldReturnCorrectPercentage(decimal dailyRate, decimal expectedPercentage)
        {
            // Act
            var result = RzwSavingsCalculationHelper.GetAPYAsPercentage(dailyRate);

            // Assert
            Assert.Equal(expectedPercentage, Math.Round(result, 4, MidpointRounding.AwayFromZero));
        }

        [Theory]
        [InlineData(0.0003, 11.57)] // 0.03% daily = 11.57% floored
        [InlineData(0.001, 44.02)] // 0.1% daily = 44.02% floored
        [InlineData(0.000410959, 16.17)] // 15% yearly rate = 16.17% floored
        public void GetAPYAsFlooredPercentage_ShouldReturnFlooredPercentage(decimal dailyRate, decimal expectedFlooredPercentage)
        {
            // Act
            var result = RzwSavingsCalculationHelper.GetAPYAsFlooredPercentage(dailyRate);

            // Assert
            Assert.Equal(expectedFlooredPercentage, result);
        }

        [Fact]
        public void CalculateAPY_WithPlan_ShouldReturnCorrectAPY()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m
            };

            // Act
            var result = RzwSavingsCalculationHelper.CalculateAPY(plan);

            // Assert
            Assert.Equal(0.115702m, Math.Round(result, 6, MidpointRounding.AwayFromZero));
        }

        [Fact]
        public void GetAPYAsPercentage_WithPlan_ShouldReturnCorrectPercentage()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m
            };

            // Act
            var result = RzwSavingsCalculationHelper.GetAPYAsPercentage(plan);

            // Assert
            Assert.Equal(11.5702m, Math.Round(result, 4, MidpointRounding.AwayFromZero));
        }

        [Fact]
        public void GetAPYAsFlooredPercentage_WithPlan_ShouldReturnFlooredPercentage()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m
            };

            // Act
            var result = RzwSavingsCalculationHelper.GetAPYAsFlooredPercentage(plan);

            // Assert
            Assert.Equal(11.57m, result);
        }

        #endregion

        #region Daily Interest and Total Interest Tests

        [Fact]
        public void CalculateDailyInterest_ShouldReturnCorrectAmount()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m
            };
            decimal principal = 1000m;

            // Act
            var result = RzwSavingsCalculationHelper.CalculateDailyInterest(principal, plan);

            // Assert
            Assert.Equal(0.3m, result);
        }

        [Fact]
        public void CalculateTotalInterest_WithDefaultDuration_ShouldUseTermDuration()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m,
                TermDuration = 30
            };
            decimal principal = 1000m;

            // Act
            var result = RzwSavingsCalculationHelper.CalculateTotalInterest(principal, plan);

            // Assert
            var expected = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, plan.InterestRate, 30);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void CalculateTotalInterest_WithSpecificDays_ShouldUseSpecifiedDays()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m,
                TermDuration = 30
            };
            decimal principal = 1000m;
            int specificDays = 15;

            // Act
            var result = RzwSavingsCalculationHelper.CalculateTotalInterest(principal, plan, specificDays);

            // Assert
            var expected = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, plan.InterestRate, 15);
            Assert.Equal(expected, result);
        }

        #endregion

        #region Effective APY Calculation Tests

        [Theory]
        [InlineData(1000, 1157.02, 365, 15.70)] // Exactly 365 days
        [InlineData(1000, 1050, 180, 10.40)] // Half year, annualized (corrected)
        [InlineData(5000, 5250, 90, 21.88)] // Quarter year, annualized (corrected)
        public void CalculateEffectiveAPY_ShouldReturnCorrectAPY(decimal principal, decimal finalAmount, int termDays, decimal expectedAPY)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateEffectiveAPY(principal, finalAmount, termDays);

            // Assert
            Assert.Equal(expectedAPY, Math.Round(result, 2, MidpointRounding.AwayFromZero));
        }

        [Theory]
        [InlineData(0, 1000, 365)] // Zero principal
        [InlineData(1000, 1000, 365)] // No gain
        [InlineData(1000, 900, 365)] // Loss
        [InlineData(1000, 1100, 0)] // Zero days
        public void CalculateEffectiveAPY_WithInvalidInputs_ShouldReturnZero(decimal principal, decimal finalAmount, int termDays)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateEffectiveAPY(principal, finalAmount, termDays);

            // Assert
            Assert.Equal(0m, result);
        }

        #endregion

        #region Format and Display Tests      

        [Fact]
        public void GetMaxAmountDisplayText_WithValue_ShouldReturnFormattedStringWithRZW()
        {
            var expected1000Rzw = $"{1000:N0} RZW";
            var expected50000Rzw = $"{50000:N0} RZW";
            // Act & Assert
            Assert.Equal(expected1000Rzw, RzwSavingsCalculationHelper.GetMaxAmountDisplayText(1000m, "Unlimited"));
            Assert.Equal(expected50000Rzw, RzwSavingsCalculationHelper.GetMaxAmountDisplayText(50000m, "Unlimited"));
        }

        [Fact]
        public void GetMaxAmountDisplayText_WithNull_ShouldReturnUnlimitedText()
        {
            // Act
            var result = RzwSavingsCalculationHelper.GetMaxAmountDisplayText(null, "Unlimited");

            // Assert
            Assert.Equal("Unlimited", result);
        }

        [Theory]
        [InlineData(100)]
        [InlineData(1000)]
        [InlineData(50000)]
        public void GetMinAmountDisplayText_ShouldReturnCorrectFormat(decimal minAmount)
        {
            // Act
            var result = RzwSavingsCalculationHelper.GetMinAmountDisplayText(minAmount);

            var expected = $"{minAmount:N0} RZW";
            // Assert
            Assert.Equal(expected, result);
        }

        #endregion

        #region Progress and Time Calculation Tests

        [Fact]
        public void CalculatePreciseProgressPercentage_ShouldReturnCorrectPercentage()
        {
            // Arrange
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = new DateTime(2024, 1, 2, 0, 0, 0, DateTimeKind.Utc); // 1 day = 24 hours
            var currentDate = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc); // 12 hours elapsed = 50%

            // Act
            var result = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(50m, result);
        }

        [Fact]
        public void CalculatePreciseProgressPercentage_WhenMatured_ShouldReturn100()
        {
            // Arrange
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = new DateTime(2024, 1, 2, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 3, 0, 0, 0, DateTimeKind.Utc); // Past maturity

            // Act
            var result = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(100m, result);
        }

        [Fact]
        public void CalculatePreciseProgressPercentage_BeforeStart_ShouldReturnZero()
        {
            // Arrange
            var startDate = new DateTime(2024, 1, 2, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = new DateTime(2024, 1, 3, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc); // Before start

            // Act
            var result = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(0m, result);
        }

        [Fact]
        public void CalculateElapsedSeconds_ShouldReturnCorrectSeconds()
        {
            // Arrange
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 1, 1, 0, 0, DateTimeKind.Utc); // 1 hour = 3600 seconds

            // Act
            var result = RzwSavingsCalculationHelper.CalculateElapsedSeconds(startDate, currentDate);

            // Assert
            Assert.Equal(3600, result);
        }

        [Fact]
        public void CalculateRemainingSeconds_ShouldReturnCorrectSeconds()
        {
            // Arrange
            var maturityDate = new DateTime(2024, 1, 1, 2, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 1, 1, 0, 0, DateTimeKind.Utc); // 1 hour remaining = 3600 seconds

            // Act
            var result = RzwSavingsCalculationHelper.CalculateRemainingSeconds(maturityDate, currentDate);

            // Assert
            Assert.Equal(3600, result);
        }

        #endregion

        #region Early Withdrawal Tests

        [Theory]
        [InlineData(100, 0.1, 10)] // 100 RZW earned, 10% penalty = 10 RZW penalty
        [InlineData(50, 0.2, 10)] // 50 RZW earned, 20% penalty = 10 RZW penalty
        [InlineData(0, 0.1, 0)] // No interest earned, no penalty
        [InlineData(100, 0, 0)] // No penalty rate, no penalty
        public void CalculateEarlyWithdrawalPenalty_ShouldReturnCorrectPenalty(decimal earnedInterest, decimal penaltyRate, decimal expectedPenalty)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateEarlyWithdrawalPenalty(earnedInterest, penaltyRate);

            // Assert
            Assert.Equal(expectedPenalty, result);
        }

        [Theory]
        [InlineData(1000, 100, 0.1, 1090)] // Principal 1000 + Interest 100 - Penalty 10 = 1090
        [InlineData(5000, 250, 0.2, 5200)] // Principal 5000 + Interest 250 - Penalty 50 = 5200
        [InlineData(1000, 0, 0.1, 1000)] // No interest earned, just principal
        public void CalculateEarlyWithdrawalNetAmount_ShouldReturnCorrectNetAmount(decimal principal, decimal earnedInterest, decimal penaltyRate, decimal expectedNetAmount)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateEarlyWithdrawalNetAmount(principal, earnedInterest, penaltyRate);

            // Assert
            Assert.Equal(expectedNetAmount, result);
        }

        #endregion

        #region Remaining Days Calculation Tests

        [Fact]
        public void CalculatePreciseRemainingDays_ShouldReturnCorrectDays()
        {
            // Arrange
            var maturityDate = new DateTime(2024, 1, 2, 12, 0, 0, DateTimeKind.Utc); // 1.5 days from start
            var currentDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            // Act
            var result = RzwSavingsCalculationHelper.CalculatePreciseRemainingDays(maturityDate, currentDate);

            // Assert
            Assert.Equal(1.5m, result);
        }

        [Fact]
        public void CalculatePreciseRemainingDays_WhenMatured_ShouldReturnZero()
        {
            // Arrange
            var maturityDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 2, 0, 0, 0, DateTimeKind.Utc); // Past maturity

            // Act
            var result = RzwSavingsCalculationHelper.CalculatePreciseRemainingDays(maturityDate, currentDate);

            // Assert
            Assert.Equal(0m, result);
        }

        [Fact]
        public void CalculateRemainingDaysAsInteger_ShouldRoundUpCorrectly()
        {
            // Arrange
            var maturityDate = new DateTime(2024, 1, 1, 1, 0, 0, DateTimeKind.Utc); // 1 hour remaining
            var currentDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            // Act
            var result = RzwSavingsCalculationHelper.CalculateRemainingDaysAsInteger(maturityDate, currentDate);

            // Assert
            Assert.Equal(1, result); // Should round up to 1 day
        }

        [Fact]
        public void CalculateRemainingDaysAsInteger_WhenMatured_ShouldReturnZero()
        {
            // Arrange
            var maturityDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 2, 0, 0, 0, DateTimeKind.Utc); // Past maturity

            // Act
            var result = RzwSavingsCalculationHelper.CalculateRemainingDaysAsInteger(maturityDate, currentDate);

            // Assert
            Assert.Equal(0, result);
        }

        #endregion

        #region Maturity and Completion Tests

        [Fact]
        public void IsMatured_WhenMatured_ShouldReturnTrue()
        {
            // Arrange
            var maturityDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 1, 0, 0, 1, DateTimeKind.Utc); // 1 second past maturity

            // Act
            var result = RzwSavingsCalculationHelper.IsMatured(maturityDate, currentDate);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsMatured_WhenNotMatured_ShouldReturnFalse()
        {
            // Arrange
            var maturityDate = new DateTime(2024, 1, 1, 0, 0, 1, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc); // 1 second before maturity

            // Act
            var result = RzwSavingsCalculationHelper.IsMatured(maturityDate, currentDate);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CalculateCompletionPercentage_ShouldMatchPreciseProgressPercentage()
        {
            // Arrange
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var maturityDate = new DateTime(2024, 1, 2, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc);

            // Act
            var completionResult = RzwSavingsCalculationHelper.CalculateCompletionPercentage(startDate, maturityDate, currentDate);
            var progressResult = RzwSavingsCalculationHelper.CalculatePreciseProgressPercentage(startDate, maturityDate, currentDate);

            // Assert
            Assert.Equal(progressResult, completionResult);
        }

        #endregion

        #region Current Earned Interest Tests

        [Fact]
        public void CalculateCurrentEarnedInterest_ShouldReturnCorrectAmount()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m,
                TermDuration = 30
            };
            decimal principal = 1000m;
            var startDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 16, 0, 0, 0, DateTimeKind.Utc); // 15 days elapsed

            // Act
            var result = RzwSavingsCalculationHelper.CalculateCurrentEarnedInterest(principal, plan, startDate, currentDate);

            // Assert
            var expected = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, plan.InterestRate, 15);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void CalculateCurrentEarnedInterest_BeforeStart_ShouldReturnZero()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m,
                TermDuration = 30
            };
            decimal principal = 1000m;
            var startDate = new DateTime(2024, 1, 2, 0, 0, 0, DateTimeKind.Utc);
            var currentDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc); // Before start

            // Act
            var result = RzwSavingsCalculationHelper.CalculateCurrentEarnedInterest(principal, plan, startDate, currentDate);

            // Assert
            Assert.Equal(0m, result);
        }

        [Fact]
        public void CalculateProjectedTotalInterest_ShouldReturnCorrectAmount()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m,
                TermDuration = 30
            };
            decimal principal = 1000m;

            // Act
            var result = RzwSavingsCalculationHelper.CalculateProjectedTotalInterest(principal, plan);

            // Assert
            var expected = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, plan.InterestRate, plan.TermDuration);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void CalculateMaturityAmount_ShouldReturnPrincipalPlusInterest()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m,
                TermDuration = 30
            };
            decimal principal = 1000m;

            // Act
            var result = RzwSavingsCalculationHelper.CalculateMaturityAmount(principal, plan);

            // Assert
            var expectedInterest = RzwSavingsCalculationHelper.CalculateProjectedTotalInterest(principal, plan);
            var expectedTotal = principal + expectedInterest;
            Assert.Equal(expectedTotal, result);
        }

        [Fact]
        public void CalculateCurrentEarnedInterestForAccount_WithNullPlan_ShouldReturnTotalEarnedRzw()
        {
            // Arrange
            var account = new RzwSavingsAccount
            {
                RzwAmount = 1000m,
                TotalEarnedRzw = 50m,
                StartDate = DateTime.UtcNow.AddDays(-10),
                Plan = null!
            };

            // Act
            var result = RzwSavingsCalculationHelper.CalculateCurrentEarnedInterestForAccount(account);

            // Assert
            Assert.Equal(50m, result);
        }

        [Fact]
        public void CalculateCurrentEarnedInterestForAccount_WithValidPlan_ShouldCalculateCorrectly()
        {
            // Arrange
            var plan = new RzwSavingsPlan
            {
                InterestRate = 0.0003m,
                TermDuration = 30
            };
            var account = new RzwSavingsAccount
            {
                RzwAmount = 1000m,
                TotalEarnedRzw = 0m,
                StartDate = new DateTime(2024, 1, 1, 0, 0, 0, DateTimeKind.Utc),
                Plan = plan
            };
            var currentDate = new DateTime(2024, 1, 16, 0, 0, 0, DateTimeKind.Utc); // 15 days elapsed

            // Act
            var result = RzwSavingsCalculationHelper.CalculateCurrentEarnedInterestForAccount(account, currentDate);

            // Assert
            var expected = RzwSavingsCalculationHelper.CalculateCompoundInterest(account.RzwAmount, plan.InterestRate, 15);
            Assert.Equal(expected, result);
        }

        #endregion

        #region Edge Cases and Boundary Tests

        [Fact]
        public void CalculateFinalAmount_WithZeroValues_ShouldReturnPrincipal()
        {
            // Act & Assert
            Assert.Equal(1000m, RzwSavingsCalculationHelper.CalculateFinalAmount(1000m, 0m, 30));
            Assert.Equal(1000m, RzwSavingsCalculationHelper.CalculateFinalAmount(1000m, 0.01m, 0));
            Assert.Equal(0m, RzwSavingsCalculationHelper.CalculateFinalAmount(0m, 0.01m, 30));
        }

        [Fact]
        public void CalculateEffectiveAPY_For365Days_ShouldReturnActualReturn()
        {
            // Arrange
            decimal principal = 1000m;
            decimal finalAmount = 1150m; // 15% return
            int termDays = 365;

            // Act
            var result = RzwSavingsCalculationHelper.CalculateEffectiveAPY(principal, finalAmount, termDays);

            // Assert
            Assert.Equal(15.00m, result); // Should be exactly 15% for 365 days
        }

        [Theory]
        [InlineData(0.0003, "Invalid", 0)] // Invalid term type should return 0
        [InlineData(0, "Daily", 0)] // Zero rate should return 0
        public void CalculateDailyRate_EdgeCases_ShouldHandleGracefully(decimal rate, string termType, decimal expected)
        {
            // Act
            var result = RzwSavingsCalculationHelper.CalculateDailyRate(rate, termType);

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public void AllCalculationMethods_ShouldRoundTo8DecimalPlaces()
        {
            // Arrange
            decimal principal = 1000.123456789m;
            decimal rate = 0.000300001m;
            int days = 30;

            // Act
            var compoundInterest = RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, rate, days);
            var finalAmount = RzwSavingsCalculationHelper.CalculateFinalAmount(principal, rate, days);

            // Assert
            Assert.True(GetDecimalPlaces(compoundInterest) <= 8, $"Compound interest has more than 8 decimal places: {compoundInterest}");
            Assert.True(GetDecimalPlaces(finalAmount) <= 8, $"Final amount has more than 8 decimal places: {finalAmount}");
        }

        private static int GetDecimalPlaces(decimal value)
        {
            var text = value.ToString();
            var decimalIndex = text.IndexOf('.');
            return decimalIndex == -1 ? 0 : text.Length - decimalIndex - 1;
        }

        #endregion
    }
}

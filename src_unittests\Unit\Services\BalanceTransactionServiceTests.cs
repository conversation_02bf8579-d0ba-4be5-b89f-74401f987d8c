using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.BalanceTransaction;
using RazeWinComTr.Areas.Admin.ViewModels.Common;
using RazeWinComTr.Tests.TestInfrastructure.Base;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Services
{
    public class BalanceTransactionServiceTests : TestBase
    {
        private readonly Mock<IBalanceTransactionService> _mockBalanceTransactionService;

        public BalanceTransactionServiceTests()
        {
            _mockBalanceTransactionService = new Mock<IBalanceTransactionService>(MockBehavior.Strict);
        }

        [Fact]
        public async Task RecordTransactionAsync_ShouldCallServiceWithCorrectParameters()
        {
            // Arrange
            var userId = 1;
            var transactionType = TransactionType.Buy;
            var amount = 100m;
            var previousBalance = 1000m;
            var newBalance = 900m;
            var description = "Test transaction";
            var referenceId = 123;
            var referenceType = "TestType";

            var expectedTransaction = new BalanceTransaction
            {
                Id = 1,
                UserId = userId,
                TransactionType = transactionType,
                Amount = amount,
                PreviousBalance = previousBalance,
                NewBalance = newBalance,
                Description = description,
                ReferenceId = referenceId,
                ReferenceType = referenceType,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };

            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                description,
                referenceId,
                referenceType,
                null))
                .ReturnsAsync(expectedTransaction);

            // Act
            var result = await _mockBalanceTransactionService.Object.RecordTransactionAsync(
                userId: userId,
                transactionType: transactionType,
                amount: amount,
                previousBalance: previousBalance,
                newBalance: newBalance,
                description: description,
                referenceId: referenceId,
                referenceType: referenceType
            );

            // Assert
            Assert.NotNull(result);
            Assert.Equal(userId, result.UserId);
            Assert.Equal(transactionType, result.TransactionType);
            Assert.Equal(amount, result.Amount);
            Assert.Equal(previousBalance, result.PreviousBalance);
            Assert.Equal(newBalance, result.NewBalance);
            Assert.Equal(description, result.Description);
            Assert.Equal(referenceId, result.ReferenceId);
            Assert.Equal(referenceType, result.ReferenceType);
            Assert.True(result.IsActive);

            // Verify the method was called with correct parameters
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                description,
                referenceId,
                referenceType,
                null), Times.Once);
        }

        [Fact]
        public async Task RecordTransactionAsync_WithExistingContext_ShouldCallServiceCorrectly()
        {
            // Arrange
            var userId = 1;
            var transactionType = TransactionType.Deposit;
            var amount = 200m;
            var previousBalance = 500m;
            var newBalance = 700m;
            var description = "Deposit transaction";
            var dbContext = CreateDbContext("TestContext");

            var expectedTransaction = new BalanceTransaction
            {
                Id = 1,
                UserId = userId,
                TransactionType = transactionType,
                Amount = amount,
                PreviousBalance = previousBalance,
                NewBalance = newBalance,
                Description = description,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };

            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                description,
                null,
                null,
                dbContext))
                .ReturnsAsync(expectedTransaction);

            // Act
            var result = await _mockBalanceTransactionService.Object.RecordTransactionAsync(
                userId: userId,
                transactionType: transactionType,
                amount: amount,
                previousBalance: previousBalance,
                newBalance: newBalance,
                description: description,
                existingContext: dbContext
            );

            // Assert
            Assert.NotNull(result);
            Assert.Equal(TransactionType.Deposit, result.TransactionType);
            Assert.Equal(200m, result.Amount);
            Assert.Equal(500m, result.PreviousBalance);
            Assert.Equal(700m, result.NewBalance);

            // Verify the method was called with existing context
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                description,
                null,
                null,
                dbContext), Times.Once);
        }

        [Fact]
        public async Task RecordTransactionAsync_WithNullOptionalParameters_ShouldCallServiceCorrectly()
        {
            // Arrange
            var userId = 1;
            var transactionType = TransactionType.Sell;
            var amount = 50m;
            var previousBalance = 300m;
            var newBalance = 350m;

            var expectedTransaction = new BalanceTransaction
            {
                Id = 1,
                UserId = userId,
                TransactionType = transactionType,
                Amount = amount,
                PreviousBalance = previousBalance,
                NewBalance = newBalance,
                Description = null,
                ReferenceId = null,
                ReferenceType = null,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };

            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                null, // description
                null, // referenceId
                null, // referenceType
                null)) // existingContext
                .ReturnsAsync(expectedTransaction);

            // Act
            var result = await _mockBalanceTransactionService.Object.RecordTransactionAsync(
                userId: userId,
                transactionType: transactionType,
                amount: amount,
                previousBalance: previousBalance,
                newBalance: newBalance
                // description, referenceId, referenceType, existingContext are null/default
            );

            // Assert
            Assert.NotNull(result);
            Assert.Equal(TransactionType.Sell, result.TransactionType);
            Assert.Equal(50m, result.Amount);
            Assert.Null(result.Description);
            Assert.Null(result.ReferenceId);
            Assert.Null(result.ReferenceType);

            // Verify the method was called with null optional parameters
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                null,
                null,
                null,
                null), Times.Once);
        }

        [Theory]
        [InlineData(TransactionType.Buy)]
        [InlineData(TransactionType.Sell)]
        [InlineData(TransactionType.Deposit)]
        [InlineData(TransactionType.Withdrawal)]
        [InlineData(TransactionType.PackagePurchase)]
        [InlineData(TransactionType.ReferralReward)]
        public async Task RecordTransactionAsync_WithDifferentTransactionTypes_ShouldCallServiceCorrectly(string transactionType)
        {
            // Arrange
            var userId = 1;
            var amount = 100m;
            var previousBalance = 1000m;
            var newBalance = 900m;
            var description = $"Test {transactionType}";

            var expectedTransaction = new BalanceTransaction
            {
                Id = 1,
                UserId = userId,
                TransactionType = transactionType,
                Amount = amount,
                PreviousBalance = previousBalance,
                NewBalance = newBalance,
                Description = description,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };

            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                description,
                null,
                null,
                null))
                .ReturnsAsync(expectedTransaction);

            // Act
            var result = await _mockBalanceTransactionService.Object.RecordTransactionAsync(
                userId: userId,
                transactionType: transactionType,
                amount: amount,
                previousBalance: previousBalance,
                newBalance: newBalance,
                description: description
            );

            // Assert
            Assert.NotNull(result);
            Assert.Equal(transactionType, result.TransactionType);
            Assert.Equal($"Test {transactionType}", result.Description);

            // Verify the method was called with correct transaction type
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                description,
                null,
                null,
                null), Times.Once);
        }

        [Fact]
        public async Task RecordTransactionAsync_ShouldReturnTransactionWithCreatedDate()
        {
            // Arrange
            var userId = 1;
            var transactionType = TransactionType.Buy;
            var amount = 100m;
            var previousBalance = 1000m;
            var newBalance = 900m;
            var beforeCall = DateTime.UtcNow;

            var expectedTransaction = new BalanceTransaction
            {
                Id = 1,
                UserId = userId,
                TransactionType = transactionType,
                Amount = amount,
                PreviousBalance = previousBalance,
                NewBalance = newBalance,
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            };

            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                null,
                null,
                null,
                null))
                .ReturnsAsync(expectedTransaction);

            // Act
            var result = await _mockBalanceTransactionService.Object.RecordTransactionAsync(
                userId: userId,
                transactionType: transactionType,
                amount: amount,
                previousBalance: previousBalance,
                newBalance: newBalance
            );

            var afterCall = DateTime.UtcNow;

            // Assert
            Assert.NotNull(result);
            Assert.True(result.CreatedDate >= beforeCall);
            Assert.True(result.CreatedDate <= afterCall);

            // Verify the method was called
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                userId,
                transactionType,
                amount,
                previousBalance,
                newBalance,
                null,
                null,
                null,
                null), Times.Once);
        }
    }
}

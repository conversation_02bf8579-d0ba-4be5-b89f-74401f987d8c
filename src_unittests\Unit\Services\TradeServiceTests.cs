using Microsoft.Extensions.Localization;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Trade;
using RazeWinComTr.Tests.TestInfrastructure.Base;

namespace RazeWinComTr.Tests.Unit.Services
{
    public class TradeServiceImplementationTests : TestBase
    {
        private AppDbContext CreateIsolatedContext() => CreateDbContext(CreateUniqueDatabaseName("TradeService"));

        private TradeService CreateService(AppDbContext context)
        {
            var localizer = new Mock<IStringLocalizer<SharedResource>>();
            return new TradeService(localizer.Object, context);
        }

        private User CreateTestUser(int id = 1, string? email = null)
            => CreateUser(id, email ?? $"user{id}@example.com");

        private Market CreateTestMarket(int id = 1, string? code = null)
            => new Market { Id = id, Coin = code ?? $"COIN{id}", Name = $"Coin {id}", ShortName = $"C{id}", PairCode = $"PAIR{id}", IsActive = 1, BuyPrice = 1, SellPrice = 1 };

        private Trade CreateTestTrade(int id, int userId, int coinId, TradeType type = TradeType.Buy, bool isActive = true)
            => new Trade
            {
                Id = id,
                UserId = userId,
                CoinId = coinId,
                Type = type,
                CoinRate = 10,
                CoinAmount = 1,
                TryAmount = 10,
                PreviousCoinBalance = 0,
                NewCoinBalance = 1,
                PreviousBalance = 0,
                NewBalance = 10,
                PreviousWalletBalance = 0,
                NewWalletBalance = 1,
                CreatedDate = DateTime.UtcNow,
                IsActive = isActive
            };

        [Fact]
        public async Task CreateAsync_ShouldAddTrade()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateTestUser();
            var market = CreateTestMarket();
            context.Users.Add(user);
            context.Markets.Add(market);
            await context.SaveChangesAsync();

            var trade = CreateTestTrade(0, user.UserId, market.Id);
            var result = await service.CreateAsync(trade);
            Assert.NotNull(result);
            Assert.True(result.Id > 0);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Single(context.Trades);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnTrade_WhenExists()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateTestUser();
            var market = CreateTestMarket();
            context.Users.Add(user);
            context.Markets.Add(market);
            var trade = CreateTestTrade(0, user.UserId, market.Id);
            context.Trades.Add(trade);
            await context.SaveChangesAsync();

            var found = await service.GetByIdAsync(trade.Id);
            Assert.NotNull(found);
            Assert.Equal(trade.Id, found!.Id);
        }

        [Fact]
        public async Task GetListAsync_ShouldReturnAllTrades()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateTestUser();
            var market = CreateTestMarket();
            context.Users.Add(user);
            context.Markets.Add(market);
            context.Trades.AddRange(
                CreateTestTrade(0, user.UserId, market.Id, TradeType.Buy),
                CreateTestTrade(0, user.UserId, market.Id, TradeType.Sell)
            );
            await context.SaveChangesAsync();

            var all = await service.GetListAsync();
            Assert.Equal(2, all.Count);
        }

        [Fact]
        public async Task GetByUserIdAsync_ShouldReturnOnlyActiveForUser()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateTestUser();
            var market = CreateTestMarket();
            context.Users.Add(user);
            context.Markets.Add(market);
            context.Trades.AddRange(
                CreateTestTrade(0, user.UserId, market.Id, TradeType.Buy, true),
                CreateTestTrade(0, user.UserId, market.Id, TradeType.Sell, false)
            );
            await context.SaveChangesAsync();

            var result = await service.GetByUserIdAsync(user.UserId);
            Assert.Single(result);
            Assert.Equal(TradeType.Buy, result[0].Type);
        }

        [Fact]
        public async Task DeleteAsync_ShouldRemoveTrade()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateTestUser();
            var market = CreateTestMarket();
            context.Users.Add(user);
            context.Markets.Add(market);
            var trade = CreateTestTrade(0, user.UserId, market.Id);
            context.Trades.Add(trade);
            await context.SaveChangesAsync();

            await service.DeleteAsync(trade.Id);
            var deleted = await context.Trades.FindAsync(trade.Id);
            Assert.Null(deleted);
        }

        [Fact]
        public async Task DeleteAsync_ShouldDoNothing_IfNotFound()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            await service.DeleteAsync(9999); // Should not throw
        }

        [Fact]
        public async Task UpdateAsync_ShouldModifyTrade()
        {
            using var context = CreateIsolatedContext();
            var service = CreateService(context);
            var user = CreateTestUser();
            var market = CreateTestMarket();
            context.Users.Add(user);
            context.Markets.Add(market);
            var trade = CreateTestTrade(0, user.UserId, market.Id);
            context.Trades.Add(trade);
            await context.SaveChangesAsync();

            trade.CoinAmount = 99;
            await service.UpdateAsync(trade);
            var updated = await context.Trades.FindAsync(trade.Id);
            Assert.Equal(99, updated!.CoinAmount);
        }
    }

    public class TradeServiceInterfaceTests : TestBase
    {
        private readonly Mock<ITradeService> _mockTradeService;
        public TradeServiceInterfaceTests()
        {
            _mockTradeService = new Mock<ITradeService>(MockBehavior.Strict);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldCallInterface()
        {
            var trade = new Trade { Id = 1 };
            _mockTradeService.Setup(x => x.GetByIdAsync(1)).ReturnsAsync(trade);
            var result = await _mockTradeService.Object.GetByIdAsync(1);
            Assert.Equal(1, result!.Id);
            _mockTradeService.Verify(x => x.GetByIdAsync(1), Times.Once);
        }

        [Fact]
        public async Task GetListAsync_ShouldCallInterface()
        {
            var list = new List<TradeViewModel> { new TradeViewModel { Id = 1 }, new TradeViewModel { Id = 2 } };
            _mockTradeService.Setup(x => x.GetListAsync()).ReturnsAsync(list);
            var result = await _mockTradeService.Object.GetListAsync();
            Assert.Equal(2, result.Count);
            _mockTradeService.Verify(x => x.GetListAsync(), Times.Once);
        }

        [Fact]
        public async Task GetByUserIdAsync_ShouldCallInterface()
        {
            var list = new List<TradeViewModel> { new TradeViewModel { Id = 1, UserId = 1 } };
            _mockTradeService.Setup(x => x.GetByUserIdAsync(1)).ReturnsAsync(list);
            var result = await _mockTradeService.Object.GetByUserIdAsync(1);
            Assert.Single(result);
            Assert.Equal(1, result[0].UserId);
            _mockTradeService.Verify(x => x.GetByUserIdAsync(1), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_ShouldCallInterface()
        {
            _mockTradeService.Setup(x => x.DeleteAsync(1)).Returns(Task.CompletedTask);
            await _mockTradeService.Object.DeleteAsync(1);
            _mockTradeService.Verify(x => x.DeleteAsync(1), Times.Once);
        }

        [Fact]
        public async Task CreateAsync_ShouldCallInterface()
        {
            var trade = new Trade { Id = 1 };
            _mockTradeService.Setup(x => x.CreateAsync(trade, null)).ReturnsAsync(trade);
            var result = await _mockTradeService.Object.CreateAsync(trade, null);
            Assert.Equal(1, result.Id);
            _mockTradeService.Verify(x => x.CreateAsync(trade, null), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldCallInterface()
        {
            var trade = new Trade { Id = 1 };
            _mockTradeService.Setup(x => x.UpdateAsync(trade, null)).Returns(Task.CompletedTask);
            await _mockTradeService.Object.UpdateAsync(trade, null);
            _mockTradeService.Verify(x => x.UpdateAsync(trade, null), Times.Once);
        }
    }
}

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using Moq;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.Common;
using RazeWinComTr.Tests.TestInfrastructure.Base;
using RazeWinComTr.Tests.TestInfrastructure.Utilities;
using Xunit;

namespace RazeWinComTr.Tests.Unit.Services
{
    public class UserPackageServiceTests : TestBase
    {
        private readonly Mock<IStringLocalizer<SharedResource>> _localizerMock;
        private readonly Mock<ILogger<UserPackageService>> _loggerMock;
        private readonly Mock<IBalanceTransactionService> _mockBalanceTransactionService;

        public UserPackageServiceTests()
        {
            _localizerMock = new Mock<IStringLocalizer<SharedResource>>();
            _loggerMock = new Mock<ILogger<UserPackageService>>();
            _mockBalanceTransactionService = new Mock<IBalanceTransactionService>(MockBehavior.Strict);

            // Setup localizer
            _localizerMock.Setup(l => l[It.IsAny<string>()])
                .Returns<string>(s => new LocalizedString(s, s));
            _localizerMock.Setup(l => l[It.IsAny<string>(), It.IsAny<object[]>()])
                .Returns<string, object[]>((s, args) => new LocalizedString(s, string.Format(s, args)));
        }

        [Fact]
        public async Task GetByIdAsync_WithValidId_ReturnsUserPackage()
        {
            // Arrange
            var dbContext = CreateDbContext("GetByIdAsync_WithValidId_ReturnsUserPackage");
            var user = TestDataGenerator.CreateUser(1);
            var package = new Package
            {
                Id = 1,
                Name = "Test Package",
                Price = 1000m,
                IsActive = true
            };

            dbContext.Users.Add(user);
            dbContext.Packages.Add(package);

            var userPackage = new UserPackage
            {
                Id = 1,
                UserId = user.UserId,
                PackageId = package.Id,
                Status = UserPackageStatus.Active,
                PurchaseDate = DateTime.UtcNow
            };
            dbContext.UserPackages.Add(userPackage);
            await dbContext.SaveChangesAsync();

            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(5.67m, 2);
            var mockTradeService = CreateMockTradeService();

            var userPackageService = new UserPackageService(
                dbContext,
                _localizerMock.Object,
                _loggerMock.Object,
                _mockBalanceTransactionService.Object,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockTradeService.Object
            );

            // Act
            var result = await userPackageService.GetByIdAsync(1);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.Id);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Equal(package.Id, result.PackageId);
            Assert.Equal(UserPackageStatus.Active, result.Status);
        }

        [Fact]
        public async Task GetByIdAsync_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var dbContext = CreateDbContext("GetByIdAsync_WithInvalidId_ReturnsNull");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(5.67m, 2);
            var mockTradeService = CreateMockTradeService();

            var userPackageService = new UserPackageService(
                dbContext,
                _localizerMock.Object,
                _loggerMock.Object,
                _mockBalanceTransactionService.Object,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockTradeService.Object
            );

            // Act
            var result = await userPackageService.GetByIdAsync(999);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetActivePackageForUserAsync_WithActivePackage_ReturnsPackage()
        {
            // Arrange
            var dbContext = CreateDbContext("GetActivePackageByUserIdAsync_WithActivePackage_ReturnsPackage");
            var user = TestDataGenerator.CreateUser(1);
            var package = new Package
            {
                Id = 1,
                Name = "Active Package",
                Price = 1000m,
                IsActive = true
            };

            dbContext.Users.Add(user);
            dbContext.Packages.Add(package);

            var userPackage = new UserPackage
            {
                Id = 1,
                UserId = user.UserId,
                PackageId = package.Id,
                Status = UserPackageStatus.Active,
                PurchaseDate = DateTime.UtcNow
            };
            dbContext.UserPackages.Add(userPackage);
            await dbContext.SaveChangesAsync();

            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(5.67m, 2);
            var mockTradeService = CreateMockTradeService();

            var userPackageService = new UserPackageService(
                dbContext,
                _localizerMock.Object,
                _loggerMock.Object,
                _mockBalanceTransactionService.Object,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockTradeService.Object
            );

            // Act
            var result = await userPackageService.GetActivePackageForUserAsync(user.UserId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.Id);
            Assert.Equal(UserPackageStatus.Active, result.Status);
        }

        [Fact]
        public async Task GetActivePackageForUserAsync_WithNoActivePackage_ReturnsNull()
        {
            // Arrange
            var dbContext = CreateDbContext("GetActivePackageByUserIdAsync_WithNoActivePackage_ReturnsNull");
            var user = TestDataGenerator.CreateUser(1);
            dbContext.Users.Add(user);
            await dbContext.SaveChangesAsync();

            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(5.67m, 2);
            var mockTradeService = CreateMockTradeService();

            var userPackageService = new UserPackageService(
                dbContext,
                _localizerMock.Object,
                _loggerMock.Object,
                _mockBalanceTransactionService.Object,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockTradeService.Object
            );

            // Act
            var result = await userPackageService.GetActivePackageForUserAsync(user.UserId);

            // Assert
            Assert.Null(result);
        }

        /// <summary>
        /// Tests that package purchase correctly updates user balance and calls RecordTransactionAsync
        /// with the new refactored parameters.
        /// </summary>
        [Fact]
        public async Task PurchasePackageAsync_UpdatesBalanceAndCallsRecordTransactionAsync()
        {
            // Arrange
            var dbContext = CreateDbContext("PurchasePackageAsync_UpdatesBalanceAndCallsRecordTransactionAsync");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(5.67m, 2);
            var mockTradeService = CreateMockTradeService();

            // Create test user with sufficient balance
            var user = TestDataGenerator.CreateUser(1);
            user.Balance = 50000m;
            dbContext.Users.Add(user);

            // Create test package
            var package = new Package
            {
                Id = 1,
                Name = "Gold Package",
                Price = 10000m,
                IsActive = true
            };
            dbContext.Packages.Add(package);
            await dbContext.SaveChangesAsync();

            // Setup mock to return a balance transaction
            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<string>(),
                It.IsAny<int?>(),
                It.IsAny<string>(),
                It.IsAny<AppDbContext>()))
                .ReturnsAsync(new BalanceTransaction { Id = 1 });

            var userPackageService = new UserPackageService(
                dbContext,
                _localizerMock.Object,
                _loggerMock.Object,
                _mockBalanceTransactionService.Object,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockTradeService.Object
            );

            // Act
            var result = await userPackageService.PurchasePackageAsync(user.UserId, package.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Equal(package.Id, result.PackageId);

            // Verify user balance was updated
            var updatedUser = await dbContext.Users.FindAsync(user.UserId);
            Assert.NotNull(updatedUser);
            Assert.Equal(40000m, updatedUser.Balance); // 50000 - 10000 = 40000

            // Verify RecordTransactionAsync was called with correct parameters
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                user.UserId, // userId
                TransactionType.PackagePurchase, // transactionType
                10000m, // amount
                50000m, // previousBalance
                40000m, // newBalance
                It.IsAny<string>(), // description
                package.Id, // referenceId
                BalanceTransactionReferenceTypes.Package, // referenceType
                dbContext // existingContext
            ), Times.Once);
        }

        /// <summary>
        /// Tests that package purchase fails when user has insufficient balance.
        /// </summary>
        [Fact]
        public async Task PurchasePackageAsync_WithInsufficientBalance_ThrowsException()
        {
            // Arrange
            var dbContext = CreateDbContext("PurchasePackageAsync_WithInsufficientBalance_ThrowsException");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(5.67m, 2);
            var mockTradeService = CreateMockTradeService();

            // Create test user with insufficient balance
            var user = TestDataGenerator.CreateUser(1);
            user.Balance = 5000m; // Less than package price
            dbContext.Users.Add(user);

            // Create test package
            var package = new Package
            {
                Id = 1,
                Name = "Gold Package",
                Price = 10000m, // More than user's balance
                IsActive = true
            };
            dbContext.Packages.Add(package);
            await dbContext.SaveChangesAsync();

            var userPackageService = new UserPackageService(
                dbContext,
                _localizerMock.Object,
                _loggerMock.Object,
                _mockBalanceTransactionService.Object,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockTradeService.Object
            );

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(
                () => userPackageService.PurchasePackageAsync(user.UserId, package.Id));

            Assert.Contains("Insufficient balance", exception.Message);

            // Verify user balance was not changed
            var userAfter = await dbContext.Users.FindAsync(user.UserId);
            Assert.NotNull(userAfter);
            Assert.Equal(5000m, userAfter.Balance);

            // Verify RecordTransactionAsync was not called
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<string>(),
                It.IsAny<int?>(),
                It.IsAny<string>(),
                It.IsAny<AppDbContext>()), Times.Never);
        }

        /// <summary>
        /// Tests that when a user purchases a new package, their previous active packages
        /// are deactivated and balance is correctly updated.
        /// </summary>
        [Fact]
        public async Task PurchasePackageAsync_DeactivatesPreviousPackagesAndUpdatesBalance()
        {
            // Arrange
            var dbContext = CreateDbContext("PurchasePackageAsync_DeactivatesPreviousPackagesAndUpdatesBalance");
            var mockWalletService = CreateMockWalletService();
            var mockTokenPriceService = CreateMockTokenPriceService(5.67m, 2);
            var mockTradeService = CreateMockTradeService();

            // Create test user
            var user = TestDataGenerator.CreateUser(1);
            user.Balance = 50000m;
            dbContext.Users.Add(user);

            // Create test packages
            var bronzePackage = new Package
            {
                Id = 1,
                Name = "Bronze Package",
                Price = 5000m,
                IsActive = true
            };

            var goldPackage = new Package
            {
                Id = 2,
                Name = "Gold Package",
                Price = 15000m,
                IsActive = true
            };

            dbContext.Packages.AddRange(bronzePackage, goldPackage);

            // Create existing active user package
            var existingUserPackage = new UserPackage
            {
                UserId = user.UserId,
                PackageId = bronzePackage.Id,
                Status = UserPackageStatus.Active,
                PurchaseDate = DateTime.UtcNow.AddDays(-10)
            };
            dbContext.UserPackages.Add(existingUserPackage);
            await dbContext.SaveChangesAsync();

            // Setup mock to return a balance transaction
            _mockBalanceTransactionService.Setup(x => x.RecordTransactionAsync(
                It.IsAny<int>(),
                It.IsAny<string>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<decimal>(),
                It.IsAny<string>(),
                It.IsAny<int?>(),
                It.IsAny<string>(),
                It.IsAny<AppDbContext>()))
                .ReturnsAsync(new BalanceTransaction { Id = 1 });

            var userPackageService = new UserPackageService(
                dbContext,
                _localizerMock.Object,
                _loggerMock.Object,
                _mockBalanceTransactionService.Object,
                mockWalletService.Object,
                mockTokenPriceService.Object,
                mockTradeService.Object
            );

            // Act
            var result = await userPackageService.PurchasePackageAsync(user.UserId, goldPackage.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(goldPackage.Id, result.PackageId);
            Assert.Equal(UserPackageStatus.Active, result.Status);

            // Verify previous package was deactivated
            var updatedExistingPackage = await dbContext.UserPackages.FindAsync(existingUserPackage.Id);
            Assert.NotNull(updatedExistingPackage);
            Assert.Equal(UserPackageStatus.Cancelled, updatedExistingPackage.Status);

            // Verify user balance was updated correctly
            var updatedUser = await dbContext.Users.FindAsync(user.UserId);
            Assert.NotNull(updatedUser);
            Assert.Equal(35000m, updatedUser.Balance); // 50000 - 15000 = 35000

            // Verify RecordTransactionAsync was called for the new package purchase
            _mockBalanceTransactionService.Verify(x => x.RecordTransactionAsync(
                user.UserId,
                TransactionType.PackagePurchase,
                15000m, // Gold package price
                50000m, // previousBalance
                35000m, // newBalance
                It.IsAny<string>(),
                goldPackage.Id,
                BalanceTransactionReferenceTypes.Package,
                dbContext
            ), Times.Once);
        }
    }
}

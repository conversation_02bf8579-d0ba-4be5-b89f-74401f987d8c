using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels.RzwSavings;

namespace RazeWinComTr.Areas.Admin.Pages.RzwSavingsPlans;

public class IndexModel : PageModel
{
    private readonly IRzwSavingsPlanService _planService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public IndexModel(IRzwSavingsPlanService planService, IStringLocalizer<SharedResource> localizer)
    {
        _planService = planService;
        _localizer = localizer;
    }

    public List<RzwSavingsPlanViewModel> Plans { get; set; } = new();
    public IStringLocalizer<SharedResource> Localizer => _localizer;

    public async Task OnGetAsync()
    {
        var plans = await _planService.GetAllPlansAsync();
        Plans = plans.Select(p => RzwSavingsPlanViewModel.FromEntity(p)).ToList();

        // Get statistics for each plan
        foreach (var plan in Plans)
        {
            var stats = await _planService.GetPlanStatisticsAsync(plan.Id);
            plan.TotalAccounts = stats.TotalAccounts;
            plan.TotalLockedRzw = stats.TotalLockedRzw;
            plan.TotalInterestPaid = stats.TotalInterestPaid;
        }
    }
}

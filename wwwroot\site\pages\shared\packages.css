/* Common Package Display Styling */
/* Packages Page Styles */
.packages-container {
    margin-top: 30px;
}

.package-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #e0e0e0;
}

.package-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.package-card.current-package {
    border: 2px solid #28a745;
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.2);
}

.package-card.current-package:hover {
    box-shadow: 0 15px 35px rgba(40, 167, 69, 0.3);
}

.current-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #28a745;
    color: white;
    padding: 3px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    z-index: 1;
}

.package-header {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: white;
    padding: 20px;
    text-align: center;
    position: relative;
}

.package-header h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
}

.package-price {
    margin-top: 15px;
    font-size: 18px;
}

.package-price .currency {
    font-size: 20px;
    vertical-align: top;
    margin-right: 2px;
}

.package-price .amount {
    font-size: 36px;
    font-weight: 700;
}

.package-body {
    padding: 20px;
    flex-grow: 1;
}

.package-description {
    color: #555;
    margin-bottom: 20px;
    font-size: 14px;
}

.package-features {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

.package-features li {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    font-size: 14px;
}

.package-features li:last-child {
    border-bottom: none;
}

.package-features li i {
    color: #28a745;
    margin-right: 10px;
}

.package-limits {
    margin-top: 15px;
    font-size: 14px;
    color: #666;
}

.package-limits .rzw-bonus {
    margin-top: 10px;
    padding: 8px;
    background-color: #f0f8ff;
    border-left: 3px solid #0f3460;
    border-radius: 4px;
    color: #0f3460;
    font-weight: 500;
}

.reward-percentages {
    margin-top: 15px;
    padding: 10px;
    background-color: #f0f8ff;
    border-radius: 5px;
    border: 1px solid #d1e7ff;
}

.reward-percentages p {
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 13px;
    color: #0f3460;
}

.reward-list {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
}

.reward-list li {
    padding: 4px 0;
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px dashed #d1e7ff;
    color: #555;
}

.reward-list li:last-child {
    border-bottom: none;
}

.reward-list .percentage {
    font-weight: 600;
    color: #0f3460;
    background-color: #e6f3ff;
    padding: 2px 6px;
    border-radius: 3px;
}

.package-footer {
    padding: 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
}

.btn-primary {
    background-color: #0f3460;
    border-color: #0f3460;
}

.btn-primary:hover {
    background-color: #16213e;
    border-color: #16213e;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

.btn-block {
    display: block;
    width: 100%;
}

/* Responsive styles */
@media (max-width: 992px) {
    .package-card {
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .package-header h3 {
        font-size: 20px;
    }

    .package-price .amount {
        font-size: 30px;
    }

    .package-description {
        font-size: 13px;
    }

    .package-features li {
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .package-header {
        padding: 15px;
    }

    .package-body {
        padding: 15px;
    }

    .package-footer {
        padding: 15px;
    }
}
